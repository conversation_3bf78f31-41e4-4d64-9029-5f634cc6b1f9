@anchor{codec-options}
@chapter Codec Options
@c man begin CODEC OPTIONS

libavcodec provides some generic global options, which can be set on
all the encoders and decoders. In addition, each codec may support
so-called private options, which are specific for a given codec.

Sometimes, a global option may only affect a specific kind of codec,
and may be nonsensical or ignored by another, so you need to be aware
of the meaning of the specified options. Also some options are
meant only for decoding or encoding.

Options may be set by specifying -@var{option} @var{value} in the
FFmpeg tools, or by setting the value explicitly in the
@code{AVCodecContext} options or using the @file{libavutil/opt.h} API
for programmatic use.

The list of supported options follow:

@table @option
@item b @var{integer} (@emph{encoding,audio,video})
Set bitrate in bits/s. Default value is 200K.

@item ab @var{integer} (@emph{encoding,audio})
Set audio bitrate (in bits/s). Default value is 128K.

@item bt @var{integer} (@emph{encoding,video})
Set video bitrate tolerance (in bits/s). In 1-pass mode, bitrate
tolerance specifies how far ratecontrol is willing to deviate from the
target average bitrate value. This is not related to min/max
bitrate. Lowering tolerance too much has an adverse effect on quality.

@item flags @var{flags} (@emph{decoding/encoding,audio,video,subtitles})
Set generic flags.

Possible values:
@table @samp
@item mv4
Use four motion vector by macroblock (mpeg4).
@item qpel
Use 1/4 pel motion compensation.
@item loop
Use loop filter.
@item qscale
Use fixed qscale.
@item pass1
Use internal 2pass ratecontrol in first pass mode.
@item pass2
Use internal 2pass ratecontrol in second pass mode.
@item gray
Only decode/encode grayscale.
@item psnr
Set error[?] variables during encoding.
@item truncated
Input bitstream might be randomly truncated.
@item drop_changed
Don't output frames whose parameters differ from first decoded frame in stream.
Error AVERROR_INPUT_CHANGED is returned when a frame is dropped.

@item ildct
Use interlaced DCT.
@item low_delay
Force low delay.
@item global_header
Place global headers in extradata instead of every keyframe.
@item bitexact
Only write platform-, build- and time-independent data. (except (I)DCT).
This ensures that file and data checksums are reproducible and match between
platforms. Its primary use is for regression testing.
@item aic
Apply H263 advanced intra coding / mpeg4 ac prediction.
@item ilme
Apply interlaced motion estimation.
@item cgop
Use closed gop.
@item output_corrupt
Output even potentially corrupted frames.
@end table

@item time_base @var{rational number}
Set codec time base.

It is the fundamental unit of time (in seconds) in terms of which
frame timestamps are represented. For fixed-fps content, timebase
should be @code{1 / frame_rate} and timestamp increments should be
identically 1.

@item g @var{integer} (@emph{encoding,video})
Set the group of picture (GOP) size. Default value is 12.

@item ar @var{integer} (@emph{decoding/encoding,audio})
Set audio sampling rate (in Hz).

@item ac @var{integer} (@emph{decoding/encoding,audio})
Set number of audio channels.

@item cutoff @var{integer} (@emph{encoding,audio})
Set cutoff bandwidth. (Supported only by selected encoders, see
their respective documentation sections.)

@item frame_size @var{integer} (@emph{encoding,audio})
Set audio frame size.

Each submitted frame except the last must contain exactly frame_size
samples per channel. May be 0 when the codec has
CODEC_CAP_VARIABLE_FRAME_SIZE set, in that case the frame size is not
restricted. It is set by some decoders to indicate constant frame
size.

@item frame_number @var{integer}
Set the frame number.

@item delay @var{integer}

@item qcomp @var{float} (@emph{encoding,video})
Set video quantizer scale compression (VBR). It is used as a constant
in the ratecontrol equation. Recommended range for default rc_eq:
0.0-1.0.

@item qblur @var{float} (@emph{encoding,video})
Set video quantizer scale blur (VBR).

@item qmin @var{integer} (@emph{encoding,video})
Set min video quantizer scale (VBR). Must be included between -1 and
69, default value is 2.

@item qmax @var{integer} (@emph{encoding,video})
Set max video quantizer scale (VBR). Must be included between -1 and
1024, default value is 31.

@item qdiff @var{integer} (@emph{encoding,video})
Set max difference between the quantizer scale (VBR).

@item bf @var{integer} (@emph{encoding,video})
Set max number of B frames between non-B-frames.

Must be an integer between -1 and 16. 0 means that B-frames are
disabled. If a value of -1 is used, it will choose an automatic value
depending on the encoder.

Default value is 0.

@item b_qfactor @var{float} (@emph{encoding,video})
Set qp factor between P and B frames.

@item codec_tag @var{integer}
@item bug @var{flags} (@emph{decoding,video})
Workaround not auto detected encoder bugs.

Possible values:
@table @samp
@item autodetect

@item xvid_ilace
Xvid interlacing bug (autodetected if fourcc==XVIX)
@item ump4
(autodetected if fourcc==UMP4)
@item no_padding
padding bug (autodetected)
@item amv

@item qpel_chroma

@item std_qpel
old standard qpel (autodetected per fourcc/version)
@item qpel_chroma2

@item direct_blocksize
direct-qpel-blocksize bug (autodetected per fourcc/version)
@item edge
edge padding bug (autodetected per fourcc/version)
@item hpel_chroma

@item dc_clip

@item ms
Workaround various bugs in microsoft broken decoders.
@item trunc
trancated frames
@end table

@item strict @var{integer} (@emph{decoding/encoding,audio,video})
Specify how strictly to follow the standards.

Possible values:
@table @samp
@item very
strictly conform to an older more strict version of the spec or reference software
@item strict
strictly conform to all the things in the spec no matter what consequences
@item normal

@item unofficial
allow unofficial extensions
@item experimental
allow non standardized experimental things, experimental
(unfinished/work in progress/not well tested) decoders and encoders.
Note: experimental decoders can pose a security risk, do not use this for
decoding untrusted input.
@end table

@item b_qoffset @var{float} (@emph{encoding,video})
Set QP offset between P and B frames.

@item err_detect @var{flags} (@emph{decoding,audio,video})
Set error detection flags.

Possible values:
@table @samp
@item crccheck
verify embedded CRCs
@item bitstream
detect bitstream specification deviations
@item buffer
detect improper bitstream length
@item explode
abort decoding on minor error detection
@item ignore_err
ignore decoding errors, and continue decoding.
This is useful if you want to analyze the content of a video and thus want
everything to be decoded no matter what. This option will not result in a video
that is pleasing to watch in case of errors.
@item careful
consider things that violate the spec and have not been seen in the wild as errors
@item compliant
consider all spec non compliancies as errors
@item aggressive
consider things that a sane encoder should not do as an error
@end table

@item has_b_frames @var{integer}

@item block_align @var{integer}

@item rc_override_count @var{integer}

@item maxrate @var{integer} (@emph{encoding,audio,video})
Set max bitrate tolerance (in bits/s). Requires bufsize to be set.

@item minrate @var{integer} (@emph{encoding,audio,video})
Set min bitrate tolerance (in bits/s). Most useful in setting up a CBR
encode. It is of little use elsewise.

@item bufsize @var{integer} (@emph{encoding,audio,video})
Set ratecontrol buffer size (in bits).

@item i_qfactor @var{float} (@emph{encoding,video})
Set QP factor between P and I frames.

@item i_qoffset @var{float} (@emph{encoding,video})
Set QP offset between P and I frames.

@item dct @var{integer} (@emph{encoding,video})
Set DCT algorithm.

Possible values:
@table @samp
@item auto
autoselect a good one (default)
@item fastint
fast integer
@item int
accurate integer
@item mmx

@item altivec

@item faan
floating point AAN DCT
@end table

@item lumi_mask @var{float} (@emph{encoding,video})
Compress bright areas stronger than medium ones.

@item tcplx_mask @var{float} (@emph{encoding,video})
Set temporal complexity masking.

@item scplx_mask @var{float} (@emph{encoding,video})
Set spatial complexity masking.

@item p_mask @var{float} (@emph{encoding,video})
Set inter masking.

@item dark_mask @var{float} (@emph{encoding,video})
Compress dark areas stronger than medium ones.

@item idct @var{integer} (@emph{decoding/encoding,video})
Select IDCT implementation.

Possible values:
@table @samp
@item auto

@item int

@item simple

@item simplemmx

@item simpleauto
Automatically pick a IDCT compatible with the simple one

@item arm

@item altivec

@item sh4

@item simplearm

@item simplearmv5te

@item simplearmv6

@item simpleneon

@item xvid

@item faani
floating point AAN IDCT
@end table

@item slice_count @var{integer}

@item ec @var{flags} (@emph{decoding,video})
Set error concealment strategy.

Possible values:
@table @samp
@item guess_mvs
iterative motion vector (MV) search (slow)
@item deblock
use strong deblock filter for damaged MBs
@item favor_inter
favor predicting from the previous frame instead of the current
@end table

@item bits_per_coded_sample @var{integer}

@item aspect @var{rational number} (@emph{encoding,video})
Set sample aspect ratio.

@item sar @var{rational number} (@emph{encoding,video})
Set sample aspect ratio. Alias to @var{aspect}.

@item debug @var{flags} (@emph{decoding/encoding,audio,video,subtitles})
Print specific debug info.

Possible values:
@table @samp
@item pict
picture info
@item rc
rate control
@item bitstream

@item mb_type
macroblock (MB) type
@item qp
per-block quantization parameter (QP)
@item dct_coeff

@item green_metadata
display complexity metadata for the upcoming frame, GoP or for a given duration.

@item skip

@item startcode

@item er
error recognition
@item mmco
memory management control operations (H.264)
@item bugs

@item buffers
picture buffer allocations
@item thread_ops
threading operations
@item nomc
skip motion compensation
@end table

@item cmp @var{integer} (@emph{encoding,video})
Set full pel me compare function.

Possible values:
@table @samp
@item sad
sum of absolute differences, fast (default)
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@end table

@item subcmp @var{integer} (@emph{encoding,video})
Set sub pel me compare function.

Possible values:
@table @samp
@item sad
sum of absolute differences, fast (default)
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@end table

@item mbcmp @var{integer} (@emph{encoding,video})
Set macroblock compare function.

Possible values:
@table @samp
@item sad
sum of absolute differences, fast (default)
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@end table

@item ildctcmp @var{integer} (@emph{encoding,video})
Set interlaced dct compare function.

Possible values:
@table @samp
@item sad
sum of absolute differences, fast (default)
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@end table

@item dia_size @var{integer} (@emph{encoding,video})
Set diamond type & size for motion estimation.
@table @samp
@item (1024, INT_MAX)
full motion estimation(slowest)
@item (768, 1024]
umh motion estimation
@item (512, 768]
hex motion estimation
@item (256, 512]
l2s diamond motion estimation
@item [2,256]
var diamond motion estimation
@item (-1,  2)
small diamond motion estimation
@item -1
funny diamond motion estimation
@item (INT_MIN, -1)
sab diamond motion estimation
@end table

@item last_pred @var{integer} (@emph{encoding,video})
Set amount of motion predictors from the previous frame.

@item precmp @var{integer} (@emph{encoding,video})
Set pre motion estimation compare function.

Possible values:
@table @samp
@item sad
sum of absolute differences, fast (default)
@item sse
sum of squared errors
@item satd
sum of absolute Hadamard transformed differences
@item dct
sum of absolute DCT transformed differences
@item psnr
sum of squared quantization errors (avoid, low quality)
@item bit
number of bits needed for the block
@item rd
rate distortion optimal, slow
@item zero
0
@item vsad
sum of absolute vertical differences
@item vsse
sum of squared vertical differences
@item nsse
noise preserving sum of squared differences
@item w53
5/3 wavelet, only used in snow
@item w97
9/7 wavelet, only used in snow
@item dctmax

@item chroma

@end table

@item pre_dia_size @var{integer} (@emph{encoding,video})
Set diamond type & size for motion estimation pre-pass.

@item subq @var{integer} (@emph{encoding,video})
Set sub pel motion estimation quality.

@item me_range @var{integer} (@emph{encoding,video})
Set limit motion vectors range (1023 for DivX player).

@item global_quality @var{integer} (@emph{encoding,audio,video})

@item slice_flags @var{integer}

@item mbd @var{integer} (@emph{encoding,video})
Set macroblock decision algorithm (high quality mode).

Possible values:
@table @samp
@item simple
use mbcmp (default)
@item bits
use fewest bits
@item rd
use best rate distortion
@end table

@item rc_init_occupancy @var{integer} (@emph{encoding,video})
Set number of bits which should be loaded into the rc buffer before
decoding starts.

@item flags2 @var{flags} (@emph{decoding/encoding,audio,video,subtitles})

Possible values:
@table @samp
@item fast
Allow non spec compliant speedup tricks.
@item noout
Skip bitstream encoding.
@item ignorecrop
Ignore cropping information from sps.
@item local_header
Place global headers at every keyframe instead of in extradata.
@item chunks
Frame data might be split into multiple chunks.
@item showall
Show all frames before the first keyframe.
@item export_mvs
Export motion vectors into frame side-data (see @code{AV_FRAME_DATA_MOTION_VECTORS})
for codecs that support it. See also @file{doc/examples/export_mvs.c}.
@item skip_manual
Do not skip samples and export skip information as frame side data.
@item ass_ro_flush_noop
Do not reset ASS ReadOrder field on flush.
@item icc_profiles
Generate/parse embedded ICC profiles from/to colorimetry tags.
@end table

@item export_side_data @var{flags} (@emph{decoding/encoding,audio,video,subtitles})

Possible values:
@table @samp
@item mvs
Export motion vectors into frame side-data (see @code{AV_FRAME_DATA_MOTION_VECTORS})
for codecs that support it. See also @file{doc/examples/export_mvs.c}.
@item prft
Export encoder Producer Reference Time into packet side-data (see @code{AV_PKT_DATA_PRFT})
for codecs that support it.
@item venc_params
Export video encoding parameters through frame side data (see @code{AV_FRAME_DATA_VIDEO_ENC_PARAMS})
for codecs that support it. At present, those are H.264 and VP9.
@item film_grain
Export film grain parameters through frame side data (see @code{AV_FRAME_DATA_FILM_GRAIN_PARAMS}).
Supported at present by AV1 decoders.
@end table

@item threads @var{integer} (@emph{decoding/encoding,video})
Set the number of threads to be used, in case the selected codec
implementation supports multi-threading.

Possible values:
@table @samp
@item auto, 0
automatically select the number of threads to set
@end table

Default value is @samp{auto}.

@item dc @var{integer} (@emph{encoding,video})
Set intra_dc_precision.

@item nssew @var{integer} (@emph{encoding,video})
Set nsse weight.

@item skip_top @var{integer} (@emph{decoding,video})
Set number of macroblock rows at the top which are skipped.

@item skip_bottom @var{integer} (@emph{decoding,video})
Set number of macroblock rows at the bottom which are skipped.

@item profile @var{integer} (@emph{encoding,audio,video})

Set encoder codec profile. Default value is @samp{unknown}. Encoder specific
profiles are documented in the relevant encoder documentation.

@item level @var{integer} (@emph{encoding,audio,video})

Set the encoder level. This level depends on the specific codec, and
might correspond to the profile level. It is set by default to
@samp{unknown}.

Possible values:
@table @samp
@item unknown
@end table

@item lowres @var{integer} (@emph{decoding,audio,video})
Decode at 1= 1/2, 2=1/4, 3=1/8 resolutions.

@item mblmin @var{integer} (@emph{encoding,video})
Set min macroblock lagrange factor (VBR).

@item mblmax @var{integer} (@emph{encoding,video})
Set max macroblock lagrange factor (VBR).

@item skip_loop_filter @var{integer} (@emph{decoding,video})
@item skip_idct        @var{integer} (@emph{decoding,video})
@item skip_frame       @var{integer} (@emph{decoding,video})

Make decoder discard processing depending on the frame type selected
by the option value.

@option{skip_loop_filter} skips frame loop filtering, @option{skip_idct}
skips frame IDCT/dequantization, @option{skip_frame} skips decoding.

Possible values:
@table @samp
@item none
Discard no frame.

@item default
Discard useless frames like 0-sized frames.

@item noref
Discard all non-reference frames.

@item bidir
Discard all bidirectional frames.

@item nokey
Discard all frames excepts keyframes.

@item nointra
Discard all frames except I frames.

@item all
Discard all frames.
@end table

Default value is @samp{default}.

@item bidir_refine @var{integer} (@emph{encoding,video})
Refine the two motion vectors used in bidirectional macroblocks.

@item keyint_min @var{integer} (@emph{encoding,video})
Set minimum interval between IDR-frames.

@item refs @var{integer} (@emph{encoding,video})
Set reference frames to consider for motion compensation.

@item trellis @var{integer} (@emph{encoding,audio,video})
Set rate-distortion optimal quantization.

@item mv0_threshold @var{integer} (@emph{encoding,video})
@item compression_level @var{integer} (@emph{encoding,audio,video})
@item bits_per_raw_sample @var{integer}
@item channel_layout @var{integer} (@emph{decoding/encoding,audio})
See @ref{channel layout syntax,,the Channel Layout section in the ffmpeg-utils(1) manual,ffmpeg-utils}
for the required syntax.

@item rc_max_vbv_use @var{float} (@emph{encoding,video})
@item rc_min_vbv_use @var{float} (@emph{encoding,video})

@item color_primaries @var{integer} (@emph{decoding/encoding,video})
Possible values:
@table @samp
@item bt709
BT.709
@item bt470m
BT.470 M
@item bt470bg
BT.470 BG
@item smpte170m
SMPTE 170 M
@item smpte240m
SMPTE 240 M
@item film
Film
@item bt2020
BT.2020
@item smpte428
@item smpte428_1
SMPTE ST 428-1
@item smpte431
SMPTE 431-2
@item smpte432
SMPTE 432-1
@item jedec-p22
JEDEC P22
@end table

@item color_trc @var{integer} (@emph{decoding/encoding,video})
Possible values:
@table @samp
@item bt709
BT.709
@item gamma22
BT.470 M
@item gamma28
BT.470 BG
@item smpte170m
SMPTE 170 M
@item smpte240m
SMPTE 240 M
@item linear
Linear
@item log
@item log100
Log
@item log_sqrt
@item log316
Log square root
@item iec61966_2_4
@item iec61966-2-4
IEC 61966-2-4
@item bt1361
@item bt1361e
BT.1361
@item iec61966_2_1
@item iec61966-2-1
IEC 61966-2-1
@item bt2020_10
@item bt2020_10bit
BT.2020 - 10 bit
@item bt2020_12
@item bt2020_12bit
BT.2020 - 12 bit
@item smpte2084
SMPTE ST 2084
@item smpte428
@item smpte428_1
SMPTE ST 428-1
@item arib-std-b67
ARIB STD-B67
@end table

@item colorspace @var{integer} (@emph{decoding/encoding,video})
Possible values:
@table @samp
@item rgb
RGB
@item bt709
BT.709
@item fcc
FCC
@item bt470bg
BT.470 BG
@item smpte170m
SMPTE 170 M
@item smpte240m
SMPTE 240 M
@item ycocg
YCOCG
@item bt2020nc
@item bt2020_ncl
BT.2020 NCL
@item bt2020c
@item bt2020_cl
BT.2020 CL
@item smpte2085
SMPTE 2085
@item chroma-derived-nc
Chroma-derived NCL
@item chroma-derived-c
Chroma-derived CL
@item ictcp
ICtCp
@end table

@item color_range @var{integer} (@emph{decoding/encoding,video})
If used as input parameter, it serves as a hint to the decoder, which
color_range the input has.
Possible values:
@table @samp
@item tv
@item mpeg
@item limited
MPEG (219*2^(n-8))
@item pc
@item jpeg
@item full
JPEG (2^n-1)
@end table

@item chroma_sample_location @var{integer} (@emph{decoding/encoding,video})
Possible values:
@table @samp
@item left

@item center

@item topleft

@item top

@item bottomleft

@item bottom

@end table

@item log_level_offset @var{integer}
Set the log level offset.

@item slices @var{integer} (@emph{encoding,video})
Number of slices, used in parallelized encoding.

@item thread_type @var{flags} (@emph{decoding/encoding,video})
Select which multithreading methods to use.

Use of @samp{frame} will increase decoding delay by one frame per
thread, so clients which cannot provide future frames should not use
it.

Possible values:
@table @samp
@item slice
Decode more than one part of a single frame at once.

Multithreading using slices works only when the video was encoded with
slices.

@item frame
Decode more than one frame at once.
@end table

Default value is @samp{slice+frame}.

@item audio_service_type @var{integer} (@emph{encoding,audio})
Set audio service type.

Possible values:
@table @samp
@item ma
Main Audio Service
@item ef
Effects
@item vi
Visually Impaired
@item hi
Hearing Impaired
@item di
Dialogue
@item co
Commentary
@item em
Emergency
@item vo
Voice Over
@item ka
Karaoke
@end table

@item request_sample_fmt @var{sample_fmt} (@emph{decoding,audio})
Set sample format audio decoders should prefer. Default value is
@code{none}.

@item pkt_timebase @var{rational number}

@item sub_charenc @var{encoding} (@emph{decoding,subtitles})
Set the input subtitles character encoding.

@item field_order  @var{field_order} (@emph{video})
Set/override the field order of the video.
Possible values:
@table @samp
@item progressive
Progressive video
@item tt
Interlaced video, top field coded and displayed first
@item bb
Interlaced video, bottom field coded and displayed first
@item tb
Interlaced video, top coded first, bottom displayed first
@item bt
Interlaced video, bottom coded first, top displayed first
@end table

@item skip_alpha @var{bool} (@emph{decoding,video})
Set to 1 to disable processing alpha (transparency). This works like the
@samp{gray} flag in the @option{flags} option which skips chroma information
instead of alpha. Default is 0.

@item codec_whitelist @var{list} (@emph{input})
"," separated list of allowed decoders. By default all are allowed.

@item dump_separator @var{string} (@emph{input})
Separator used to separate the fields printed on the command line about the
Stream parameters.
For example, to separate the fields with newlines and indentation:
@example
ffprobe -dump_separator "
                          "  -i ~/videos/matrixbench_mpeg2.mpg
@end example

@item max_pixels @var{integer} (@emph{decoding/encoding,video})
Maximum number of pixels per image. This value can be used to avoid out of
memory failures due to large images.

@item apply_cropping @var{bool} (@emph{decoding,video})
Enable cropping if cropping parameters are multiples of the required
alignment for the left and top parameters. If the alignment is not met the
cropping will be partially applied to maintain alignment.
Default is 1 (enabled).
Note: The required alignment depends on if @code{AV_CODEC_FLAG_UNALIGNED} is set and the
CPU. @code{AV_CODEC_FLAG_UNALIGNED} cannot be changed from the command line. Also hardware
decoders will not apply left/top Cropping.


@end table

@c man end CODEC OPTIONS

@ifclear config-writeonly
@include decoders.texi
@end ifclear
@ifclear config-readonly
@include encoders.texi
@end ifclear
