\input texinfo @c -*- texinfo -*-
@documentencoding UTF-8

@settitle Community
@titlepage
@center @titlefont{Community}
@end titlepage

@top

@contents

@anchor{Organisation}
@chapter Organisation

The FFmpeg project is organized through a community working on global consensus.

Decisions are taken by the ensemble of active members, through voting and are aided by two committees.

@anchor{General Assembly}
@chapter General Assembly

The ensemble of active members is called the General Assembly (GA).

The General Assembly is sovereign and legitimate for all its decisions regarding the FFmpeg project.

The General Assembly is made up of active contributors.

Contributors are considered "active contributors" if they have authored more than 20 patches in the last 36 months in the main FFmpeg repository, or if they have been voted in by the GA.

The list of active contributors is updated twice each year, on 1st January and 1st July, 0:00 UTC.

Additional members are added to the General Assembly through a vote after proposal by a member of the General Assembly. They are part of the GA for two years, after which they need a confirmation by the GA.

A script to generate the current members of the general assembly (minus members voted in) can be found in `tools/general_assembly.pl`.

@anchor{Voting}
@chapter Voting

Voting is done using a ranked voting system, currently running on https://vote.ffmpeg.org/ .

Majority vote means more than 50% of the expressed ballots.

@anchor{Technical Committee}
@chapter Technical Committee

The Technical Committee (TC) is here to arbitrate and make decisions when technical conflicts occur in the project. They will consider the merits of all the positions, judge them and make a decision.

The TC resolves technical conflicts but is not a technical steering committee.

Decisions by the TC are binding for all the contributors.

Decisions made by the TC can be re-opened after 1 year or by a majority vote of the General Assembly, requested by one of the member of the GA.

The TC is elected by the General Assembly for a duration of 1 year, and is composed of 5 members. Members can be re-elected if they wish. A majority vote in the General Assembly can trigger a new election of the TC.

The members of the TC can be elected from outside of the GA. Candidates for election can either be suggested or self-nominated.

The conflict resolution process is detailed in the resolution process document.

The TC can be contacted at <tc@@ffmpeg>.

@anchor{Resolution Process}
@section Resolution Process

The Technical Committee (TC) is here to arbitrate and make decisions when technical conflicts occur in the project.

The TC main role is to resolve technical conflicts. It is therefore not a technical steering committee, but it is understood that some decisions might impact the future of the project.

@subsection Seizing

The TC can take possession of any technical matter that it sees fit.

To involve the TC in a matter, email tc@ or CC them on an ongoing discussion.

As members of TC are developers, they also can email tc@ to raise an issue.
@subsection Announcement

The TC, once seized, must announce itself on the main mailing list, with a [TC] tag.

The TC has 2 modes of operation: a RFC one and an internal one.

If the TC thinks it needs the input from the larger community, the TC can call for a RFC. Else, it can decide by itself.

The decision to use a RFC process or an internal discussion is a discretionary decision of the TC.

The TC can also reject a seizure for a few reasons such as: the matter was not discussed enough previously; it lacks expertise to reach a beneficial decision on the matter; or the matter is too trivial.
@subsection RFC call

In the RFC mode, one person from the TC posts on the mailing list the technical question and will request input from the community.

The mail will have the following specification:

    a precise title
    a specific tag [TC RFC]
    a top-level email
    contain a precise question that does not exceed 100 words and that is answerable by developers
    may have an extra description, or a link to a previous discussion, if deemed necessary,
    contain a precise end date for the answers.

The answers from the community must be on the main mailing list and must have the following specification:

    keep the tag and the title unchanged
    limited to 400 words
    a first-level, answering directly to the main email
    answering to the question.

Further replies to answers are permitted, as long as they conform to the community standards of politeness, they are limited to 100 words, and are not nested more than once. (max-depth=2)

After the end-date, mails on the thread will be ignored.

Violations of those rules will be escalated through the Community Committee.

After all the emails are in, the TC has 96 hours to give its final decision. Exceptionally, the TC can request an extra delay, that will be notified on the mailing list.
@subsection Within TC

In the internal case, the TC has 96 hours to give its final decision. Exceptionally, the TC can request an extra delay.
@subsection Decisions

The decisions from the TC will be sent on the mailing list, with the [TC] tag.

Internally, the TC should take decisions with a majority, or using ranked-choice voting.

Each TC member must vote on such decision according to what is, in their view, best for the project.

If a TC member feels they are affected by a conflict of interest with regards to the case, they should announce it and recuse themselves from the TC
discussion and vote.

A conflict of interest is presumed to occur when a TC member has a personal interest (e.g. financial) in a specific outcome of the case.

The decision from the TC should be published with a summary of the reasons that lead to this decision.

The decisions from the TC are final, until the matters are reopened after no less than one year.

@anchor{Community Committee}
@chapter Community Committee

The Community Committee (CC) is here to arbitrage and make decisions when inter-personal conflicts occur in the project. It will decide quickly and take actions, for the sake of the project.

The CC can remove privileges of offending members, including removal of commit access and temporary ban from the community.

Decisions made by the CC can be re-opened after 1 year or by a majority vote of the General Assembly. Indefinite bans from the community must be confirmed by the General Assembly, in a majority vote.

The CC is elected by the General Assembly for a duration of 1 year, and is composed of 5 members. Members can be re-elected if they wish. A majority vote in the General Assembly can trigger a new election of the CC.

The members of the CC can be elected from outside of the GA. Candidates for election can either be suggested or self-nominated.

The CC is governed by and responsible for enforcing the Code of Conduct.

The CC can be contacted at <cc@@ffmpeg>.

@anchor{Code of Conduct}
@chapter Code of Conduct

Be friendly and respectful towards others and third parties.
Treat others the way you yourself want to be treated.

Be considerate. Not everyone shares the same viewpoint and priorities as you do.
Different opinions and interpretations help the project.
Looking at issues from a different perspective assists development.

Do not assume malice for things that can be attributed to incompetence. Even if
it is malice, it's rarely good to start with that as initial assumption.

Stay friendly even if someone acts contrarily. Everyone has a bad day
once in a while.
If you yourself have a bad day or are angry then try to take a break and reply
once you are calm and without anger if you have to.

Try to help other team members and cooperate if you can.

The goal of software development is to create technical excellence, not for any
individual to be better and "win" against the others. Large software projects
are only possible and successful through teamwork.

If someone struggles do not put them down. Give them a helping hand
instead and point them in the right direction.

Finally, keep in mind the immortal words of Bill and Ted,
"Be excellent to each other."

@bye
