@chapter Encoders
@c man begin ENCODERS

Encoders are configured elements in FFmpeg which allow the encoding of
multimedia streams.

When you configure your FFmpeg build, all the supported native encoders
are enabled by default. Encoders requiring an external library must be enabled
manually via the corresponding @code{--enable-lib} option. You can list all
available encoders using the configure option @code{--list-encoders}.

You can disable all the encoders with the configure option
@code{--disable-encoders} and selectively enable / disable single encoders
with the options @code{--enable-encoder=@var{ENCODER}} /
@code{--disable-encoder=@var{ENCODER}}.

The option @code{-encoders} of the ff* tools will display the list of
enabled encoders.

@c man end ENCODERS

@chapter Audio Encoders
@c man begin AUDIO ENCODERS

A description of some of the currently available audio encoders
follows.

@anchor{aacenc}
@section aac

Advanced Audio Coding (AAC) encoder.

This encoder is the default AAC encoder, natively implemented into FFmpeg.

@subsection Options

@table @option
@item b
Set bit rate in bits/s. Setting this automatically activates constant bit rate
(CBR) mode. If this option is unspecified it is set to 128kbps.

@item q
Set quality for variable bit rate (VBR) mode. This option is valid only using
the @command{ffmpeg} command-line tool. For library interface users, use
@option{global_quality}.

@item cutoff
Set cutoff frequency. If unspecified will allow the encoder to dynamically
adjust the cutoff to improve clarity on low bitrates.

@item aac_coder
Set AAC encoder coding method. Possible values:

@table @samp
@item twoloop
Two loop searching (TLS) method. This is the default method.

This method first sets quantizers depending on band thresholds and then tries
to find an optimal combination by adding or subtracting a specific value from
all quantizers and adjusting some individual quantizer a little.  Will tune
itself based on whether @option{aac_is}, @option{aac_ms} and @option{aac_pns}
are enabled.

@item anmr
Average noise to mask ratio (ANMR) trellis-based solution.

This is an experimental coder which currently produces a lower quality, is more
unstable and is slower than the default twoloop coder but has potential.
Currently has no support for the @option{aac_is} or @option{aac_pns} options.
Not currently recommended.

@item fast
Constant quantizer method.

Uses a cheaper version of twoloop algorithm that doesn't try to do as many
clever adjustments. Worse with low bitrates (less than 64kbps), but is better
and much faster at higher bitrates.

@end table

@item aac_ms
Sets mid/side coding mode. The default value of "auto" will automatically use
M/S with bands which will benefit from such coding. Can be forced for all bands
using the value "enable", which is mainly useful for debugging or disabled using
"disable".

@item aac_is
Sets intensity stereo coding tool usage. By default, it's enabled and will
automatically toggle IS for similar pairs of stereo bands if it's beneficial.
Can be disabled for debugging by setting the value to "disable".

@item aac_pns
Uses perceptual noise substitution to replace low entropy high frequency bands
with imperceptible white noise during the decoding process. By default, it's
enabled, but can be disabled for debugging purposes by using "disable".

@item aac_tns
Enables the use of a multitap FIR filter which spans through the high frequency
bands to hide quantization noise during the encoding process and is reverted
by the decoder. As well as decreasing unpleasant artifacts in the high range
this also reduces the entropy in the high bands and allows for more bits to
be used by the mid-low bands. By default it's enabled but can be disabled for
debugging by setting the option to "disable".

@item aac_ltp
Enables the use of the long term prediction extension which increases coding
efficiency in very low bandwidth situations such as encoding of voice or
solo piano music by extending constant harmonic peaks in bands throughout
frames. This option is implied by profile:a aac_low and is incompatible with
aac_pred. Use in conjunction with @option{-ar} to decrease the samplerate.

@item aac_pred
Enables the use of a more traditional style of prediction where the spectral
coefficients transmitted are replaced by the difference of the current
coefficients minus the previous "predicted" coefficients. In theory and sometimes
in practice this can improve quality for low to mid bitrate audio.
This option implies the aac_main profile and is incompatible with aac_ltp.

@item profile
Sets the encoding profile, possible values:

@table @samp
@item aac_low
The default, AAC "Low-complexity" profile. Is the most compatible and produces
decent quality.

@item mpeg2_aac_low
Equivalent to @code{-profile:a aac_low -aac_pns 0}. PNS was introduced with the
MPEG4 specifications.

@item aac_ltp
Long term prediction profile, is enabled by and will enable the @option{aac_ltp}
option. Introduced in MPEG4.

@item aac_main
Main-type prediction profile, is enabled by and will enable the @option{aac_pred}
option. Introduced in MPEG2.

@end table
If this option is unspecified it is set to @samp{aac_low}.
@end table

@section ac3 and ac3_fixed

AC-3 audio encoders.

These encoders implement part of ATSC A/52:2010 and ETSI TS 102 366.

The @var{ac3} encoder uses floating-point math, while the @var{ac3_fixed}
encoder only uses fixed-point integer math. This does not mean that one is
always faster, just that one or the other may be better suited to a
particular system. The @var{ac3_fixed} encoder is not the default codec for
any of the output formats, so it must be specified explicitly using the option
@code{-acodec ac3_fixed} in order to use it.

@subsection AC-3 Metadata

The AC-3 metadata options are used to set parameters that describe the audio,
but in most cases do not affect the audio encoding itself. Some of the options
do directly affect or influence the decoding and playback of the resulting
bitstream, while others are just for informational purposes. A few of the
options will add bits to the output stream that could otherwise be used for
audio data, and will thus affect the quality of the output. Those will be
indicated accordingly with a note in the option list below.

These parameters are described in detail in several publicly-available
documents.
@itemize
@item @uref{http://www.atsc.org/cms/standards/a_52-2010.pdf,A/52:2010 - Digital Audio Compression (AC-3) (E-AC-3) Standard}
@item @uref{http://www.atsc.org/cms/standards/a_54a_with_corr_1.pdf,A/54 - Guide to the Use of the ATSC Digital Television Standard}
@item @uref{http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/18_Metadata.Guide.pdf,Dolby Metadata Guide}
@item @uref{http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/46_DDEncodingGuidelines.pdf,Dolby Digital Professional Encoding Guidelines}
@end itemize

@subsubsection Metadata Control Options

@table @option

@item -per_frame_metadata @var{boolean}
Allow Per-Frame Metadata. Specifies if the encoder should check for changing
metadata for each frame.
@table @option
@item 0
The metadata values set at initialization will be used for every frame in the
stream. (default)
@item 1
Metadata values can be changed before encoding each frame.
@end table

@end table

@subsubsection Downmix Levels

@table @option

@item -center_mixlev @var{level}
Center Mix Level. The amount of gain the decoder should apply to the center
channel when downmixing to stereo. This field will only be written to the
bitstream if a center channel is present. The value is specified as a scale
factor. There are 3 valid values:
@table @option
@item 0.707
Apply -3dB gain
@item 0.595
Apply -4.5dB gain (default)
@item 0.500
Apply -6dB gain
@end table

@item -surround_mixlev @var{level}
Surround Mix Level. The amount of gain the decoder should apply to the surround
channel(s) when downmixing to stereo. This field will only be written to the
bitstream if one or more surround channels are present. The value is specified
as a scale factor.  There are 3 valid values:
@table @option
@item 0.707
Apply -3dB gain
@item 0.500
Apply -6dB gain (default)
@item 0.000
Silence Surround Channel(s)
@end table

@end table

@subsubsection Audio Production Information
Audio Production Information is optional information describing the mixing
environment.  Either none or both of the fields are written to the bitstream.

@table @option

@item -mixing_level @var{number}
Mixing Level. Specifies peak sound pressure level (SPL) in the production
environment when the mix was mastered. Valid values are 80 to 111, or -1 for
unknown or not indicated. The default value is -1, but that value cannot be
used if the Audio Production Information is written to the bitstream. Therefore,
if the @code{room_type} option is not the default value, the @code{mixing_level}
option must not be -1.

@item -room_type @var{type}
Room Type. Describes the equalization used during the final mixing session at
the studio or on the dubbing stage. A large room is a dubbing stage with the
industry standard X-curve equalization; a small room has flat equalization.
This field will not be written to the bitstream if both the @code{mixing_level}
option and the @code{room_type} option have the default values.
@table @option
@item 0
@itemx notindicated
Not Indicated (default)
@item 1
@itemx large
Large Room
@item 2
@itemx small
Small Room
@end table

@end table

@subsubsection Other Metadata Options

@table @option

@item -copyright @var{boolean}
Copyright Indicator. Specifies whether a copyright exists for this audio.
@table @option
@item 0
@itemx off
No Copyright Exists (default)
@item 1
@itemx on
Copyright Exists
@end table

@item -dialnorm @var{value}
Dialogue Normalization. Indicates how far the average dialogue level of the
program is below digital 100% full scale (0 dBFS). This parameter determines a
level shift during audio reproduction that sets the average volume of the
dialogue to a preset level. The goal is to match volume level between program
sources. A value of -31dB will result in no volume level change, relative to
the source volume, during audio reproduction. Valid values are whole numbers in
the range -31 to -1, with -31 being the default.

@item -dsur_mode @var{mode}
Dolby Surround Mode. Specifies whether the stereo signal uses Dolby Surround
(Pro Logic). This field will only be written to the bitstream if the audio
stream is stereo. Using this option does @b{NOT} mean the encoder will actually
apply Dolby Surround processing.
@table @option
@item 0
@itemx notindicated
Not Indicated (default)
@item 1
@itemx off
Not Dolby Surround Encoded
@item 2
@itemx on
Dolby Surround Encoded
@end table

@item -original @var{boolean}
Original Bit Stream Indicator. Specifies whether this audio is from the
original source and not a copy.
@table @option
@item 0
@itemx off
Not Original Source
@item 1
@itemx on
Original Source (default)
@end table

@end table

@subsection Extended Bitstream Information
The extended bitstream options are part of the Alternate Bit Stream Syntax as
specified in Annex D of the A/52:2010 standard. It is grouped into 2 parts.
If any one parameter in a group is specified, all values in that group will be
written to the bitstream.  Default values are used for those that are written
but have not been specified.  If the mixing levels are written, the decoder
will use these values instead of the ones specified in the @code{center_mixlev}
and @code{surround_mixlev} options if it supports the Alternate Bit Stream
Syntax.

@subsubsection Extended Bitstream Information - Part 1

@table @option

@item -dmix_mode @var{mode}
Preferred Stereo Downmix Mode. Allows the user to select either Lt/Rt
(Dolby Surround) or Lo/Ro (normal stereo) as the preferred stereo downmix mode.
@table @option
@item 0
@itemx notindicated
Not Indicated (default)
@item 1
@itemx ltrt
Lt/Rt Downmix Preferred
@item 2
@itemx loro
Lo/Ro Downmix Preferred
@end table

@item -ltrt_cmixlev @var{level}
Lt/Rt Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lt/Rt mode.
@table @option
@item 1.414
Apply +3dB gain
@item 1.189
Apply +1.5dB gain
@item 1.000
Apply 0dB gain
@item 0.841
Apply -1.5dB gain
@item 0.707
Apply -3.0dB gain
@item 0.595
Apply -4.5dB gain (default)
@item 0.500
Apply -6.0dB gain
@item 0.000
Silence Center Channel
@end table

@item -ltrt_surmixlev @var{level}
Lt/Rt Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lt/Rt mode.
@table @option
@item 0.841
Apply -1.5dB gain
@item 0.707
Apply -3.0dB gain
@item 0.595
Apply -4.5dB gain
@item 0.500
Apply -6.0dB gain (default)
@item 0.000
Silence Surround Channel(s)
@end table

@item -loro_cmixlev @var{level}
Lo/Ro Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lo/Ro mode.
@table @option
@item 1.414
Apply +3dB gain
@item 1.189
Apply +1.5dB gain
@item 1.000
Apply 0dB gain
@item 0.841
Apply -1.5dB gain
@item 0.707
Apply -3.0dB gain
@item 0.595
Apply -4.5dB gain (default)
@item 0.500
Apply -6.0dB gain
@item 0.000
Silence Center Channel
@end table

@item -loro_surmixlev @var{level}
Lo/Ro Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lo/Ro mode.
@table @option
@item 0.841
Apply -1.5dB gain
@item 0.707
Apply -3.0dB gain
@item 0.595
Apply -4.5dB gain
@item 0.500
Apply -6.0dB gain (default)
@item 0.000
Silence Surround Channel(s)
@end table

@end table

@subsubsection Extended Bitstream Information - Part 2

@table @option

@item -dsurex_mode @var{mode}
Dolby Surround EX Mode. Indicates whether the stream uses Dolby Surround EX
(7.1 matrixed to 5.1). Using this option does @b{NOT} mean the encoder will actually
apply Dolby Surround EX processing.
@table @option
@item 0
@itemx notindicated
Not Indicated (default)
@item 1
@itemx on
Dolby Surround EX Off
@item 2
@itemx off
Dolby Surround EX On
@end table

@item -dheadphone_mode @var{mode}
Dolby Headphone Mode. Indicates whether the stream uses Dolby Headphone
encoding (multi-channel matrixed to 2.0 for use with headphones). Using this
option does @b{NOT} mean the encoder will actually apply Dolby Headphone
processing.
@table @option
@item 0
@itemx notindicated
Not Indicated (default)
@item 1
@itemx on
Dolby Headphone Off
@item 2
@itemx off
Dolby Headphone On
@end table

@item -ad_conv_type @var{type}
A/D Converter Type. Indicates whether the audio has passed through HDCD A/D
conversion.
@table @option
@item 0
@itemx standard
Standard A/D Converter (default)
@item 1
@itemx hdcd
HDCD A/D Converter
@end table

@end table

@subsection Other AC-3 Encoding Options

@table @option

@item -stereo_rematrixing @var{boolean}
Stereo Rematrixing. Enables/Disables use of rematrixing for stereo input. This
is an optional AC-3 feature that increases quality by selectively encoding
the left/right channels as mid/side. This option is enabled by default, and it
is highly recommended that it be left as enabled except for testing purposes.

@item cutoff @var{frequency}
Set lowpass cutoff frequency. If unspecified, the encoder selects a default
determined by various other encoding parameters.

@end table

@subsection Floating-Point-Only AC-3 Encoding Options

These options are only valid for the floating-point encoder and do not exist
for the fixed-point encoder due to the corresponding features not being
implemented in fixed-point.

@table @option

@item -channel_coupling @var{boolean}
Enables/Disables use of channel coupling, which is an optional AC-3 feature
that increases quality by combining high frequency information from multiple
channels into a single channel. The per-channel high frequency information is
sent with less accuracy in both the frequency and time domains. This allows
more bits to be used for lower frequencies while preserving enough information
to reconstruct the high frequencies. This option is enabled by default for the
floating-point encoder and should generally be left as enabled except for
testing purposes or to increase encoding speed.
@table @option
@item -1
@itemx auto
Selected by Encoder (default)
@item 0
@itemx off
Disable Channel Coupling
@item 1
@itemx on
Enable Channel Coupling
@end table

@item -cpl_start_band @var{number}
Coupling Start Band. Sets the channel coupling start band, from 1 to 15. If a
value higher than the bandwidth is used, it will be reduced to 1 less than the
coupling end band. If @var{auto} is used, the start band will be determined by
the encoder based on the bit rate, sample rate, and channel layout. This option
has no effect if channel coupling is disabled.
@table @option
@item -1
@itemx auto
Selected by Encoder (default)
@end table

@end table

@anchor{flac}
@section flac

FLAC (Free Lossless Audio Codec) Encoder

@subsection Options

The following options are supported by FFmpeg's flac encoder.

@table @option
@item compression_level
Sets the compression level, which chooses defaults for many other options
if they are not set explicitly. Valid values are from 0 to 12, 5 is the
default.

@item frame_size
Sets the size of the frames in samples per channel.

@item lpc_coeff_precision
Sets the LPC coefficient precision, valid values are from 1 to 15, 15 is the
default.

@item lpc_type
Sets the first stage LPC algorithm
@table @samp
@item none
LPC is not used

@item fixed
fixed LPC coefficients

@item levinson

@item cholesky
@end table

@item lpc_passes
Number of passes to use for Cholesky factorization during LPC analysis

@item min_partition_order
The minimum partition order

@item max_partition_order
The maximum partition order

@item prediction_order_method
@table @samp
@item estimation
@item 2level
@item 4level
@item 8level
@item search
Bruteforce search
@item log
@end table

@item ch_mode
Channel mode
@table @samp
@item auto
The mode is chosen automatically for each frame
@item indep
Channels are independently coded
@item left_side
@item right_side
@item mid_side
@end table

@item exact_rice_parameters
Chooses if rice parameters are calculated exactly or approximately.
if set to 1 then they are chosen exactly, which slows the code down slightly and
improves compression slightly.

@item multi_dim_quant
Multi Dimensional Quantization. If set to 1 then a 2nd stage LPC algorithm is
applied after the first stage to finetune the coefficients. This is quite slow
and slightly improves compression.

@end table

@anchor{opusenc}
@section opus

Opus encoder.

This is a native FFmpeg encoder for the Opus format. Currently, it's in development and
only implements the CELT part of the codec. Its quality is usually worse and at best
is equal to the libopus encoder.

@subsection Options

@table @option
@item b
Set bit rate in bits/s. If unspecified it uses the number of channels and the layout
to make a good guess.

@item opus_delay
Sets the maximum delay in milliseconds. Lower delays than 20ms will very quickly
decrease quality.
@end table

@anchor{libfdk-aac-enc}
@section libfdk_aac

libfdk-aac AAC (Advanced Audio Coding) encoder wrapper.

The libfdk-aac library is based on the Fraunhofer FDK AAC code from
the Android project.

Requires the presence of the libfdk-aac headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libfdk-aac}. The library is also incompatible with GPL,
so if you allow the use of GPL, you should configure with
@code{--enable-gpl --enable-nonfree --enable-libfdk-aac}.

This encoder has support for the AAC-HE profiles.

VBR encoding, enabled through the @option{vbr} or @option{flags
+qscale} options, is experimental and only works with some
combinations of parameters.

Support for encoding 7.1 audio is only available with libfdk-aac 0.1.3 or
higher.

For more information see the fdk-aac project at
@url{http://sourceforge.net/p/opencore-amr/fdk-aac/}.

@subsection Options

The following options are mapped on the shared FFmpeg codec options.

@table @option
@item b
Set bit rate in bits/s. If the bitrate is not explicitly specified, it
is automatically set to a suitable value depending on the selected
profile.

In case VBR mode is enabled the option is ignored.

@item ar
Set audio sampling rate (in Hz).

@item channels
Set the number of audio channels.

@item flags +qscale
Enable fixed quality, VBR (Variable Bit Rate) mode.
Note that VBR is implicitly enabled when the @option{vbr} value is
positive.

@item cutoff
Set cutoff frequency. If not specified (or explicitly set to 0) it
will use a value automatically computed by the library. Default value
is 0.

@item profile
Set audio profile.

The following profiles are recognized:
@table @samp
@item aac_low
Low Complexity AAC (LC)

@item aac_he
High Efficiency AAC (HE-AAC)

@item aac_he_v2
High Efficiency AAC version 2 (HE-AACv2)

@item aac_ld
Low Delay AAC (LD)

@item aac_eld
Enhanced Low Delay AAC (ELD)
@end table

If not specified it is set to @samp{aac_low}.
@end table

The following are private options of the libfdk_aac encoder.

@table @option
@item afterburner
Enable afterburner feature if set to 1, disabled if set to 0. This
improves the quality but also the required processing power.

Default value is 1.

@item eld_sbr
Enable SBR (Spectral Band Replication) for ELD if set to 1, disabled
if set to 0.

Default value is 0.

@item eld_v2
Enable ELDv2 (LD-MPS extension for ELD stereo signals) for ELDv2 if set to 1,
disabled if set to 0.

Note that option is available when fdk-aac version (AACENCODER_LIB_VL0.AACENCODER_LIB_VL1.AACENCODER_LIB_VL2) > (4.0.0).

Default value is 0.

@item signaling
Set SBR/PS signaling style.

It can assume one of the following values:
@table @samp
@item default
choose signaling implicitly (explicit hierarchical by default,
implicit if global header is disabled)

@item implicit
implicit backwards compatible signaling

@item explicit_sbr
explicit SBR, implicit PS signaling

@item explicit_hierarchical
explicit hierarchical signaling
@end table

Default value is @samp{default}.

@item latm
Output LATM/LOAS encapsulated data if set to 1, disabled if set to 0.

Default value is 0.

@item header_period
Set StreamMuxConfig and PCE repetition period (in frames) for sending
in-band configuration buffers within LATM/LOAS transport layer.

Must be a 16-bits non-negative integer.

Default value is 0.

@item vbr
Set VBR mode, from 1 to 5. 1 is lowest quality (though still pretty
good) and 5 is highest quality. A value of 0 will disable VBR, and CBR
(Constant Bit Rate) is enabled.

Currently only the @samp{aac_low} profile supports VBR encoding.

VBR modes 1-5 correspond to roughly the following average bit rates:

@table @samp
@item 1
32 kbps/channel
@item 2
40 kbps/channel
@item 3
48-56 kbps/channel
@item 4
64 kbps/channel
@item 5
about 80-96 kbps/channel
@end table

Default value is 0.

@item frame_length
Set the audio frame length in samples. Default value is the internal
default of the library. Refer to the library's documentation for information
about supported values.
@end table

@subsection Examples

@itemize
@item
Use @command{ffmpeg} to convert an audio file to VBR AAC in an M4A (MP4)
container:
@example
ffmpeg -i input.wav -codec:a libfdk_aac -vbr 3 output.m4a
@end example

@item
Use @command{ffmpeg} to convert an audio file to CBR 64k kbps AAC, using the
High-Efficiency AAC profile:
@example
ffmpeg -i input.wav -c:a libfdk_aac -profile:a aac_he -b:a 64k output.m4a
@end example
@end itemize

@anchor{liblc3-enc}
@section liblc3

liblc3 LC3 (Low Complexity Communication Codec) encoder wrapper.

Requires the presence of the liblc3 headers and library during configuration.
You need to explicitly configure the build with @code{--enable-liblc3}.

This encoder has support for the Bluetooth SIG LC3 codec for the LE Audio
protocol, and the following features of LC3plus:
@itemize
@item
Frame duration of 2.5 and 5ms.
@item
High-Resolution mode, 48 KHz, and 96 kHz sampling rates.
@end itemize

For more information see the liblc3 project at
@url{https://github.com/google/liblc3}.

@subsection Options

The following options are mapped on the shared FFmpeg codec options.

@table @option
@item b @var{bitrate}
Set the bit rate in bits/s. This will determine the fixed size of the encoded
frames, for a selected frame duration.

@item ar @var{frequency}
Set the audio sampling rate (in Hz).

@item channels
Set the number of audio channels.

@item frame_duration
Set the audio frame duration in milliseconds. Default value is 10ms.
Allowed frame durations are 2.5ms, 5ms, 7.5ms and 10ms.
LC3 (Bluetooth LE Audio), allows 7.5ms and 10ms; and LC3plus 2.5ms, 5ms
and 10ms.

The 10ms frame duration is available in LC3 and LC3 plus standard.
In this mode, the produced bitstream can be referenced either as LC3 or LC3plus.

@item high_resolution @var{boolean}
Enable the high-resolution mode if set to 1. The high-resolution mode is
available with all LC3plus frame durations and for a sampling rate of 48 KHz,
and 96 KHz.

The encoder automatically turns off this mode at lower sampling rates and
activates it at 96 KHz.

This mode should be preferred at high bitrates. In this mode, the audio
bandwidth is always up to the Nyquist frequency, compared to LC3 at 48 KHz,
which limits the bandwidth to 20 KHz.
@end table

@anchor{libmp3lame}
@section libmp3lame

LAME (Lame Ain't an MP3 Encoder) MP3 encoder wrapper.

Requires the presence of the libmp3lame headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libmp3lame}.

See @ref{libshine} for a fixed-point MP3 encoder, although with a
lower quality.

@subsection Options

The following options are supported by the libmp3lame wrapper. The
@command{lame}-equivalent of the options are listed in parentheses.

@table @option
@item b (@emph{-b})
Set bitrate expressed in bits/s for CBR or ABR. LAME @code{bitrate} is
expressed in kilobits/s.

@item q (@emph{-V})
Set constant quality setting for VBR. This option is valid only
using the @command{ffmpeg} command-line tool. For library interface
users, use @option{global_quality}.

@item compression_level (@emph{-q})
Set algorithm quality. Valid arguments are integers in the 0-9 range,
with 0 meaning highest quality but slowest, and 9 meaning fastest
while producing the worst quality.

@item cutoff (@emph{--lowpass})
Set lowpass cutoff frequency. If unspecified, the encoder dynamically
adjusts the cutoff.

@item reservoir
Enable use of bit reservoir when set to 1. Default value is 1. LAME
has this enabled by default, but can be overridden by use
@option{--nores} option.

@item joint_stereo (@emph{-m j})
Enable the encoder to use (on a frame by frame basis) either L/R
stereo or mid/side stereo. Default value is 1.

@item abr (@emph{--abr})
Enable the encoder to use ABR when set to 1. The @command{lame}
@option{--abr} sets the target bitrate, while this options only
tells FFmpeg to use ABR still relies on @option{b} to set bitrate.

@item copyright (@emph{-c})
Set MPEG audio copyright flag when set to 1. The default value is 0
(disabled).

@item original (@emph{-o})
Set MPEG audio original flag when set to 1. The default value is 1
(enabled).
@end table

@section libopencore-amrnb

OpenCORE Adaptive Multi-Rate Narrowband encoder.

Requires the presence of the libopencore-amrnb headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libopencore-amrnb --enable-version3}.

This is a mono-only encoder. Officially it only supports 8000Hz sample rate,
but you can override it by setting @option{strict} to @samp{unofficial} or
lower.

@subsection Options

@table @option

@item b
Set bitrate in bits per second. Only the following bitrates are supported,
otherwise libavcodec will round to the nearest valid bitrate.

@table @option
@item 4750
@item 5150
@item 5900
@item 6700
@item 7400
@item 7950
@item 10200
@item 12200
@end table

@item dtx
Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).

@end table

@section libopus

libopus Opus Interactive Audio Codec encoder wrapper.

Requires the presence of the libopus headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libopus}.

@subsection Option Mapping

Most libopus options are modelled after the @command{opusenc} utility from
opus-tools. The following is an option mapping chart describing options
supported by the libopus wrapper, and their @command{opusenc}-equivalent
in parentheses.

@table @option

@item b (@emph{bitrate})
Set the bit rate in bits/s.  FFmpeg's @option{b} option is
expressed in bits/s, while @command{opusenc}'s @option{bitrate} in
kilobits/s.

@item vbr (@emph{vbr}, @emph{hard-cbr}, and @emph{cvbr})
Set VBR mode. The FFmpeg @option{vbr} option has the following
valid arguments, with the @command{opusenc} equivalent options
in parentheses:

@table @samp
@item off (@emph{hard-cbr})
Use constant bit rate encoding.

@item on (@emph{vbr})
Use variable bit rate encoding (the default).

@item constrained (@emph{cvbr})
Use constrained variable bit rate encoding.
@end table

@item compression_level (@emph{comp})
Set encoding algorithm complexity. Valid options are integers in
the 0-10 range. 0 gives the fastest encodes but lower quality, while 10
gives the highest quality but slowest encoding. The default is 10.

@item frame_duration (@emph{framesize})
Set maximum frame size, or duration of a frame in milliseconds. The
argument must be exactly the following: 2.5, 5, 10, 20, 40, 60. Smaller
frame sizes achieve lower latency but less quality at a given bitrate.
Sizes greater than 20ms are only interesting at fairly low bitrates.
The default is 20ms.

@item packet_loss (@emph{expect-loss})
Set expected packet loss percentage. The default is 0.

@item fec (@emph{n/a})
Enable inband forward error correction. @option{packet_loss} must be non-zero
to take advantage - frequency of FEC 'side-data' is proportional to expected packet loss.
Default is disabled.

@item application (N.A.)
Set intended application type. Valid options are listed below:

@table @samp
@item voip
Favor improved speech intelligibility.
@item audio
Favor faithfulness to the input (the default).
@item lowdelay
Restrict to only the lowest delay modes by disabling voice-optimized
modes.
@end table

@item cutoff (N.A.)
Set cutoff bandwidth in Hz. The argument must be exactly one of the
following: 4000, 6000, 8000, 12000, or 20000, corresponding to
narrowband, mediumband, wideband, super wideband, and fullband
respectively. The default is 0 (cutoff disabled). Note that libopus
forces a wideband cutoff for bitrates < 15 kbps, unless CELT-only
(@option{application} set to @samp{lowdelay}) mode is used.

@item mapping_family (@emph{mapping_family})
Set channel mapping family to be used by the encoder. The default value of -1
uses mapping family 0 for mono and stereo inputs, and mapping family 1
otherwise. The default also disables the surround masking and LFE bandwidth
optimzations in libopus, and requires that the input contains 8 channels or
fewer.

Other values include 0 for mono and stereo, 1 for surround sound with masking
and LFE bandwidth optimizations, and 255 for independent streams with an
unspecified channel layout.

@item apply_phase_inv (N.A.) (requires libopus >= 1.2)
If set to 0, disables the use of phase inversion for intensity stereo,
improving the quality of mono downmixes, but slightly reducing normal stereo
quality. The default is 1 (phase inversion enabled).

@end table

@anchor{libshine}
@section libshine

Shine Fixed-Point MP3 encoder wrapper.

Shine is a fixed-point MP3 encoder. It has a far better performance on
platforms without an FPU, e.g. armel CPUs, and some phones and tablets.
However, as it is more targeted on performance than quality, it is not on par
with LAME and other production-grade encoders quality-wise. Also, according to
the project's homepage, this encoder may not be free of bugs as the code was
written a long time ago and the project was dead for at least 5 years.

This encoder only supports stereo and mono input. This is also CBR-only.

The original project (last updated in early 2007) is at
@url{http://sourceforge.net/projects/libshine-fxp/}. We only support the
updated fork by the Savonet/Liquidsoap project at @url{https://github.com/savonet/shine}.

Requires the presence of the libshine headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libshine}.

See also @ref{libmp3lame}.

@subsection Options

The following options are supported by the libshine wrapper. The
@command{shineenc}-equivalent of the options are listed in parentheses.

@table @option
@item b (@emph{-b})
Set bitrate expressed in bits/s for CBR. @command{shineenc} @option{-b} option
is expressed in kilobits/s.

@end table

@section libtwolame

TwoLAME MP2 encoder wrapper.

Requires the presence of the libtwolame headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libtwolame}.

@subsection Options

The following options are supported by the libtwolame wrapper. The
@command{twolame}-equivalent options follow the FFmpeg ones and are in
parentheses.

@table @option
@item b (@emph{-b})
Set bitrate expressed in bits/s for CBR. @command{twolame} @option{b}
option is expressed in kilobits/s. Default value is 128k.

@item q (@emph{-V})
Set quality for experimental VBR support. Maximum value range is
from -50 to 50, useful range is from -10 to 10. The higher the
value, the better the quality. This option is valid only using the
@command{ffmpeg} command-line tool. For library interface users,
use @option{global_quality}.

@item mode (@emph{--mode})
Set the mode of the resulting audio. Possible values:

@table @samp
@item auto
Choose mode automatically based on the input. This is the default.
@item stereo
Stereo
@item joint_stereo
Joint stereo
@item dual_channel
Dual channel
@item mono
Mono
@end table

@item psymodel (@emph{--psyc-mode})
Set psychoacoustic model to use in encoding. The argument must be
an integer between -1 and 4, inclusive. The higher the value, the
better the quality. The default value is 3.

@item energy_levels (@emph{--energy})
Enable energy levels extensions when set to 1. The default value is
0 (disabled).

@item error_protection (@emph{--protect})
Enable CRC error protection when set to 1. The default value is 0
(disabled).

@item copyright (@emph{--copyright})
Set MPEG audio copyright flag when set to 1. The default value is 0
(disabled).

@item original (@emph{--original})
Set MPEG audio original flag when set to 1. The default value is 0
(disabled).

@end table

@section libvo-amrwbenc

VisualOn Adaptive Multi-Rate Wideband encoder.

Requires the presence of the libvo-amrwbenc headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libvo-amrwbenc --enable-version3}.

This is a mono-only encoder. Officially it only supports 16000Hz sample
rate, but you can override it by setting @option{strict} to
@samp{unofficial} or lower.

@subsection Options

@table @option

@item b
Set bitrate in bits/s. Only the following bitrates are supported, otherwise
libavcodec will round to the nearest valid bitrate.

@table @samp
@item 6600
@item 8850
@item 12650
@item 14250
@item 15850
@item 18250
@item 19850
@item 23050
@item 23850
@end table

@item dtx
Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).

@end table

@section libvorbis

libvorbis encoder wrapper.

Requires the presence of the libvorbisenc headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libvorbis}.

@subsection Options

The following options are supported by the libvorbis wrapper. The
@command{oggenc}-equivalent of the options are listed in parentheses.

To get a more accurate and extensive documentation of the libvorbis
options, consult the libvorbisenc's and @command{oggenc}'s documentations.
See @url{http://xiph.org/vorbis/},
@url{http://wiki.xiph.org/Vorbis-tools}, and oggenc(1).

@table @option
@item b (@emph{-b})
Set bitrate expressed in bits/s for ABR. @command{oggenc} @option{-b} is
expressed in kilobits/s.

@item q (@emph{-q})
Set constant quality setting for VBR. The value should be a float
number in the range of -1.0 to 10.0. The higher the value, the better
the quality. The default value is @samp{3.0}.

This option is valid only using the @command{ffmpeg} command-line tool.
For library interface users, use @option{global_quality}.

@item cutoff (@emph{--advanced-encode-option lowpass_frequency=N})
Set cutoff bandwidth in Hz, a value of 0 disables cutoff. @command{oggenc}'s
related option is expressed in kHz. The default value is @samp{0} (cutoff
disabled).

@item minrate (@emph{-m})
Set minimum bitrate expressed in bits/s. @command{oggenc} @option{-m} is
expressed in kilobits/s.

@item maxrate (@emph{-M})
Set maximum bitrate expressed in bits/s. @command{oggenc} @option{-M} is
expressed in kilobits/s. This only has effect on ABR mode.

@item iblock (@emph{--advanced-encode-option impulse_noisetune=N})
Set noise floor bias for impulse blocks. The value is a float number from
-15.0 to 0.0. A negative bias instructs the encoder to pay special attention
to the crispness of transients in the encoded audio. The tradeoff for better
transient response is a higher bitrate.

@end table

@anchor{mjpegenc}
@section mjpeg

Motion JPEG encoder.

@subsection Options

@table @option
@item huffman
Set the huffman encoding strategy. Possible values:

@table @samp
@item default
Use the default huffman tables. This is the default strategy.

@item optimal
Compute and use optimal huffman tables.

@end table
@end table

@anchor{wavpackenc}
@section wavpack

WavPack lossless audio encoder.

@subsection Options

The equivalent options for @command{wavpack} command line utility are listed in
parentheses.

@subsubsection Shared options

The following shared options are effective for this encoder. Only special notes
about this particular encoder will be documented here. For the general meaning
of the options, see @ref{codec-options,,the Codec Options chapter}.

@table @option
@item frame_size (@emph{--blocksize})
For this encoder, the range for this option is between 128 and 131072. Default
is automatically decided based on sample rate and number of channel.

For the complete formula of calculating default, see
@file{libavcodec/wavpackenc.c}.

@item compression_level (@emph{-f}, @emph{-h}, @emph{-hh}, and @emph{-x})
@end table

@subsubsection Private options

@table @option
@item joint_stereo (@emph{-j})
Set whether to enable joint stereo. Valid values are:

@table @samp
@item on (@emph{1})
Force mid/side audio encoding.
@item off (@emph{0})
Force left/right audio encoding.
@item auto
Let the encoder decide automatically.
@end table

@item optimize_mono
Set whether to enable optimization for mono. This option is only effective for
non-mono streams. Available values:

@table @samp
@item on
enabled
@item off
disabled
@end table

@end table

@c man end AUDIO ENCODERS

@chapter Video Encoders
@c man begin VIDEO ENCODERS

A description of some of the currently available video encoders
follows.

@section a64_multi, a64_multi5

A64 / Commodore 64 multicolor charset encoder. @code{a64_multi5} is extended with 5th color (colram).

@section Cinepak

Cinepak aka CVID encoder.
Compatible with Windows 3.1 and vintage MacOS.

@subsection Options

@table @option
@item g @var{integer}
Keyframe interval.
A keyframe is inserted at least every @code{-g} frames, sometimes sooner.

@item q:v @var{integer}
Quality factor. Lower is better. Higher gives lower bitrate.
The following table lists bitrates when encoding akiyo_cif.y4m for various values of @code{-q:v} with @code{-g 100}:

@table @option
@item @code{-q:v 1} 1918 kb/s
@item @code{-q:v 2} 1735 kb/s
@item @code{-q:v 4} 1500 kb/s
@item @code{-q:v 10} 1041 kb/s
@item @code{-q:v 20} 826 kb/s
@item @code{-q:v 40} 553 kb/s
@item @code{-q:v 100} 394 kb/s
@item @code{-q:v 200} 312 kb/s
@item @code{-q:v 400} 266 kb/s
@item @code{-q:v 1000} 237 kb/s
@end table

@item max_extra_cb_iterations @var{integer}
Max extra codebook recalculation passes, more is better and slower.

@item skip_empty_cb @var{boolean}
Avoid wasting bytes, ignore vintage MacOS decoder.

@item max_strips @var{integer}
@itemx min_strips @var{integer}
The minimum and maximum number of strips to use.
Wider range sometimes improves quality.
More strips is generally better quality but costs more bits.
Fewer strips tend to yield more keyframes.
Vintage compatible is 1..3.

@item strip_number_adaptivity @var{integer}
How much number of strips is allowed to change between frames.
Higher is better but slower.

@end table

@section GIF

GIF image/animation encoder.

@subsection Options

@table @option
@item gifflags @var{integer}
Sets the flags used for GIF encoding.

@table @option
@item offsetting
Enables picture offsetting.

Default is enabled.

@item transdiff
Enables transparency detection between frames.

Default is enabled.

@end table

@item gifimage @var{integer}
Enables encoding one full GIF image per frame, rather than an animated GIF.

Default value is @option{0}.

@item global_palette @var{integer}
Writes a palette to the global GIF header where feasible.

If disabled, every frame will always have a palette written, even if there
is a global palette supplied.

Default value is @option{1}.

@end table

@section Hap

Vidvox Hap video encoder.

@subsection Options

@table @option
@item format @var{integer}
Specifies the Hap format to encode.

@table @option
@item hap
@item hap_alpha
@item hap_q
@end table

Default value is @option{hap}.

@item chunks @var{integer}
Specifies the number of chunks to split frames into, between 1 and 64. This
permits multithreaded decoding of large frames, potentially at the cost of
data-rate. The encoder may modify this value to divide frames evenly.

Default value is @var{1}.

@item compressor @var{integer}
Specifies the second-stage compressor to use. If set to @option{none},
@option{chunks} will be limited to 1, as chunked uncompressed frames offer no
benefit.

@table @option
@item none
@item snappy
@end table

Default value is @option{snappy}.

@end table

@section jpeg2000

The native jpeg 2000 encoder is lossy by default, the @code{-q:v}
option can be used to set the encoding quality. Lossless encoding
can be selected with @code{-pred 1}.

@subsection Options

@table @option
@item format @var{integer}
Can be set to either @code{j2k} or @code{jp2} (the default) that
makes it possible to store non-rgb pix_fmts.

@item tile_width @var{integer}
Sets tile width. Range is 1 to 1073741824. Default is 256.

@item tile_height @var{integer}
Sets tile height. Range is 1 to 1073741824. Default is 256.

@item pred @var{integer}
Allows setting the discrete wavelet transform (DWT) type
@table @option
@item dwt97int (Lossy)
@item dwt53 (Lossless)
@end table
Default is @code{dwt97int}

@item sop @var{boolean}
Enable this to add SOP marker at the start of each packet. Disabled by default.

@item eph @var{boolean}
Enable this to add EPH marker at the end of each packet header. Disabled by default.

@item prog @var{integer}
Sets the progression order to be used by the encoder.
Possible values are:
@table @option
@item lrcp
@item rlcp
@item rpcl
@item pcrl
@item cprl
@end table
Set to @code{lrcp} by default.

@item layer_rates @var{string}
By default, when this option is not used, compression is done using the quality metric.
This option allows for compression using compression ratio. The compression ratio for each
level could be specified. The compression ratio of a layer @code{l} species the what ratio of
total file size is contained in the first @code{l} layers.

Example usage:

@example
ffmpeg -i input.bmp -c:v jpeg2000 -layer_rates "100,10,1" output.j2k
@end example

This would compress the image to contain 3 layers, where the data contained in the
first layer would be compressed by 1000 times, compressed by 100 in the first two layers,
and shall contain all data while using all 3 layers.

@end table

@section librav1e

rav1e AV1 encoder wrapper.

Requires the presence of the rav1e headers and library during configuration.
You need to explicitly configure the build with @code{--enable-librav1e}.

@subsection Options

@table @option
@item qmax
Sets the maximum quantizer to use when using bitrate mode.

@item qmin
Sets the minimum quantizer to use when using bitrate mode.

@item qp
Uses quantizer mode to encode at the given quantizer (0-255).

@item speed
Selects the speed preset (0-10) to encode with.

@item tiles
Selects how many tiles to encode with.

@item tile-rows
Selects how many rows of tiles to encode with.

@item tile-columns
Selects how many columns of tiles to encode with.

@item rav1e-params
Set rav1e options using a list of @var{key}=@var{value} pairs separated
by ":". See @command{rav1e --help} for a list of options.

For example to specify librav1e encoding options with @option{-rav1e-params}:

@example
ffmpeg -i input -c:v librav1e -b:v 500K -rav1e-params speed=5:low_latency=true output.mp4
@end example

@end table

@section libaom-av1

libaom AV1 encoder wrapper.

Requires the presence of the libaom headers and library during
configuration.  You need to explicitly configure the build with
@code{--enable-libaom}.

@subsection Options

The wrapper supports the following standard libavcodec options:

@table @option

@item b
Set bitrate target in bits/second.  By default this will use
variable-bitrate mode.  If @option{maxrate} and @option{minrate} are
also set to the same value then it will use constant-bitrate mode,
otherwise if @option{crf} is set as well then it will use
constrained-quality mode.

@item g keyint_min
Set key frame placement.  The GOP size sets the maximum distance between
key frames; if zero the output stream will be intra-only.  The minimum
distance is ignored unless it is the same as the GOP size, in which case
key frames will always appear at a fixed interval.  Not set by default,
so without this option the library has completely free choice about
where to place key frames.

@item qmin qmax
Set minimum/maximum quantisation values.  Valid range is from 0 to 63
(warning: this does not match the quantiser values actually used by AV1
- divide by four to map real quantiser values to this range).  Defaults
to min/max (no constraint).

@item minrate maxrate bufsize rc_init_occupancy
Set rate control buffering parameters.  Not used if not set - defaults
to unconstrained variable bitrate.

@item threads
Set the number of threads to use while encoding.  This may require the
@option{tiles} or @option{row-mt} options to also be set to actually
use the specified number of threads fully. Defaults to the number of
hardware threads supported by the host machine.

@item profile
Set the encoding profile.  Defaults to using the profile which matches
the bit depth and chroma subsampling of the input.

@end table

The wrapper also has some specific options:

@table @option

@item cpu-used
Set the quality/encoding speed tradeoff.  Valid range is from 0 to 8,
higher numbers indicating greater speed and lower quality.  The default
value is 1, which will be slow and high quality.

@item auto-alt-ref
Enable use of alternate reference frames.  Defaults to the internal
default of the library.

@item arnr-max-frames (@emph{frames})
Set altref noise reduction max frame count. Default is -1.

@item arnr-strength (@emph{strength})
Set altref noise reduction filter strength. Range is -1 to 6. Default is -1.

@item aq-mode (@emph{aq-mode})
Set adaptive quantization mode. Possible values:

@table @samp
@item none (@emph{0})
Disabled.

@item variance (@emph{1})
Variance-based.

@item complexity (@emph{2})
Complexity-based.

@item cyclic (@emph{3})
Cyclic refresh.
@end table

@item tune (@emph{tune})
Set the distortion metric the encoder is tuned with. Default is @code{psnr}.

@table @samp
@item psnr (@emph{0})

@item ssim (@emph{1})
@end table

@item lag-in-frames
Set the maximum number of frames which the encoder may keep in flight
at any one time for lookahead purposes.  Defaults to the internal
default of the library.

@item error-resilience
Enable error resilience features:
@table @option
@item default
Improve resilience against losses of whole frames.
@end table
Not enabled by default.

@item crf
Set the quality/size tradeoff for constant-quality (no bitrate target)
and constrained-quality (with maximum bitrate target) modes. Valid
range is 0 to 63, higher numbers indicating lower quality and smaller
output size.  Only used if set; by default only the bitrate target is
used.

@item static-thresh
Set a change threshold on blocks below which they will be skipped by
the encoder.  Defined in arbitrary units as a nonnegative integer,
defaulting to zero (no blocks are skipped).

@item drop-threshold
Set a threshold for dropping frames when close to rate control bounds.
Defined as a percentage of the target buffer - when the rate control
buffer falls below this percentage, frames will be dropped until it
has refilled above the threshold.  Defaults to zero (no frames are
dropped).

@item denoise-noise-level (@emph{level})
Amount of noise to be removed for grain synthesis. Grain synthesis is disabled if
this option is not set or set to 0.

@item denoise-block-size (@emph{pixels})
Block size used for denoising for grain synthesis. If not set, AV1 codec
uses the default value of 32.

@item undershoot-pct (@emph{pct})
Set datarate undershoot (min) percentage of the target bitrate. Range is -1 to 100.
Default is -1.

@item overshoot-pct (@emph{pct})
Set datarate overshoot (max) percentage of the target bitrate. Range is -1 to 1000.
Default is -1.

@item minsection-pct (@emph{pct})
Minimum percentage variation of the GOP bitrate from the target bitrate. If minsection-pct
is not set, the libaomenc wrapper computes it as follows: @code{(minrate * 100 / bitrate)}.
Range is -1 to 100. Default is -1 (unset).

@item maxsection-pct (@emph{pct})
Maximum percentage variation of the GOP bitrate from the target bitrate. If maxsection-pct
is not set, the libaomenc wrapper computes it as follows: @code{(maxrate * 100 / bitrate)}.
Range is -1 to 5000. Default is -1 (unset).

@item frame-parallel (@emph{boolean})
Enable frame parallel decodability features. Default is true.

@item tiles
Set the number of tiles to encode the input video with, as columns x
rows.  Larger numbers allow greater parallelism in both encoding and
decoding, but may decrease coding efficiency.  Defaults to the minimum
number of tiles required by the size of the input video (this is 1x1
(that is, a single tile) for sizes up to and including 4K).

@item tile-columns tile-rows
Set the number of tiles as log2 of the number of tile rows and columns.
Provided for compatibility with libvpx/VP9.

@item row-mt (Requires libaom >= 1.0.0-759-g90a15f4f2)
Enable row based multi-threading. Disabled by default.

@item enable-cdef (@emph{boolean})
Enable Constrained Directional Enhancement Filter. The libaom-av1
encoder enables CDEF by default.

@item enable-restoration (@emph{boolean})
Enable Loop Restoration Filter. Default is true for libaom-av1.

@item enable-global-motion (@emph{boolean})
Enable the use of global motion for block prediction. Default is true.

@item enable-intrabc (@emph{boolean})
Enable block copy mode for intra block prediction. This mode is
useful for screen content. Default is true.

@item enable-rect-partitions (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable rectangular partitions. Default is true.

@item enable-1to4-partitions (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable 1:4/4:1 partitions. Default is true.

@item enable-ab-partitions (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable AB shape partitions. Default is true.

@item enable-angle-delta (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable angle delta intra prediction. Default is true.

@item enable-cfl-intra (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable chroma predicted from luma intra prediction. Default is true.

@item enable-filter-intra (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable filter intra predictor. Default is true.

@item enable-intra-edge-filter (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable intra edge filter. Default is true.

@item enable-smooth-intra (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable smooth intra prediction mode. Default is true.

@item enable-paeth-intra (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable paeth predictor in intra prediction. Default is true.

@item enable-palette (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable palette prediction mode. Default is true.

@item enable-flip-idtx (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable extended transform type, including FLIPADST_DCT, DCT_FLIPADST,
FLIPADST_FLIPADST, ADST_FLIPADST, FLIPADST_ADST, IDTX, V_DCT, H_DCT,
V_ADST, H_ADST, V_FLIPADST, H_FLIPADST. Default is true.

@item enable-tx64 (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable 64-pt transform. Default is true.

@item reduced-tx-type-set (@emph{boolean}) (Requires libaom >= v2.0.0)
Use reduced set of transform types. Default is false.

@item use-intra-dct-only (@emph{boolean}) (Requires libaom >= v2.0.0)
Use DCT only for INTRA modes. Default is false.

@item use-inter-dct-only (@emph{boolean}) (Requires libaom >= v2.0.0)
Use DCT only for INTER modes. Default is false.

@item use-intra-default-tx-only (@emph{boolean}) (Requires libaom >= v2.0.0)
Use Default-transform only for INTRA modes. Default is false.

@item enable-ref-frame-mvs (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable temporal mv prediction. Default is true.

@item enable-reduced-reference-set (@emph{boolean}) (Requires libaom >= v2.0.0)
Use reduced set of single and compound references. Default is false.

@item enable-obmc (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable obmc. Default is true.

@item enable-dual-filter (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable dual filter. Default is true.

@item enable-diff-wtd-comp (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable difference-weighted compound. Default is true.

@item enable-dist-wtd-comp (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable distance-weighted compound. Default is true.

@item enable-onesided-comp (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable one sided compound. Default is true.

@item enable-interinter-wedge (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable interinter wedge compound. Default is true.

@item enable-interintra-wedge (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable interintra wedge compound. Default is true.

@item enable-masked-comp (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable masked compound. Default is true.

@item enable-interintra-comp (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable interintra compound. Default is true.

@item enable-smooth-interintra (@emph{boolean}) (Requires libaom >= v2.0.0)
Enable smooth interintra mode. Default is true.

@item aom-params
Set libaom options using a list of @var{key}=@var{value} pairs separated
by ":". For a list of supported options, see @command{aomenc --help} under the
section "AV1 Specific Options".

For example to specify libaom encoding options with @option{-aom-params}:

@example
ffmpeg -i input -c:v libaom-av1 -b:v 500K -aom-params tune=psnr:enable-tpl-model=1 output.mp4
@end example

@end table

@section libsvtav1

SVT-AV1 encoder wrapper.

Requires the presence of the SVT-AV1 headers and library during configuration.
You need to explicitly configure the build with @code{--enable-libsvtav1}.

@subsection Options

@table @option
@item profile
Set the encoding profile.
@table @samp
@item main
@item high
@item professional
@end table

@item level
Set the operating point level. For example: '4.0'

@item hielevel
Set the Hierarchical prediction levels.
@table @samp
@item 3level
@item 4level
This is the default.
@end table

@item tier
Set the operating point tier.
@table @samp
@item main
This is the default.
@item high
@end table

@item qmax
Set the maximum quantizer to use when using a bitrate mode.

@item qmin
Set the minimum quantizer to use when using a bitrate mode.

@item crf
Constant rate factor value used in crf rate control mode (0-63).

@item qp
Set the quantizer used in cqp rate control mode (0-63).

@item sc_detection
Enable scene change detection.

@item la_depth
Set number of frames to look ahead (0-120).

@item preset
Set the quality-speed tradeoff, in the range 0 to 13.  Higher values are
faster but lower quality.

@item tile_rows
Set log2 of the number of rows of tiles to use (0-6).

@item tile_columns
Set log2 of the number of columns of tiles to use (0-4).

@item svtav1-params
Set SVT-AV1 options using a list of @var{key}=@var{value} pairs separated
by ":". See the SVT-AV1 encoder user guide for a list of accepted parameters.

@end table

@section libjxl

libjxl JPEG XL encoder wrapper.

Requires the presence of the libjxl headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libjxl}.

@subsection Options

The libjxl wrapper supports the following options:

@table @option

@item distance
Set the target Butteraugli distance. This is a quality setting: lower
distance yields higher quality, with distance=1.0 roughly comparable to
libjpeg Quality 90 for photographic content. Setting distance=0.0 yields
true lossless encoding. Valid values range between 0.0 and 15.0, and sane
values rarely exceed 5.0. Setting distance=0.1 usually attains
transparency for most input. The default is 1.0.

@item effort
Set the encoding effort used. Higher effort values produce more consistent
quality and usually produces a better quality/bpp curve, at the cost of
more CPU time required. Valid values range from 1 to 9, and the default is 7.

@item modular
Force the encoder to use Modular mode instead of choosing automatically. The
default is to use VarDCT for lossy encoding and Modular for lossless. VarDCT
is generally superior to Modular for lossy encoding but does not support
lossless encoding.

@end table

@section libkvazaar

Kvazaar H.265/HEVC encoder.

Requires the presence of the libkvazaar headers and library during
configuration. You need to explicitly configure the build with
@option{--enable-libkvazaar}.

@subsection Options

@table @option

@item b
Set target video bitrate in bit/s and enable rate control.

@item kvazaar-params
Set kvazaar parameters as a list of @var{name}=@var{value} pairs separated
by commas (,). See kvazaar documentation for a list of options.

@end table

@section libopenh264

Cisco libopenh264 H.264/MPEG-4 AVC encoder wrapper.

This encoder requires the presence of the libopenh264 headers and
library during configuration. You need to explicitly configure the
build with @code{--enable-libopenh264}. The library is detected using
@command{pkg-config}.

For more information about the library see
@url{http://www.openh264.org}.

@subsection Options

The following FFmpeg global options affect the configurations of the
libopenh264 encoder.

@table @option
@item b
Set the bitrate (as a number of bits per second).

@item g
Set the GOP size.

@item maxrate
Set the max bitrate (as a number of bits per second).

@item flags +global_header
Set global header in the bitstream.

@item slices
Set the number of slices, used in parallelized encoding. Default value
is 0. This is only used when @option{slice_mode} is set to
@samp{fixed}.

@item loopfilter
Enable loop filter, if set to 1 (automatically enabled). To disable
set a value of 0.

@item profile
Set profile restrictions. If set to the value of @samp{main} enable
CABAC (set the @code{SEncParamExt.iEntropyCodingModeFlag} flag to 1).

@item max_nal_size
Set maximum NAL size in bytes.

@item allow_skip_frames
Allow skipping frames to hit the target bitrate if set to 1.
@end table

@section libtheora

libtheora Theora encoder wrapper.

Requires the presence of the libtheora headers and library during
configuration. You need to explicitly configure the build with
@code{--enable-libtheora}.

For more information about the libtheora project see
@url{http://www.theora.org/}.

@subsection Options

The following global options are mapped to internal libtheora options
which affect the quality and the bitrate of the encoded stream.

@table @option
@item b
Set the video bitrate in bit/s for CBR (Constant Bit Rate) mode.  In
case VBR (Variable Bit Rate) mode is enabled this option is ignored.

@item flags
Used to enable constant quality mode (VBR) encoding through the
@option{qscale} flag, and to enable the @code{pass1} and @code{pass2}
modes.

@item g
Set the GOP size.

@item global_quality
Set the global quality as an integer in lambda units.

Only relevant when VBR mode is enabled with @code{flags +qscale}. The
value is converted to QP units by dividing it by @code{FF_QP2LAMBDA},
clipped in the [0 - 10] range, and then multiplied by 6.3 to get a
value in the native libtheora range [0-63]. A higher value corresponds
to a higher quality.

@item q
Enable VBR mode when set to a non-negative value, and set constant
quality value as a double floating point value in QP units.

The value is clipped in the [0-10] range, and then multiplied by 6.3
to get a value in the native libtheora range [0-63].

This option is valid only using the @command{ffmpeg} command-line
tool. For library interface users, use @option{global_quality}.
@end table

@subsection Examples

@itemize
@item
Set maximum constant quality (VBR) encoding with @command{ffmpeg}:
@example
ffmpeg -i INPUT -codec:v libtheora -q:v 10 OUTPUT.ogg
@end example

@item
Use @command{ffmpeg} to convert a CBR 1000 kbps Theora video stream:
@example
ffmpeg -i INPUT -codec:v libtheora -b:v 1000k OUTPUT.ogg
@end example
@end itemize

@section libvpx

VP8/VP9 format supported through libvpx.

Requires the presence of the libvpx headers and library during configuration.
You need to explicitly configure the build with @code{--enable-libvpx}.

@subsection Options

The following options are supported by the libvpx wrapper. The
@command{vpxenc}-equivalent options or values are listed in parentheses
for easy migration.

To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
@ref{codec-options,,the Codec Options chapter}.

To get more documentation of the libvpx options, invoke the command
@command{ffmpeg -h encoder=libvpx}, @command{ffmpeg -h encoder=libvpx-vp9} or
@command{vpxenc --help}. Further information is available in the libvpx API
documentation.

@table @option

@item b (@emph{target-bitrate})
Set bitrate in bits/s. Note that FFmpeg's @option{b} option is
expressed in bits/s, while @command{vpxenc}'s @option{target-bitrate} is in
kilobits/s.

@item g (@emph{kf-max-dist})

@item keyint_min (@emph{kf-min-dist})

@item qmin (@emph{min-q})
Minimum (Best Quality) Quantizer.

@item qmax (@emph{max-q})
Maximum (Worst Quality) Quantizer.
Can be changed per-frame.

@item bufsize (@emph{buf-sz}, @emph{buf-optimal-sz})
Set ratecontrol buffer size (in bits). Note @command{vpxenc}'s options are
specified in milliseconds, the libvpx wrapper converts this value as follows:
@code{buf-sz = bufsize * 1000 / bitrate},
@code{buf-optimal-sz = bufsize * 1000 / bitrate * 5 / 6}.

@item rc_init_occupancy (@emph{buf-initial-sz})
Set number of bits which should be loaded into the rc buffer before decoding
starts. Note @command{vpxenc}'s option is specified in milliseconds, the libvpx
wrapper converts this value as follows:
@code{rc_init_occupancy * 1000 / bitrate}.

@item undershoot-pct
Set datarate undershoot (min) percentage of the target bitrate.

@item overshoot-pct
Set datarate overshoot (max) percentage of the target bitrate.

@item skip_threshold (@emph{drop-frame})

@item qcomp (@emph{bias-pct})

@item maxrate (@emph{maxsection-pct})
Set GOP max bitrate in bits/s. Note @command{vpxenc}'s option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: @code{(maxrate * 100 / bitrate)}.

@item minrate (@emph{minsection-pct})
Set GOP min bitrate in bits/s. Note @command{vpxenc}'s option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: @code{(minrate * 100 / bitrate)}.

@item minrate, maxrate, b @emph{end-usage=cbr}
@code{(minrate == maxrate == bitrate)}.

@item crf (@emph{end-usage=cq}, @emph{cq-level})

@item tune (@emph{tune})
@table @samp
@item psnr (@emph{psnr})
@item ssim (@emph{ssim})
@end table

@item quality, deadline (@emph{deadline})
@table @samp
@item best
Use best quality deadline. Poorly named and quite slow, this option should be
avoided as it may give worse quality output than good.
@item good
Use good quality deadline. This is a good trade-off between speed and quality
when used with the @option{cpu-used} option.
@item realtime
Use realtime quality deadline.
@end table

@item speed, cpu-used (@emph{cpu-used})
Set quality/speed ratio modifier. Higher values speed up the encode at the cost
of quality.

@item nr (@emph{noise-sensitivity})

@item static-thresh
Set a change threshold on blocks below which they will be skipped by the
encoder.

@item slices (@emph{token-parts})
Note that FFmpeg's @option{slices} option gives the total number of partitions,
while @command{vpxenc}'s @option{token-parts} is given as
@code{log2(partitions)}.

@item max-intra-rate
Set maximum I-frame bitrate as a percentage of the target bitrate. A value of 0
means unlimited.

@item force_key_frames
@code{VPX_EFLAG_FORCE_KF}

@item Alternate reference frame related
@table @option
@item auto-alt-ref
Enable use of alternate reference frames (2-pass only).
Values greater than 1 enable multi-layer alternate reference frames (VP9 only).
@item arnr-maxframes
Set altref noise reduction max frame count.
@item arnr-type
Set altref noise reduction filter type: backward, forward, centered.
@item arnr-strength
Set altref noise reduction filter strength.
@item rc-lookahead, lag-in-frames (@emph{lag-in-frames})
Set number of frames to look ahead for frametype and ratecontrol.
@item min-gf-interval
Set minimum golden/alternate reference frame interval (VP9 only).
@end table

@item error-resilient
Enable error resiliency features.

@item sharpness @var{integer}
Increase sharpness at the expense of lower PSNR.
The valid range is [0, 7].

@item ts-parameters
Sets the temporal scalability configuration using a :-separated list of
key=value pairs. For example, to specify temporal scalability parameters
with @code{ffmpeg}:
@example
ffmpeg -i INPUT -c:v libvpx -ts-parameters ts_number_layers=3:\
ts_target_bitrate=250,500,1000:ts_rate_decimator=4,2,1:\
ts_periodicity=4:ts_layer_id=0,2,1,2:ts_layering_mode=3 OUTPUT
@end example
Below is a brief explanation of each of the parameters, please
refer to @code{struct vpx_codec_enc_cfg} in @code{vpx/vpx_encoder.h} for more
details.
@table @option
@item ts_number_layers
Number of temporal coding layers.
@item ts_target_bitrate
Target bitrate for each temporal layer (in kbps).
(bitrate should be inclusive of the lower temporal layer).
@item ts_rate_decimator
Frame rate decimation factor for each temporal layer.
@item ts_periodicity
Length of the sequence defining frame temporal layer membership.
@item ts_layer_id
Template defining the membership of frames to temporal layers.
@item ts_layering_mode
(optional) Selecting the temporal structure from a set of pre-defined temporal layering modes.
Currently supports the following options.
@table @option
@item 0
No temporal layering flags are provided internally,
relies on flags being passed in using @code{metadata} field in @code{AVFrame}
with following keys.
@table @option
@item vp8-flags
Sets the flags passed into the encoder to indicate the referencing scheme for
the current frame.
Refer to function @code{vpx_codec_encode} in @code{vpx/vpx_encoder.h} for more
details.
@item temporal_id
Explicitly sets the temporal id of the current frame to encode.
@end table
@item 2
Two temporal layers. 0-1...
@item 3
Three temporal layers. 0-2-1-2...; with single reference frame.
@item 4
Same as option "3", except there is a dependency between
the two temporal layer 2 frames within the temporal period.
@end table
@end table

@item VP8-specific options
@table @option
@item screen-content-mode
Screen content mode, one of: 0 (off), 1 (screen), 2 (screen with more aggressive rate control).
@end table

@item VP9-specific options
@table @option
@item lossless
Enable lossless mode.
@item tile-columns
Set number of tile columns to use. Note this is given as
@code{log2(tile_columns)}. For example, 8 tile columns would be requested by
setting the @option{tile-columns} option to 3.
@item tile-rows
Set number of tile rows to use. Note this is given as @code{log2(tile_rows)}.
For example, 4 tile rows would be requested by setting the @option{tile-rows}
option to 2.
@item frame-parallel
Enable frame parallel decodability features.
@item aq-mode
Set adaptive quantization mode (0: off (default), 1: variance 2: complexity, 3:
cyclic refresh, 4: equator360).
@item colorspace @emph{color-space}
Set input color space. The VP9 bitstream supports signaling the following
colorspaces:
@table @option
@item @samp{rgb} @emph{sRGB}
@item @samp{bt709} @emph{bt709}
@item @samp{unspecified} @emph{unknown}
@item @samp{bt470bg} @emph{bt601}
@item @samp{smpte170m} @emph{smpte170}
@item @samp{smpte240m} @emph{smpte240}
@item @samp{bt2020_ncl} @emph{bt2020}
@end table
@item row-mt @var{boolean}
Enable row based multi-threading.
@item tune-content
Set content type: default (0), screen (1), film (2).
@item corpus-complexity
Corpus VBR mode is a variant of standard VBR where the complexity distribution
midpoint is passed in rather than calculated for a specific clip or chunk.

The valid range is [0, 10000]. 0 (default) uses standard VBR.
@item enable-tpl @var{boolean}
Enable temporal dependency model.
@item ref-frame-config
Using per-frame metadata, set members of the structure @code{vpx_svc_ref_frame_config_t} in @code{vpx/vp8cx.h} to fine-control referencing schemes and frame buffer management.
@*Use a :-separated list of key=value pairs.
For example,
@example
av_dict_set(&av_frame->metadata, "ref-frame-config", \
"rfc_update_buffer_slot=7:rfc_lst_fb_idx=0:rfc_gld_fb_idx=1:rfc_alt_fb_idx=2:rfc_reference_last=0:rfc_reference_golden=0:rfc_reference_alt_ref=0");
@end example
@table @option
@item rfc_update_buffer_slot
Indicates the buffer slot number to update
@item rfc_update_last
Indicates whether to update the LAST frame
@item rfc_update_golden
Indicates whether to update GOLDEN frame
@item rfc_update_alt_ref
Indicates whether to update ALT_REF frame
@item rfc_lst_fb_idx
LAST frame buffer index
@item rfc_gld_fb_idx
GOLDEN frame buffer index
@item rfc_alt_fb_idx
ALT_REF frame buffer index
@item rfc_reference_last
Indicates whether to reference LAST frame
@item rfc_reference_golden
Indicates whether to reference GOLDEN frame
@item rfc_reference_alt_ref
Indicates whether to reference ALT_REF frame
@item rfc_reference_duration
Indicates frame duration
@end table
@end table

@end table

For more information about libvpx see:
@url{http://www.webmproject.org/}

@section libvvenc

VVenC H.266/VVC encoder wrapper.

This encoder requires the presence of the libvvenc headers and library
during configuration. You need to explicitly configure the build with
@option{--enable-libvvenc}.

The VVenC project website is at
@url{https://github.com/fraunhoferhhi/vvenc}.

@subsection Supported Pixel Formats

VVenC supports only 10-bit color spaces as input. But the internal (encoded)
bit depth can be set to 8-bit or 10-bit at runtime.

@subsection Options

@table @option
@item b
Sets target video bitrate.

@item g
Set the GOP size. Currently support for g=1 (Intra only) or default.

@item preset
Set the VVenC preset.

@item levelidc
Set level idc.

@item tier
Set vvc tier.

@item qp
Set constant quantization parameter.

@item subopt @var{boolean}
Set subjective (perceptually motivated) optimization. Default is 1 (on).

@item bitdepth8 @var{boolean}
Set 8bit coding mode instead of using 10bit. Default is 0 (off).

@item period
set (intra) refresh period in seconds.

@item vvenc-params
Set vvenc options using a list of @var{key}=@var{value} couples separated
by ":". See @command{vvencapp --fullhelp} or @command{vvencFFapp --fullhelp} for a list of options.

For example, the options might be provided as:

@example
intraperiod=64:decodingrefreshtype=idr:poc0idr=1:internalbitdepth=8
@end example

For example the encoding options might be provided with @option{-vvenc-params}:

@example
ffmpeg -i input -c:v libvvenc -b 1M -vvenc-params intraperiod=64:decodingrefreshtype=idr:poc0idr=1:internalbitdepth=8 output.mp4
@end example

@end table

@section libwebp

libwebp WebP Image encoder wrapper

libwebp is Google's official encoder for WebP images. It can encode in either
lossy or lossless mode. Lossy images are essentially a wrapper around a VP8
frame. Lossless images are a separate codec developed by Google.

@subsection Pixel Format

Currently, libwebp only supports YUV420 for lossy and RGB for lossless due
to limitations of the format and libwebp. Alpha is supported for either mode.
Because of API limitations, if RGB is passed in when encoding lossy or YUV is
passed in for encoding lossless, the pixel format will automatically be
converted using functions from libwebp. This is not ideal and is done only for
convenience.

@subsection Options

@table @option

@item -lossless @var{boolean}
Enables/Disables use of lossless mode. Default is 0.

@item -compression_level @var{integer}
For lossy, this is a quality/speed tradeoff. Higher values give better quality
for a given size at the cost of increased encoding time. For lossless, this is
a size/speed tradeoff. Higher values give smaller size at the cost of increased
encoding time. More specifically, it controls the number of extra algorithms
and compression tools used, and varies the combination of these tools. This
maps to the @var{method} option in libwebp. The valid range is 0 to 6.
Default is 4.

@item -quality @var{float}
For lossy encoding, this controls image quality. For lossless encoding, this
controls the effort and time spent in compression.
Range is 0 to 100. Default is 75.

@item -preset @var{type}
Configuration preset. This does some automatic settings based on the general
type of the image.
@table @option
@item none
Do not use a preset.
@item default
Use the encoder default.
@item picture
Digital picture, like portrait, inner shot
@item photo
Outdoor photograph, with natural lighting
@item drawing
Hand or line drawing, with high-contrast details
@item icon
Small-sized colorful images
@item text
Text-like
@end table

@end table

@section libx264, libx264rgb

x264 H.264/MPEG-4 AVC encoder wrapper.

This encoder requires the presence of the libx264 headers and library
during configuration. You need to explicitly configure the build with
@code{--enable-libx264}.

libx264 supports an impressive number of features, including 8x8 and
4x4 adaptive spatial transform, adaptive B-frame placement, CAVLC/CABAC
entropy coding, interlacing (MBAFF), lossless mode, psy optimizations
for detail retention (adaptive quantization, psy-RD, psy-trellis).

Many libx264 encoder options are mapped to FFmpeg global codec
options, while unique encoder options are provided through private
options. Additionally the @option{x264opts} and @option{x264-params}
private options allows one to pass a list of key=value tuples as accepted
by the libx264 @code{x264_param_parse} function.

The x264 project website is at
@url{http://www.videolan.org/developers/x264.html}.

The libx264rgb encoder is the same as libx264, except it accepts packed RGB
pixel formats as input instead of YUV.

@subsection Supported Pixel Formats

x264 supports 8- to 10-bit color spaces. The exact bit depth is controlled at
x264's configure time.

@subsection Options

The following options are supported by the libx264 wrapper. The
@command{x264}-equivalent options or values are listed in parentheses
for easy migration.

To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
@ref{codec-options,,the Codec Options chapter}.

To get a more accurate and extensive documentation of the libx264
options, invoke the command @command{x264 --fullhelp} or consult
the libx264 documentation.

In the list below, note that the @command{x264} option name is shown
in parentheses after the libavcodec corresponding name, in case there
is a direct mapping.

@table @option
@item b (@emph{bitrate})
Set bitrate in bits/s. Note that FFmpeg's @option{b} option is
expressed in bits/s, while @command{x264}'s @option{bitrate} is in
kilobits/s.

@item bf (@emph{bframes})
Number of B-frames between I and P-frames

@item g (@emph{keyint})
Maximum GOP size

@item qmin (@emph{qpmin})
Minimum quantizer scale

@item qmax (@emph{qpmax})
Maximum quantizer scale

@item qdiff (@emph{qpstep})
Maximum difference between quantizer scales

@item qblur (@emph{qblur})
Quantizer curve blur

@item qcomp (@emph{qcomp})
Quantizer curve compression factor

@item refs (@emph{ref})
Number of reference frames each P-frame can use. The range is @var{0-16}.

@item level (@emph{level})
Set the @code{x264_param_t.i_level_idc} value in case the value is
positive, it is ignored otherwise.

This value can be set using the @code{AVCodecContext} API (e.g. by
setting the @code{AVCodecContext} value directly), and is specified as
an integer mapped on a corresponding level (e.g. the value 31 maps
to H.264 level IDC "3.1", as defined in the @code{x264_levels}
table). It is ignored when set to a non positive value.

Alternatively it can be set as a private option, overriding the value
set in @code{AVCodecContext}, and in this case must be specified as
the level IDC identifier (e.g. "3.1"), as defined by H.264 Annex A.

@item sc_threshold (@emph{scenecut})
Sets the threshold for the scene change detection.

@item trellis (@emph{trellis})
Performs Trellis quantization to increase efficiency. Enabled by default.

@item nr (@emph{nr})
Noise reduction

@item me_range (@emph{merange})
Maximum range of the motion search in pixels.

@item me_method (@emph{me})
Set motion estimation method. Possible values in the decreasing order
of speed:

@table @samp
@item dia (@emph{dia})
@item epzs (@emph{dia})
Diamond search with radius 1 (fastest). @samp{epzs} is an alias for
@samp{dia}.
@item hex (@emph{hex})
Hexagonal search with radius 2.
@item umh (@emph{umh})
Uneven multi-hexagon search.
@item esa (@emph{esa})
Exhaustive search.
@item tesa (@emph{tesa})
Hadamard exhaustive search (slowest).
@end table

@item forced-idr
Normally, when forcing a I-frame type, the encoder can select any type
of I-frame. This option forces it to choose an IDR-frame.

@item subq (@emph{subme})
Sub-pixel motion estimation method.

@item b_strategy (@emph{b-adapt})
Adaptive B-frame placement decision algorithm. Use only on first-pass.

@item keyint_min (@emph{min-keyint})
Minimum GOP size.

@item coder
Set entropy encoder. Possible values:

@table @samp
@item ac
Enable CABAC.

@item vlc
Enable CAVLC and disable CABAC. It generates the same effect as
@command{x264}'s @option{--no-cabac} option.
@end table

@item cmp
Set full pixel motion estimation comparison algorithm. Possible values:

@table @samp
@item chroma
Enable chroma in motion estimation.

@item sad
Ignore chroma in motion estimation. It generates the same effect as
@command{x264}'s @option{--no-chroma-me} option.
@end table

@item threads (@emph{threads})
Number of encoding threads.

@item thread_type
Set multithreading technique. Possible values:

@table @samp
@item slice
Slice-based multithreading. It generates the same effect as
@command{x264}'s @option{--sliced-threads} option.
@item frame
Frame-based multithreading.
@end table

@item flags
Set encoding flags. It can be used to disable closed GOP and enable
open GOP by setting it to @code{-cgop}. The result is similar to
the behavior of @command{x264}'s @option{--open-gop} option.

@item rc_init_occupancy (@emph{vbv-init})
Initial VBV buffer occupancy

@item preset (@emph{preset})
Set the encoding preset.

@item tune (@emph{tune})
Set tuning of the encoding params.

@item profile (@emph{profile})
Set profile restrictions.

@item fastfirstpass
Enable fast settings when encoding first pass, when set to 1. When set
to 0, it has the same effect of @command{x264}'s
@option{--slow-firstpass} option.

@item crf (@emph{crf})
Set the quality for constant quality mode.

@item crf_max (@emph{crf-max})
In CRF mode, prevents VBV from lowering quality beyond this point.

@item qp (@emph{qp})
Set constant quantization rate control method parameter.

@item aq-mode (@emph{aq-mode})
Set AQ method. Possible values:

@table @samp
@item none (@emph{0})
Disabled.

@item variance (@emph{1})
Variance AQ (complexity mask).

@item autovariance (@emph{2})
Auto-variance AQ (experimental).
@end table

@item aq-strength (@emph{aq-strength})
Set AQ strength, reduce blocking and blurring in flat and textured areas.

@item psy
Use psychovisual optimizations when set to 1. When set to 0, it has the
same effect as @command{x264}'s @option{--no-psy} option.

@item psy-rd (@emph{psy-rd})
Set strength of psychovisual optimization, in
@var{psy-rd}:@var{psy-trellis} format.

@item rc-lookahead (@emph{rc-lookahead})
Set number of frames to look ahead for frametype and ratecontrol.

@item weightb
Enable weighted prediction for B-frames when set to 1. When set to 0,
it has the same effect as @command{x264}'s @option{--no-weightb} option.

@item weightp (@emph{weightp})
Set weighted prediction method for P-frames. Possible values:

@table @samp
@item none (@emph{0})
Disabled
@item simple (@emph{1})
Enable only weighted refs
@item smart (@emph{2})
Enable both weighted refs and duplicates
@end table

@item ssim (@emph{ssim})
Enable calculation and printing SSIM stats after the encoding.

@item intra-refresh (@emph{intra-refresh})
Enable the use of Periodic Intra Refresh instead of IDR frames when set
to 1.

@item avcintra-class (@emph{class})
Configure the encoder to generate AVC-Intra.
Valid values are 50, 100 and 200

@item bluray-compat (@emph{bluray-compat})
Configure the encoder to be compatible with the bluray standard.
It is a shorthand for setting "bluray-compat=1 force-cfr=1".

@item b-bias (@emph{b-bias})
Set the influence on how often B-frames are used.

@item b-pyramid (@emph{b-pyramid})
Set method for keeping of some B-frames as references. Possible values:

@table @samp
@item none (@emph{none})
Disabled.
@item strict (@emph{strict})
Strictly hierarchical pyramid.
@item normal (@emph{normal})
Non-strict (not Blu-ray compatible).
@end table

@item mixed-refs
Enable the use of one reference per partition, as opposed to one
reference per macroblock when set to 1. When set to 0, it has the
same effect as @command{x264}'s @option{--no-mixed-refs} option.

@item 8x8dct
Enable adaptive spatial transform (high profile 8x8 transform)
when set to 1. When set to 0, it has the same effect as
@command{x264}'s @option{--no-8x8dct} option.

@item fast-pskip
Enable early SKIP detection on P-frames when set to 1. When set
to 0, it has the same effect as @command{x264}'s
@option{--no-fast-pskip} option.

@item aud (@emph{aud})
Enable use of access unit delimiters when set to 1.

@item mbtree
Enable use macroblock tree ratecontrol when set to 1. When set
to 0, it has the same effect as @command{x264}'s
@option{--no-mbtree} option.

@item deblock (@emph{deblock})
Set loop filter parameters, in @var{alpha}:@var{beta} form.

@item cplxblur (@emph{cplxblur})
Set fluctuations reduction in QP (before curve compression).

@item partitions (@emph{partitions})
Set partitions to consider as a comma-separated list of values.
Possible values in the list:

@table @samp
@item p8x8
8x8 P-frame partition.
@item p4x4
4x4 P-frame partition.
@item b8x8
4x4 B-frame partition.
@item i8x8
8x8 I-frame partition.
@item i4x4
4x4 I-frame partition.
(Enabling @samp{p4x4} requires @samp{p8x8} to be enabled. Enabling
@samp{i8x8} requires adaptive spatial transform (@option{8x8dct}
option) to be enabled.)
@item none (@emph{none})
Do not consider any partitions.
@item all (@emph{all})
Consider every partition.
@end table

@item direct-pred (@emph{direct})
Set direct MV prediction mode. Possible values:

@table @samp
@item none (@emph{none})
Disable MV prediction.
@item spatial (@emph{spatial})
Enable spatial predicting.
@item temporal (@emph{temporal})
Enable temporal predicting.
@item auto (@emph{auto})
Automatically decided.
@end table

@item slice-max-size (@emph{slice-max-size})
Set the limit of the size of each slice in bytes. If not specified
but RTP payload size (@option{ps}) is specified, that is used.

@item stats (@emph{stats})
Set the file name for multi-pass stats.

@item nal-hrd (@emph{nal-hrd})
Set signal HRD information (requires @option{vbv-bufsize} to be set).
Possible values:

@table @samp
@item none (@emph{none})
Disable HRD information signaling.
@item vbr (@emph{vbr})
Variable bit rate.
@item cbr (@emph{cbr})
Constant bit rate (not allowed in MP4 container).
@end table

@item x264opts @var{opts}
@item x264-params @var{opts}
Override the x264 configuration using a :-separated list of key=value
options.

The argument for both options is a list of @var{key}=@var{value}
couples separated by ":". With @option{x264opts} the value can be
omitted, and the value @code{1} is assumed in that case.

For @var{filter} and @var{psy-rd} options values that use ":" as a
separator themselves, use "," instead. They accept it as well since
long ago but this is kept undocumented for some reason.

For example, the options might be provided as:
@example
level=30:bframes=0:weightp=0:cabac=0:ref=1:vbv-maxrate=768:vbv-bufsize=2000:analyse=all:me=umh:no-fast-pskip=1:subq=6:8x8dct=0:trellis=0
@end example

For example to specify libx264 encoding options with @command{ffmpeg}:
@example
ffmpeg -i foo.mpg -c:v libx264 -x264opts keyint=123:min-keyint=20 -an out.mkv
@end example

To get the complete list of the libx264 options, invoke the command
@command{x264 --fullhelp} or consult the libx264 documentation.

@item a53cc @var{boolean}
Import closed captions (which must be ATSC compatible format) into output.
Only the mpeg2 and h264 decoders provide these. Default is 1 (on).

@item udu_sei @var{boolean}
Import user data unregistered SEI if available into output. Default is 0 (off).

@item mb_info @var{boolean}
Set mb_info data through AVFrameSideData, only useful when used from the
API. Default is 0 (off).
@end table

Encoding ffpresets for common usages are provided so they can be used with the
general presets system (e.g. passing the @option{pre} option).

@section libx265

x265 H.265/HEVC encoder wrapper.

This encoder requires the presence of the libx265 headers and library
during configuration. You need to explicitly configure the build with
@option{--enable-libx265}.

@subsection Options

@table @option
@item b
Sets target video bitrate.

@item bf

@item g
Set the GOP size.

@item keyint_min
Minimum GOP size.

@item refs
Number of reference frames each P-frame can use. The range is from @var{1-16}.

@item preset
Set the x265 preset.

@item tune
Set the x265 tune parameter.

@item profile
Set profile restrictions.

@item crf
Set the quality for constant quality mode.

@item qp
Set constant quantization rate control method parameter.

@item qmin
Minimum quantizer scale.

@item qmax
Maximum quantizer scale.

@item qdiff
Maximum difference between quantizer scales.

@item qblur
Quantizer curve blur

@item qcomp
Quantizer curve compression factor

@item i_qfactor

@item b_qfactor

@item forced-idr
Normally, when forcing a I-frame type, the encoder can select any type
of I-frame. This option forces it to choose an IDR-frame.

@item udu_sei @var{boolean}
Import user data unregistered SEI if available into output. Default is 0 (off).

@item x265-params
Set x265 options using a list of @var{key}=@var{value} couples separated
by ":". See @command{x265 --help} for a list of options.

For example to specify libx265 encoding options with @option{-x265-params}:

@example
ffmpeg -i input -c:v libx265 -x265-params crf=26:psy-rd=1 output.mp4
@end example
@end table

@section libxavs2

xavs2 AVS2-P2/IEEE1857.4 encoder wrapper.

This encoder requires the presence of the libxavs2 headers and library
during configuration. You need to explicitly configure the build with
@option{--enable-libxavs2}.

The following standard libavcodec options are used:
@itemize
@item
@option{b} / @option{bit_rate}
@item
@option{g} / @option{gop_size}
@item
@option{bf} / @option{max_b_frames}
@end itemize

The encoder also has its own specific options:
@subsection Options

@table @option
@item lcu_row_threads
Set the number of parallel threads for rows from 1 to 8 (default 5).

@item initial_qp
Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the initial qp for the first frame.

@item qp
Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the qp value under constant-QP mode.

@item max_qp
Set the max qp for rate control from 1 to 63 (default 55).

@item min_qp
Set the min qp for rate control from 1 to 63 (default 20).

@item speed_level
Set the Speed level from 0 to 9 (default 0). Higher is better but slower.

@item log_level
Set the log level from -1 to 3 (default 0). -1: none, 0: error,
1: warning, 2: info, 3: debug.

@item xavs2-params
Set xavs2 options using a list of @var{key}=@var{value} couples separated
by ":".

For example to specify libxavs2 encoding options with @option{-xavs2-params}:

@example
ffmpeg -i input -c:v libxavs2 -xavs2-params RdoqLevel=0 output.avs2
@end example
@end table

@section libxeve

eXtra-fast Essential Video Encoder (XEVE) MPEG-5 EVC encoder wrapper.
The xeve-equivalent options or values are listed in parentheses for easy migration.

This encoder requires the presence of the libxeve headers and library
during configuration. You need to explicitly configure the build with
@option{--enable-libxeve}.

@float NOTE
Many libxeve encoder options are mapped to FFmpeg global codec options,
while unique encoder options are provided through private options.
Additionally the xeve-params private options allows one to pass a list
of key=value tuples as accepted by the libxeve @code{parse_xeve_params} function.
@end float

The xeve project website is at @url{https://github.com/mpeg5/xeve}.

@subsection Options

The following options are supported by the libxeve wrapper.
The xeve-equivalent options or values are listed in parentheses for easy migration.

@float NOTE
To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
@ref{codec-options,,the Codec Options chapter}.
@end float

@float NOTE
To get a more accurate and extensive documentation of the libxeve options,
invoke the command  @code{xeve_app --help} or consult the libxeve documentation.
@end float

@table @option
@item b (@emph{bitrate})
Set target video bitrate in bits/s.
Note that FFmpeg's b option is expressed in bits/s, while xeve's bitrate is in kilobits/s.

@item bf (@emph{bframes})
Set the maximum number of B frames (1,3,7,15).

@item g (@emph{keyint})
Set the GOP size (I-picture period).

@item preset (@emph{preset})
Set the xeve preset.
Set the encoder preset value to determine encoding speed [fast, medium, slow, placebo]

@item tune (@emph{tune})
Set the encoder tune parameter [psnr, zerolatency]

@item profile (@emph{profile})
Set the encoder profile [0: baseline; 1: main]

@item crf (@emph{crf})
Set the quality for constant quality mode.
Constant rate factor <10..49> [default: 32]

@item qp (@emph{qp})
Set constant quantization rate control method parameter.
Quantization parameter qp <0..51> [default: 32]

@item threads (@emph{threads})
Force to use a specific number of threads

@end table

@section libxvid

Xvid MPEG-4 Part 2 encoder wrapper.

This encoder requires the presence of the libxvidcore headers and library
during configuration. You need to explicitly configure the build with
@code{--enable-libxvid --enable-gpl}.

The native @code{mpeg4} encoder supports the MPEG-4 Part 2 format, so
users can encode to this format without this library.

@subsection Options

The following options are supported by the libxvid wrapper. Some of
the following options are listed but are not documented, and
correspond to shared codec options. See @ref{codec-options,,the Codec
Options chapter} for their documentation. The other shared options
which are not listed have no effect for the libxvid encoder.

@table @option
@item b

@item g

@item qmin

@item qmax

@item mpeg_quant

@item threads

@item bf

@item b_qfactor

@item b_qoffset

@item flags
Set specific encoding flags. Possible values:

@table @samp

@item mv4
Use four motion vector by macroblock.

@item aic
Enable high quality AC prediction.

@item gray
Only encode grayscale.

@item qpel
Enable quarter-pixel motion compensation.

@item cgop
Enable closed GOP.

@item global_header
Place global headers in extradata instead of every keyframe.

@end table

@item gmc
Enable the use of global motion compensation (GMC).  Default is 0
(disabled).

@item me_quality
Set motion estimation quality level. Possible values in decreasing order of
speed and increasing order of quality:

@table @samp
@item 0
Use no motion estimation (default).

@item 1, 2
Enable advanced diamond zonal search for 16x16 blocks and half-pixel
refinement for 16x16 blocks.

@item 3, 4
Enable all of the things described above, plus advanced diamond zonal
search for 8x8 blocks and half-pixel refinement for 8x8 blocks, also
enable motion estimation on chroma planes for P and B-frames.

@item 5, 6
Enable all of the things described above, plus extended 16x16 and 8x8
blocks search.
@end table

@item mbd
Set macroblock decision algorithm. Possible values in the increasing
order of quality:

@table @samp
@item simple
Use macroblock comparing function algorithm (default).

@item bits
Enable rate distortion-based half pixel and quarter pixel refinement for
16x16 blocks.

@item rd
Enable all of the things described above, plus rate distortion-based
half pixel and quarter pixel refinement for 8x8 blocks, and rate
distortion-based search using square pattern.
@end table

@item lumi_aq
Enable lumi masking adaptive quantization when set to 1. Default is 0
(disabled).

@item variance_aq
Enable variance adaptive quantization when set to 1. Default is 0
(disabled).

When combined with @option{lumi_aq}, the resulting quality will not
be better than any of the two specified individually. In other
words, the resulting quality will be the worse one of the two
effects.

@item trellis
Set rate-distortion optimal quantization.

@item ssim
Set structural similarity (SSIM) displaying method. Possible values:

@table @samp
@item off
Disable displaying of SSIM information.

@item avg
Output average SSIM at the end of encoding to stdout. The format of
showing the average SSIM is:

@example
Average SSIM: %f
@end example

For users who are not familiar with C, %f means a float number, or
a decimal (e.g. 0.939232).

@item frame
Output both per-frame SSIM data during encoding and average SSIM at
the end of encoding to stdout. The format of per-frame information
is:

@example
       SSIM: avg: %1.3f min: %1.3f max: %1.3f
@end example

For users who are not familiar with C, %1.3f means a float number
rounded to 3 digits after the dot (e.g. 0.932).

@end table

@item ssim_acc
Set SSIM accuracy. Valid options are integers within the range of
0-4, while 0 gives the most accurate result and 4 computes the
fastest.

@end table

@section MediaFoundation

This provides wrappers to encoders (both audio and video) in the
MediaFoundation framework. It can access both SW and HW encoders.
Video encoders can take input in either of nv12 or yuv420p form
(some encoders support both, some support only either - in practice,
nv12 is the safer choice, especially among HW encoders).

@section Microsoft RLE

Microsoft RLE aka MSRLE encoder.
Only 8-bit palette mode supported.
Compatible with Windows 3.1 and Windows 95.

@subsection Options

@table @option
@item g @var{integer}
Keyframe interval.
A keyframe is inserted at least every @code{-g} frames, sometimes sooner.
@end table

@section mpeg2

MPEG-2 video encoder.

@subsection Options

@table @option
@item profile
Select the mpeg2 profile to encode:

@table @samp
@item 422
@item high
@item ss
Spatially Scalable
@item snr
SNR Scalable
@item main
@item simple
@end table

@item level
Select the mpeg2 level to encode:

@table @samp
@item high
@item high1440
@item main
@item low
@end table

@item seq_disp_ext @var{integer}
Specifies if the encoder should write a sequence_display_extension to the
output.
@table @option
@item -1
@itemx auto
Decide automatically to write it or not (this is the default) by checking if
the data to be written is different from the default or unspecified values.
@item 0
@itemx never
Never write it.
@item 1
@itemx always
Always write it.
@end table
@item video_format @var{integer}
Specifies the video_format written into the sequence display extension
indicating the source of the video pictures. The default is @samp{unspecified},
can be @samp{component}, @samp{pal}, @samp{ntsc}, @samp{secam} or @samp{mac}.
For maximum compatibility, use @samp{component}.
@item a53cc @var{boolean}
Import closed captions (which must be ATSC compatible format) into output.
Default is 1 (on).
@end table

@section png

PNG image encoder.

@subsection Private options

@table @option
@item dpi @var{integer}
Set physical density of pixels, in dots per inch, unset by default
@item dpm @var{integer}
Set physical density of pixels, in dots per meter, unset by default
@end table

@section ProRes

Apple ProRes encoder.

FFmpeg contains 2 ProRes encoders, the prores-aw and prores-ks encoder.
The used encoder can be chosen with the @code{-vcodec} option.

@subsection Private Options for prores-ks

@table @option
@item profile @var{integer}
Select the ProRes profile to encode
@table @samp
@item proxy
@item lt
@item standard
@item hq
@item 4444
@item 4444xq
@end table

@item quant_mat @var{integer}
Select quantization matrix.
@table @samp
@item auto
@item default
@item proxy
@item lt
@item standard
@item hq
@end table
If set to @var{auto}, the matrix matching the profile will be picked.
If not set, the matrix providing the highest quality, @var{default}, will be
picked.

@item bits_per_mb @var{integer}
How many bits to allot for coding one macroblock. Different profiles use
between 200 and 2400 bits per macroblock, the maximum is 8000.

@item mbs_per_slice @var{integer}
Number of macroblocks in each slice (1-8); the default value (8)
should be good in almost all situations.

@item vendor @var{string}
Override the 4-byte vendor ID.
A custom vendor ID like @var{apl0} would claim the stream was produced by
the Apple encoder.

@item alpha_bits @var{integer}
Specify number of bits for alpha component.
Possible values are @var{0}, @var{8} and @var{16}.
Use @var{0} to disable alpha plane coding.

@end table

@subsection Speed considerations

In the default mode of operation the encoder has to honor frame constraints
(i.e. not produce frames with size bigger than requested) while still making
output picture as good as possible.
A frame containing a lot of small details is harder to compress and the encoder
would spend more time searching for appropriate quantizers for each slice.

Setting a higher @option{bits_per_mb} limit will improve the speed.

For the fastest encoding speed set the @option{qscale} parameter (4 is the
recommended value) and do not set a size constraint.

@section QSV Encoders

The family of Intel QuickSync Video encoders (MPEG-2, H.264, HEVC, JPEG/MJPEG,
VP9, AV1)

@subsection Ratecontrol Method
The ratecontrol method is selected as follows:
@itemize @bullet
@item
When @option{global_quality} is specified, a quality-based mode is used.
Specifically this means either
@itemize @minus
@item
@var{CQP} - constant quantizer scale, when the @option{qscale} codec flag is
also set (the @option{-qscale} ffmpeg option).

@item
@var{LA_ICQ} - intelligent constant quality with lookahead, when the
@option{look_ahead} option is also set.

@item
@var{ICQ} -- intelligent constant quality otherwise. For the ICQ modes, global
quality range is 1 to 51, with 1 being the best quality.
@end itemize

@item
Otherwise when the desired average bitrate is specified with the @option{b}
option, a bitrate-based mode is used.
@itemize @minus
@item
@var{LA} - VBR with lookahead, when the @option{look_ahead} option is specified.

@item
@var{VCM} - video conferencing mode, when the @option{vcm} option is set.

@item
@var{CBR} - constant bitrate, when @option{maxrate} is specified and equal to
the average bitrate.

@item
@var{VBR} - variable bitrate, when @option{maxrate} is specified, but is higher
than the average bitrate.

@item
@var{AVBR} - average VBR mode, when @option{maxrate} is not specified, both
@option{avbr_accuracy} and @option{avbr_convergence} are set to non-zero. This
mode is available for H264 and HEVC on Windows.
@end itemize

@item
Otherwise the default ratecontrol method @var{CQP} is used.
@end itemize

Note that depending on your system, a different mode than the one you specified
may be selected by the encoder. Set the verbosity level to @var{verbose} or
higher to see the actual settings used by the QSV runtime.

@subsection Global Options -> MSDK Options
Additional libavcodec global options are mapped to MSDK options as follows:

@itemize
@item
@option{g/gop_size} -> @option{GopPicSize}

@item
@option{bf/max_b_frames}+1 -> @option{GopRefDist}

@item
@option{rc_init_occupancy/rc_initial_buffer_occupancy} ->
@option{InitialDelayInKB}

@item
@option{slices} -> @option{NumSlice}

@item
@option{refs} -> @option{NumRefFrame}

@item
@option{b_strategy/b_frame_strategy} -> @option{BRefType}

@item
@option{cgop/CLOSED_GOP} codec flag -> @option{GopOptFlag}

@item
For the @var{CQP} mode, the @option{i_qfactor/i_qoffset} and
@option{b_qfactor/b_qoffset} set the difference between @var{QPP} and @var{QPI},
and @var{QPP} and @var{QPB} respectively.

@item
Setting the @option{coder} option to the value @var{vlc} will make the H.264
encoder use CAVLC instead of CABAC.

@end itemize

@subsection Common Options
Following options are used by all qsv encoders.

@table @option
@item @var{async_depth}
Specifies how many asynchronous operations an application performs
before the application explicitly synchronizes the result. If zero,
the value is not specified.

@item @var{preset}
This option itemizes a range of choices from veryfast (best speed) to veryslow
(best quality).
@table @samp
@item veryfast
@item faster
@item fast
@item medium
@item slow
@item slower
@item veryslow
@end table

@item @var{forced_idr}
Forcing I frames as IDR frames.

@item @var{low_power}
For encoders set this flag to ON to reduce power consumption and GPU usage.
@end table

@subsection Runtime Options
Following options can be used durning qsv encoding.

@table @option
@item @var{global_quality}
@item @var{i_quant_factor}
@item @var{i_quant_offset}
@item @var{b_quant_factor}
@item @var{b_quant_offset}
Supported in h264_qsv and hevc_qsv.
Change these value to reset qsv codec's qp configuration.

@item @var{max_frame_size}
Supported in h264_qsv and hevc_qsv.
Change this value to reset qsv codec's MaxFrameSize configuration.

@item @var{gop_size}
Change this value to reset qsv codec's gop configuration.

@item @var{int_ref_type}
@item @var{int_ref_cycle_size}
@item @var{int_ref_qp_delta}
@item @var{int_ref_cycle_dist}
Supported in h264_qsv and hevc_qsv.
Change these value to reset qsv codec's Intra Refresh configuration.

@item @var{qmax}
@item @var{qmin}
@item @var{max_qp_i}
@item @var{min_qp_i}
@item @var{max_qp_p}
@item @var{min_qp_p}
@item @var{max_qp_b}
@item @var{min_qp_b}
Supported in h264_qsv.
Change these value to reset qsv codec's max/min qp configuration.

@item @var{low_delay_brc}
Supported in h264_qsv, hevc_qsv and av1_qsv.
Change this value to reset qsv codec's low_delay_brc configuration.

@item @var{framerate}
Change this value to reset qsv codec's framerate configuration.

@item @var{bit_rate}
@item @var{rc_buffer_size}
@item @var{rc_initial_buffer_occupancy}
@item @var{rc_max_rate}
Change these value to reset qsv codec's bitrate control configuration.

@item @var{pic_timing_sei}
Supported in h264_qsv and hevc_qsv.
Change this value to reset qsv codec's pic_timing_sei configuration.

@item @var{qsv_params}
Set QSV encoder parameters as a colon-separated list of key-value pairs.

The @option{qsv_params} should be formatted as @code{key1=value1:key2=value2:...}.

These parameters are passed directly to the underlying Intel Quick Sync Video (QSV) encoder using the MFXSetParameter function.

Example:
@example
ffmpeg -i input.mp4 -c:v h264_qsv -qsv_params "CodingOption1=1:CodingOption2=2" output.mp4
@end example

This option allows fine-grained control over various encoder-specific settings provided by the QSV encoder.
@end table

@subsection H264 options
These options are used by h264_qsv

@table @option
@item @var{extbrc}
Extended bitrate control.

@item @var{recovery_point_sei}
Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.

@item @var{rdo}
Enable rate distortion optimization.

@item @var{max_frame_size}
Maximum encoded frame size in bytes.

@item @var{max_frame_size_i}
Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.

@item @var{max_frame_size_p}
Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.

@item @var{max_slice_size}
Maximum encoded slice size in bytes.

@item @var{bitrate_limit}
Toggle bitrate limitations.
Modifies bitrate to be in the range imposed by the QSV encoder. Setting this
flag off may lead to violation of HRD conformance. Mind that specifying bitrate
below the QSV encoder range might significantly affect quality. If on this
option takes effect in non CQP modes: if bitrate is not in the range imposed
by the QSV encoder, it will be changed to be in the range.

@item @var{mbbrc}
Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.

@item @var{low_delay_brc}
Setting this flag turns on or off LowDelayBRC feautre in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on

@item @var{adaptive_i}
This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.

@item @var{adaptive_b}
This flag controls changing of frame type from B to P.

@item @var{p_strategy}
Enable P-pyramid: 0-default 1-simple 2-pyramid(bf need to be set to 0).

@item @var{b_strategy}
This option controls usage of B frames as reference.

@item @var{dblk_idc}
This option disable deblocking. It has value in range 0~2.

@item @var{cavlc}
If set, CAVLC is used; if unset, CABAC is used for encoding.

@item @var{vcm}
Video conferencing mode, please see ratecontrol method.

@item @var{idr_interval}
Distance (in I-frames) between IDR frames.

@item @var{pic_timing_sei}
Insert picture timing SEI with pic_struct_syntax element.

@item @var{single_sei_nal_unit}
Put all the SEI messages into one NALU.

@item @var{max_dec_frame_buffering}
Maximum number of frames buffered in the DPB.

@item @var{look_ahead}
Use VBR algorithm with look ahead.

@item @var{look_ahead_depth}
Depth of look ahead in number frames.

@item @var{look_ahead_downsampling}
Downscaling factor for the frames saved for the lookahead analysis.
@table @samp
@item unknown
@item auto
@item off
@item 2x
@item 4x
@end table

@item @var{int_ref_type}
Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. @var{none} means no refresh. @var{vertical} means
vertical refresh, by column of MBs. @var{horizontal} means horizontal refresh,
by rows of MBs. @var{slice} means horizontal refresh by slices without
overlapping. In case of @var{slice}, in_ref_cycle_size is ignored. To enable
intra refresh, B frame should be set to 0.

@item @var{int_ref_cycle_size}
Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.

@item @var{int_ref_qp_delta}
Specifies QP difference for inserted intra MBs. This is signed value in
[-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [-63, 63] for 10 bit-depth or [-75, 75] for 12 bit-depth respectively.

@item @var{int_ref_cycle_dist}
Distance between the beginnings of the intra-refresh cycles in frames.

@item @var{profile}
@table @samp
@item unknown
@item baseline
@item main
@item high
@end table

@item @var{a53cc}
Use A53 Closed Captions (if available).

@item @var{aud}
Insert the Access Unit Delimiter NAL.

@item @var{mfmode}
Multi-Frame Mode.
@table @samp
@item off
@item auto
@end table

@item @var{repeat_pps}
Repeat pps for every frame.

@item @var{max_qp_i}
Maximum video quantizer scale for I frame.

@item @var{min_qp_i}
Minimum video quantizer scale for I frame.

@item @var{max_qp_p}
Maximum video quantizer scale for P frame.

@item @var{min_qp_p}
Minimum video quantizer scale for P frame.

@item @var{max_qp_b}
Maximum video quantizer scale for B frame.

@item @var{min_qp_b}
Minimum video quantizer scale for B frame.

@item @var{scenario}
Provides a hint to encoder about the scenario for the encoding session.
@table @samp
@item unknown
@item displayremoting
@item videoconference
@item archive
@item livestreaming
@item cameracapture
@item videosurveillance
@item gamestreaming
@item remotegaming
@end table

@item @var{avbr_accuracy}
Accuracy of the AVBR ratecontrol (unit of tenth of percent).

@item @var{avbr_convergence}
Convergence of the AVBR ratecontrol (unit of 100 frames)

The parameters @var{avbr_accuracy} and @var{avbr_convergence} are for the
average variable bitrate control (AVBR) algorithm.
The algorithm focuses on overall encoding quality while meeting the specified
bitrate, @var{target_bitrate}, within the accuracy range @var{avbr_accuracy},
after a @var{avbr_Convergence} period. This method does not follow HRD and the
instant bitrate is not capped or padded.

@item @var{skip_frame}
Use per-frame metadata "qsv_skip_frame" to skip frame when encoding. This option
defines the usage of this metadata.
@table @samp
@item no_skip
Frame skipping is disabled.
@item insert_dummy
Encoder inserts into bitstream frame where all macroblocks are encoded as
skipped.
@item insert_nothing
Similar to insert_dummy, but encoder inserts nothing into bitstream. The skipped
frames are still used in brc. For example, gop still include skipped frames, and
the frames after skipped frames will be larger in size.
@item brc_only
skip_frame metadata indicates the number of missed frames before the current
frame.
@end table

@end table

@subsection HEVC Options
These options are used by hevc_qsv

@table @option
@item @var{extbrc}
Extended bitrate control.

@item @var{recovery_point_sei}
Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.

@item @var{rdo}
Enable rate distortion optimization.

@item @var{max_frame_size}
Maximum encoded frame size in bytes.

@item @var{max_frame_size_i}
Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.

@item @var{max_frame_size_p}
Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.

@item @var{max_slice_size}
Maximum encoded slice size in bytes.

@item @var{mbbrc}
Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.

@item @var{low_delay_brc}
Setting this flag turns on or off LowDelayBRC feautre in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on

@item @var{adaptive_i}
This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.

@item @var{adaptive_b}
This flag controls changing of frame type from B to P.

@item @var{p_strategy}
Enable P-pyramid: 0-default 1-simple 2-pyramid(bf need to be set to 0).

@item @var{b_strategy}
This option controls usage of B frames as reference.

@item @var{dblk_idc}
This option disable deblocking. It has value in range 0~2.

@item @var{idr_interval}
Distance (in I-frames) between IDR frames.
@table @samp
@item begin_only
Output an IDR-frame only at the beginning of the stream.
@end table

@item @var{load_plugin}
A user plugin to load in an internal session.
@table @samp
@item none
@item hevc_sw
@item hevc_hw
@end table

@item @var{load_plugins}
A :-separate list of hexadecimal plugin UIDs to load in
an internal session.

@item @var{look_ahead_depth}
Depth of look ahead in number frames, available when extbrc option is enabled.

@item @var{profile}
Set the encoding profile (scc requires libmfx >= 1.32).

@table @samp
@item unknown
@item main
@item main10
@item mainsp
@item rext
@item scc
@end table

@item @var{tier}
Set the encoding tier (only level >= 4 can support high tier).
This option only takes effect when the level option is specified.

@table @samp
@item main
@item high
@end table

@item @var{gpb}
1: GPB (generalized P/B frame)

0: regular P frame.

@item @var{tile_cols}
Number of columns for tiled encoding.

@item @var{tile_rows}
Number of rows for tiled encoding.

@item @var{aud}
Insert the Access Unit Delimiter NAL.

@item @var{pic_timing_sei}
Insert picture timing SEI with pic_struct_syntax element.

@item @var{transform_skip}
Turn this option ON to enable transformskip. It is supported on platform equal
or newer than ICL.

@item @var{int_ref_type}
Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. @var{none} means no refresh. @var{vertical} means
vertical refresh, by column of MBs. @var{horizontal} means horizontal refresh,
by rows of MBs. @var{slice} means horizontal refresh by slices without
overlapping. In case of @var{slice}, in_ref_cycle_size is ignored. To enable
intra refresh, B frame should be set to 0.

@item @var{int_ref_cycle_size}
Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.

@item @var{int_ref_qp_delta}
Specifies QP difference for inserted intra MBs. This is signed value in
[-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [-63, 63] for 10 bit-depth or [-75, 75] for 12 bit-depth respectively.

@item @var{int_ref_cycle_dist}
Distance between the beginnings of the intra-refresh cycles in frames.

@item @var{max_qp_i}
Maximum video quantizer scale for I frame.

@item @var{min_qp_i}
Minimum video quantizer scale for I frame.

@item @var{max_qp_p}
Maximum video quantizer scale for P frame.

@item @var{min_qp_p}
Minimum video quantizer scale for P frame.

@item @var{max_qp_b}
Maximum video quantizer scale for B frame.

@item @var{min_qp_b}
Minimum video quantizer scale for B frame.

@item @var{scenario}
Provides a hint to encoder about the scenario for the encoding session.
@table @samp
@item unknown
@item displayremoting
@item videoconference
@item archive
@item livestreaming
@item cameracapture
@item videosurveillance
@item gamestreaming
@item remotegaming
@end table

@item @var{avbr_accuracy}
Accuracy of the AVBR ratecontrol (unit of tenth of percent).

@item @var{avbr_convergence}
Convergence of the AVBR ratecontrol (unit of 100 frames)

The parameters @var{avbr_accuracy} and @var{avbr_convergence} are for the
average variable bitrate control (AVBR) algorithm.
The algorithm focuses on overall encoding quality while meeting the specified
bitrate, @var{target_bitrate}, within the accuracy range @var{avbr_accuracy},
after a @var{avbr_Convergence} period. This method does not follow HRD and the
instant bitrate is not capped or padded.

@item @var{skip_frame}
Use per-frame metadata "qsv_skip_frame" to skip frame when encoding. This option
defines the usage of this metadata.
@table @samp
@item no_skip
Frame skipping is disabled.
@item insert_dummy
Encoder inserts into bitstream frame where all macroblocks are encoded as
skipped.
@item insert_nothing
Similar to insert_dummy, but encoder inserts nothing into bitstream. The skipped
frames are still used in brc. For example, gop still include skipped frames, and
the frames after skipped frames will be larger in size.
@item brc_only
skip_frame metadata indicates the number of missed frames before the current
frame.
@end table

@end table

@subsection MPEG2 Options
These options are used by mpeg2_qsv
@table @option
@item @var{profile}
@table @samp
@item unknown
@item simple
@item main
@item high
@end table
@end table

@subsection VP9 Options
These options are used by vp9_qsv
@table @option
@item @var{profile}
@table @samp
@item unknown
@item profile0
@item profile1
@item profile2
@item profile3
@end table

@item @var{tile_cols}
Number of columns for tiled encoding (requires libmfx >= 1.29).

@item @var{tile_rows}
Number of rows for tiled encoding (requires libmfx  >= 1.29).
@end table

@subsection AV1 Options
These options are used by av1_qsv (requires libvpl).
@table @option
@item @var{profile}
@table @samp
@item unknown
@item main
@end table

@item @var{tile_cols}
Number of columns for tiled encoding.

@item @var{tile_rows}
Number of rows for tiled encoding.

@item @var{adaptive_i}
This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.

@item @var{adaptive_b}
This flag controls changing of frame type from B to P.

@item @var{b_strategy}
This option controls usage of B frames as reference.

@item @var{extbrc}
Extended bitrate control.

@item @var{look_ahead_depth}
Depth of look ahead in number frames, available when extbrc option is enabled.

@item @var{low_delay_brc}
Setting this flag turns on or off LowDelayBRC feautre in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on

@item @var{max_frame_size}
Set the allowed max size in bytes for each frame. If the frame size exceeds
the limitation, encoder will adjust the QP value to control the frame size.
Invalid in CQP rate control mode.

@item @var{max_frame_size_i}
Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.

@item @var{max_frame_size_p}
Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.
@end table

@section snow

@subsection Options

@table @option
@item iterative_dia_size
dia size for the iterative motion estimation
@end table

@section VAAPI encoders

Wrappers for hardware encoders accessible via VAAPI.

These encoders only accept input in VAAPI hardware surfaces.  If you have input
in software frames, use the @option{hwupload} filter to upload them to the GPU.

The following standard libavcodec options are used:
@itemize
@item
@option{g} / @option{gop_size}
@item
@option{bf} / @option{max_b_frames}
@item
@option{profile}

If not set, this will be determined automatically from the format of the input
frames and the profiles supported by the driver.
@item
@option{level}
@item
@option{b} / @option{bit_rate}
@item
@option{maxrate} / @option{rc_max_rate}
@item
@option{bufsize} / @option{rc_buffer_size}
@item
@option{rc_init_occupancy} / @option{rc_initial_buffer_occupancy}
@item
@option{compression_level}

Speed / quality tradeoff: higher values are faster / worse quality.
@item
@option{q} / @option{global_quality}

Size / quality tradeoff: higher values are smaller / worse quality.
@item
@option{qmin}
@item
@option{qmax}
@item
@option{i_qfactor} / @option{i_quant_factor}
@item
@option{i_qoffset} / @option{i_quant_offset}
@item
@option{b_qfactor} / @option{b_quant_factor}
@item
@option{b_qoffset} / @option{b_quant_offset}
@item
@option{slices}
@end itemize

All encoders support the following options:
@table @option
@item low_power
Some drivers/platforms offer a second encoder for some codecs intended to use
less power than the default encoder; setting this option will attempt to use
that encoder.  Note that it may support a reduced feature set, so some other
options may not be available in this mode.

@item idr_interval
Set the number of normal intra frames between full-refresh (IDR) frames in
open-GOP mode.  The intra frames are still IRAPs, but will not include global
headers and may have non-decodable leading pictures.

@item b_depth
Set the B-frame reference depth.  When set to one (the default), all B-frames
will refer only to P- or I-frames.  When set to greater values multiple layers
of B-frames will be present, frames in each layer only referring to frames in
higher layers.

@item async_depth
Maximum processing parallelism. Increase this to improve single channel
performance. This option doesn't work if driver doesn't implement vaSyncBuffer
function. Please make sure there are enough hw_frames allocated if a large
number of async_depth is used.

@item max_frame_size
Set the allowed max size in bytes for each frame. If the frame size exceeds
the limitation, encoder will adjust the QP value to control the frame size.
Invalid in CQP rate control mode.

@item rc_mode
Set the rate control mode to use.  A given driver may only support a subset of
modes.

Possible modes:
@table @option
@item auto
Choose the mode automatically based on driver support and the other options.
This is the default.
@item CQP
Constant-quality.
@item CBR
Constant-bitrate.
@item VBR
Variable-bitrate.
@item ICQ
Intelligent constant-quality.
@item QVBR
Quality-defined variable-bitrate.
@item AVBR
Average variable bitrate.
@end table

@item blbrc
Enable block level rate control, which assigns different bitrate block by block.
Invalid for CQP mode.

@end table

Each encoder also has its own specific options:
@table @option

@item av1_vaapi
@option{profile} sets the value of @emph{seq_profile}.
@option{tier} sets the value of @emph{seq_tier}.
@option{level} sets the value of @emph{seq_level_idx}.

@table @option
@item tiles
Set the number of tiles to encode the input video with, as columns x rows.
(default is auto, which means use minimal tile column/row number).
@item tile_groups
Set tile groups number. All the tiles will be distributed as evenly as possible to
each tile group. (default is 1).
@end table

@item h264_vaapi
@option{profile} sets the value of @emph{profile_idc} and the @emph{constraint_set*_flag}s.
@option{level} sets the value of @emph{level_idc}.

@table @option
@item coder
Set entropy encoder (default is @emph{cabac}).  Possible values:

@table @samp
@item ac
@item cabac
Use CABAC.

@item vlc
@item cavlc
Use CAVLC.
@end table

@item aud
Include access unit delimiters in the stream (not included by default).

@item sei
Set SEI message types to include.
Some combination of the following values:
@table @samp
@item identifier
Include a @emph{user_data_unregistered} message containing information about
the encoder.
@item timing
Include picture timing parameters (@emph{buffering_period} and
@emph{pic_timing} messages).
@item recovery_point
Include recovery points where appropriate (@emph{recovery_point} messages).
@end table

@end table

@item hevc_vaapi
@option{profile} and @option{level} set the values of
@emph{general_profile_idc} and @emph{general_level_idc} respectively.

@table @option
@item aud
Include access unit delimiters in the stream (not included by default).

@item tier
Set @emph{general_tier_flag}.  This may affect the level chosen for the stream
if it is not explicitly specified.

@item sei
Set SEI message types to include.
Some combination of the following values:
@table @samp
@item hdr
Include HDR metadata if the input frames have it
(@emph{mastering_display_colour_volume} and @emph{content_light_level}
messages).
@end table

@item tiles
Set the number of tiles to encode the input video with, as columns x rows.
Larger numbers allow greater parallelism in both encoding and decoding, but
may decrease coding efficiency.

@end table

@item mjpeg_vaapi
Only baseline DCT encoding is supported.  The encoder always uses the standard
quantisation and huffman tables - @option{global_quality} scales the standard
quantisation table (range 1-100).

For YUV, 4:2:0, 4:2:2 and 4:4:4 subsampling modes are supported.  RGB is also
supported, and will create an RGB JPEG.

@table @option
@item jfif
Include JFIF header in each frame (not included by default).
@item huffman
Include standard huffman tables (on by default).  Turning this off will save
a few hundred bytes in each output frame, but may lose compatibility with some
JPEG decoders which don't fully handle MJPEG.
@end table

@item mpeg2_vaapi
@option{profile} and @option{level} set the value of @emph{profile_and_level_indication}.

@item vp8_vaapi
B-frames are not supported.

@option{global_quality} sets the @emph{q_idx} used for non-key frames (range 0-127).

@table @option
@item loop_filter_level
@item loop_filter_sharpness
Manually set the loop filter parameters.
@end table

@item vp9_vaapi
@option{global_quality} sets the @emph{q_idx} used for P-frames (range 0-255).

@table @option
@item loop_filter_level
@item loop_filter_sharpness
Manually set the loop filter parameters.
@end table

B-frames are supported, but the output stream is always in encode order rather than display
order.  If B-frames are enabled, it may be necessary to use the @option{vp9_raw_reorder}
bitstream filter to modify the output stream to display frames in the correct order.

Only normal frames are produced - the @option{vp9_superframe} bitstream filter may be
required to produce a stream usable with all decoders.

@end table

@section vbn

Vizrt Binary Image encoder.

This format is used by the broadcast vendor Vizrt for quick texture streaming.
Advanced features of the format such as LZW compression of texture data or
generation of mipmaps are not supported.

@subsection Options

@table @option
@item format @var{string}
Sets the texture compression used by the VBN file. Can be @var{dxt1},
@var{dxt5} or @var{raw}. Default is @var{dxt5}.
@end table

@section vc2

SMPTE VC-2 (previously BBC Dirac Pro). This codec was primarily aimed at
professional broadcasting but since it supports yuv420, yuv422 and yuv444 at
8 (limited range or full range), 10 or 12 bits, this makes it suitable for
other tasks which require low overhead and low compression (like screen
recording).

@subsection Options

@table @option

@item b
Sets target video bitrate. Usually that's around 1:6 of the uncompressed
video bitrate (e.g. for 1920x1080 50fps yuv422p10 that's around 400Mbps). Higher
values (close to the uncompressed bitrate) turn on lossless compression mode.

@item field_order
Enables field coding when set (e.g. to tt - top field first) for interlaced
inputs. Should increase compression with interlaced content as it splits the
fields and encodes each separately.

@item wavelet_depth
Sets the total amount of wavelet transforms to apply, between 1 and 5 (default).
Lower values reduce compression and quality. Less capable decoders may not be
able to handle values of @option{wavelet_depth} over 3.

@item wavelet_type
Sets the transform type. Currently only @var{5_3} (LeGall) and @var{9_7}
(Deslauriers-Dubuc)
are implemented, with 9_7 being the one with better compression and thus
is the default.

@item slice_width
@item slice_height
Sets the slice size for each slice. Larger values result in better compression.
For compatibility with other more limited decoders use @option{slice_width} of
32 and @option{slice_height} of 8.

@item tolerance
Sets the undershoot tolerance of the rate control system in percent. This is
to prevent an expensive search from being run.

@item qm
Sets the quantization matrix preset to use by default or when @option{wavelet_depth}
is set to 5
@itemize @minus
@item
@var{default}
Uses the default quantization matrix from the specifications, extended with
values for the fifth level. This provides a good balance between keeping detail
and omitting artifacts.

@item
@var{flat}
Use a completely zeroed out quantization matrix. This increases PSNR but might
reduce perception. Use in bogus benchmarks.

@item
@var{color}
Reduces detail but attempts to preserve color at extremely low bitrates.
@end itemize

@end table

@c man end VIDEO ENCODERS

@chapter Subtitles Encoders
@c man begin SUBTITLES ENCODERS

@section dvdsub

This codec encodes the bitmap subtitle format that is used in DVDs.
Typically they are stored in VOBSUB file pairs (*.idx + *.sub),
and they can also be used in Matroska files.

@subsection Options

@table @option
@item palette
Specify the global palette used by the bitmaps.

The format for this option is a string containing 16 24-bits hexadecimal
numbers (without 0x prefix) separated by commas, for example @code{0d00ee,
ee450d, 101010, eaeaea, 0ce60b, ec14ed, ebff0b, 0d617a, 7b7b7b, d1d1d1,
7b2a0e, 0d950c, 0f007b, cf0dec, cfa80c, 7c127b}.

@item even_rows_fix
When set to 1, enable a work-around that makes the number of pixel rows
even in all subtitles.  This fixes a problem with some players that
cut off the bottom row if the number is odd.  The work-around just adds
a fully transparent row if needed.  The overhead is low, typically
one byte per subtitle on average.

By default, this work-around is disabled.
@end table

@c man end SUBTITLES ENCODERS
