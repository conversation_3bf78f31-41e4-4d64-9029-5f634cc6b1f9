slot=                                    # some unique identifier
repo=git://source.ffmpeg.org/ffmpeg.git  # the source repository
#branch=release/2.6                       # the branch to test
samples=                                 # path to samples directory
workdir=                                 # directory in which to do all the work
#fate_recv="ssh -T <EMAIL>" # command to submit report
comment=                                 # optional description
build_only=     # set to "yes" for a compile-only instance that skips tests
ignore_tests=

# the following are optional and map to configure options
arch=
cpu=
cross_prefix=
as=
cc=
ld=
target_os=
sysroot=
target_exec=
target_path=
target_samples=
extra_cflags=
extra_ldflags=
extra_libs=
extra_conf=     # extra configure options not covered above

#make=          # name of GNU make if not 'make'
makeopts=       # extra options passed to 'make'
#makeopts_fate= # extra options passed to 'make' when running tests,
                # defaulting to makeopts above if this is not set
#tar=           # command to create a tar archive from its arguments on stdout,
                # defaults to 'tar c'
#fate_targets=  # targets to make when running fate; defaults to "fate",
                # can be set to run a subset of tests, e.g. "fate-checkasm".

#fate_environments=  # a list of names of configurations to run tests for;
                     # each round is run with variables from ${${name}_env} set.

# One example of using fate_environments:

# target_exec="qemu-aarch64-static"
# fate_targets="fate-checkasm fate-cpu"
# fate_environments="sve128 sve256"
# sve128_env="QEMU_CPU=max,sve128=on"
# sve256_env="QEMU_CPU=max,sve256=on"

# The variables set by fate_environments can also be used explicitly
# by target_exec, e.g. like this:

# target_exec="qemu-aarch64-static -cpu \$(MY_CPU)"
# fate_targets="fate-checkasm fate-cpu"
# fate_environments="sve128 sve256"
# sve128_env="MY_CPU=max,sve128=on"
# sve256_env="MY_CPU=max,sve256=on"
