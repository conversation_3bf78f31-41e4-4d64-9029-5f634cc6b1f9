\input texinfo @c -*- texinfo -*-
@documentencoding UTF-8

@settitle ffmpeg Documentation
@titlepage
@center @titlefont{ffmpeg Documentation}
@end titlepage

@top

@contents

@chapter Synopsis

ffmpeg [@var{global_options}] @{[@var{input_file_options}] -i @file{input_url}@} ... @{[@var{output_file_options}] @file{output_url}@} ...

@chapter Description
@c man begin DESCRIPTION

@command{ffmpeg} is a universal media converter. It can read a wide variety of
inputs - including live grabbing/recording devices - filter, and transcode them
into a plethora of output formats.

@command{ffmpeg} reads from an arbitrary number of input "files" (which can be regular
files, pipes, network streams, grabbing devices, etc.), specified by the
@code{-i} option, and writes to an arbitrary number of output "files", which are
specified by a plain output url. Anything found on the command line which
cannot be interpreted as an option is considered to be an output url.

Each input or output url can, in principle, contain any number of streams of
different types (video/audio/subtitle/attachment/data). The allowed number and/or
types of streams may be limited by the container format. Selecting which
streams from which inputs will go into which output is either done automatically
or with the @code{-map} option (see the Stream selection chapter).

To refer to input files in options, you must use their indices (0-based). E.g.
the first input file is @code{0}, the second is @code{1}, etc. Similarly, streams
within a file are referred to by their indices. E.g. @code{2:3} refers to the
fourth stream in the third input file. Also see the Stream specifiers chapter.

As a general rule, options are applied to the next specified
file. Therefore, order is important, and you can have the same
option on the command line multiple times. Each occurrence is
then applied to the next input or output file.
Exceptions from this rule are the global options (e.g. verbosity level),
which should be specified first.

Do not mix input and output files -- first specify all input files, then all
output files. Also do not mix options which belong to different files. All
options apply ONLY to the next input or output file and are reset between files.

Some simple examples follow.

@itemize
@item
Convert an input media file to a different format, by re-encoding media streams:
@example
ffmpeg -i input.avi output.mp4
@end example

@item
Set the video bitrate of the output file to 64 kbit/s:
@example
ffmpeg -i input.avi -b:v 64k -bufsize 64k output.mp4
@end example

@item
Force the frame rate of the output file to 24 fps:
@example
ffmpeg -i input.avi -r 24 output.mp4
@end example

@item
Force the frame rate of the input file (valid for raw formats only) to 1 fps and
the frame rate of the output file to 24 fps:
@example
ffmpeg -r 1 -i input.m2v -r 24 output.mp4
@end example
@end itemize

The format option may be needed for raw input files.

@c man end DESCRIPTION

@chapter Detailed description
@c man begin DETAILED DESCRIPTION

The transcoding process in @command{ffmpeg} for each output can be described by
the following diagram:

@verbatim
 _______              ______________
|       |            |              |
| input |  demuxer   | encoded data |   decoder
| file  | ---------> | packets      | -----+
|_______|            |______________|      |
                                           v
                                       _________
                                      |         |
                                      | decoded |
                                      | frames  |
                                      |_________|
 ________             ______________       |
|        |           |              |      |
| output | <-------- | encoded data | <----+
| file   |   muxer   | packets      |   encoder
|________|           |______________|


@end verbatim

@command{ffmpeg} calls the libavformat library (containing demuxers) to read
input files and get packets containing encoded data from them. When there are
multiple input files, @command{ffmpeg} tries to keep them synchronized by
tracking lowest timestamp on any active input stream.

Encoded packets are then passed to the decoder (unless streamcopy is selected
for the stream, see further for a description). The decoder produces
uncompressed frames (raw video/PCM audio/...) which can be processed further by
filtering (see next section). After filtering, the frames are passed to the
encoder, which encodes them and outputs encoded packets. Finally, those are
passed to the muxer, which writes the encoded packets to the output file.

@section Filtering
Before encoding, @command{ffmpeg} can process raw audio and video frames using
filters from the libavfilter library. Several chained filters form a filter
graph. @command{ffmpeg} distinguishes between two types of filtergraphs:
simple and complex.

@subsection Simple filtergraphs
Simple filtergraphs are those that have exactly one input and output, both of
the same type. In the above diagram they can be represented by simply inserting
an additional step between decoding and encoding:

@verbatim
 _________                        ______________
|         |                      |              |
| decoded |                      | encoded data |
| frames  |\                   _ | packets      |
|_________| \                  /||______________|
             \   __________   /
  simple     _\||          | /  encoder
  filtergraph   | filtered |/
                | frames   |
                |__________|

@end verbatim

Simple filtergraphs are configured with the per-stream @option{-filter} option
(with @option{-vf} and @option{-af} aliases for video and audio respectively).
A simple filtergraph for video can look for example like this:

@verbatim
 _______        _____________        _______        ________
|       |      |             |      |       |      |        |
| input | ---> | deinterlace | ---> | scale | ---> | output |
|_______|      |_____________|      |_______|      |________|

@end verbatim

Note that some filters change frame properties but not frame contents. E.g. the
@code{fps} filter in the example above changes number of frames, but does not
touch the frame contents. Another example is the @code{setpts} filter, which
only sets timestamps and otherwise passes the frames unchanged.

@subsection Complex filtergraphs
Complex filtergraphs are those which cannot be described as simply a linear
processing chain applied to one stream. This is the case, for example, when the graph has
more than one input and/or output, or when output stream type is different from
input. They can be represented with the following diagram:

@verbatim
 _________
|         |
| input 0 |\                    __________
|_________| \                  |          |
             \   _________    /| output 0 |
              \ |         |  / |__________|
 _________     \| complex | /
|         |     |         |/
| input 1 |---->| filter  |\
|_________|     |         | \   __________
               /| graph   |  \ |          |
              / |         |   \| output 1 |
 _________   /  |_________|    |__________|
|         | /
| input 2 |/
|_________|

@end verbatim

Complex filtergraphs are configured with the @option{-filter_complex} option.
Note that this option is global, since a complex filtergraph, by its nature,
cannot be unambiguously associated with a single stream or file.

The @option{-lavfi} option is equivalent to @option{-filter_complex}.

A trivial example of a complex filtergraph is the @code{overlay} filter, which
has two video inputs and one video output, containing one video overlaid on top
of the other. Its audio counterpart is the @code{amix} filter.

@section Stream copy
Stream copy is a mode selected by supplying the @code{copy} parameter to the
@option{-codec} option. It makes @command{ffmpeg} omit the decoding and encoding
step for the specified stream, so it does only demuxing and muxing. It is useful
for changing the container format or modifying container-level metadata. The
diagram above will, in this case, simplify to this:

@verbatim
 _______              ______________            ________
|       |            |              |          |        |
| input |  demuxer   | encoded data |  muxer   | output |
| file  | ---------> | packets      | -------> | file   |
|_______|            |______________|          |________|

@end verbatim

Since there is no decoding or encoding, it is very fast and there is no quality
loss. However, it might not work in some cases because of many factors. Applying
filters is obviously also impossible, since filters work on uncompressed data.

@section Loopback decoders
While decoders are normally associated with demuxer streams, it is also possible
to create "loopback" decoders that decode the output from some encoder and allow
it to be fed back to complex filtergraphs. This is done with the @code{-dec}
directive, which takes as a parameter the index of the output stream that should
be decoded. Every such directive creates a new loopback decoder, indexed with
successive integers starting at zero. These indices should then be used to refer
to loopback decoders in complex filtergraph link labels, as described in the
documentation for @option{-filter_complex}.

Decoding AVOptions can be passed to loopback decoders by placing them before
@code{-dec}, analogously to input/output options.

E.g. the following example:

@example
ffmpeg -i INPUT                                        \
  -map 0:v:0 -c:v libx264 -crf 45 -f null -            \
  -threads 3 -dec 0:0                                  \
  -filter_complex '[0:v][dec:0]hstack[stack]'          \
  -map '[stack]' -c:v ffv1 OUTPUT
@end example

reads an input video and
@itemize
@item
(line 2) encodes it with @code{libx264} at low quality;

@item
(line 3) decodes this encoded stream using 3 threads;

@item
(line 4) places decoded video side by side with the original input video;

@item
(line 5) combined video is then losslessly encoded and written into
@file{OUTPUT}.

@end itemize

@c man end DETAILED DESCRIPTION

@chapter Stream selection
@c man begin STREAM SELECTION

@command{ffmpeg} provides the @code{-map} option for manual control of stream selection in each
output file. Users can skip @code{-map} and let ffmpeg perform automatic stream selection as
described below. The @code{-vn / -an / -sn / -dn} options can be used to skip inclusion of
video, audio, subtitle and data streams respectively, whether manually mapped or automatically
selected, except for those streams which are outputs of complex filtergraphs.

@section Description
The sub-sections that follow describe the various rules that are involved in stream selection.
The examples that follow next show how these rules are applied in practice.

While every effort is made to accurately reflect the behavior of the program, FFmpeg is under
continuous development and the code may have changed since the time of this writing.

@subsection Automatic stream selection

In the absence of any map options for a particular output file, ffmpeg inspects the output
format to check which type of streams can be included in it, viz. video, audio and/or
subtitles. For each acceptable stream type, ffmpeg will pick one stream, when available,
from among all the inputs.

It will select that stream based upon the following criteria:
@itemize
@item
for video, it is the stream with the highest resolution,
@item
for audio, it is the stream with the most channels,
@item
for subtitles, it is the first subtitle stream found but there's a caveat.
The output format's default subtitle encoder can be either text-based or image-based,
and only a subtitle stream of the same type will be chosen.
@end itemize

In the case where several streams of the same type rate equally, the stream with the lowest
index is chosen.

Data or attachment streams are not automatically selected and can only be included
using @code{-map}.
@subsection Manual stream selection

When @code{-map} is used, only user-mapped streams are included in that output file,
with one possible exception for filtergraph outputs described below.

@subsection Complex filtergraphs

If there are any complex filtergraph output streams with unlabeled pads, they will be added
to the first output file. This will lead to a fatal error if the stream type is not supported
by the output format. In the absence of the map option, the inclusion of these streams leads
to the automatic stream selection of their types being skipped. If map options are present,
these filtergraph streams are included in addition to the mapped streams.

Complex filtergraph output streams with labeled pads must be mapped once and exactly once.

@subsection Stream handling

Stream handling is independent of stream selection, with an exception for subtitles described
below. Stream handling is set via the @code{-codec} option addressed to streams within a
specific @emph{output} file. In particular, codec options are applied by ffmpeg after the
stream selection process and thus do not influence the latter. If no @code{-codec} option is
specified for a stream type, ffmpeg will select the default encoder registered by the output
file muxer.

An exception exists for subtitles. If a subtitle encoder is specified for an output file, the
first subtitle stream found of any type, text or image, will be included. ffmpeg does not validate
if the specified encoder can convert the selected stream or if the converted stream is acceptable
within the output format. This applies generally as well: when the user sets an encoder manually,
the stream selection process cannot check if the encoded stream can be muxed into the output file.
If it cannot, ffmpeg will abort and @emph{all} output files will fail to be processed.

@section Examples

The following examples illustrate the behavior, quirks and limitations of ffmpeg's stream
selection methods.

They assume the following three input files.

@verbatim

input file 'A.avi'
      stream 0: video 640x360
      stream 1: audio 2 channels

input file 'B.mp4'
      stream 0: video 1920x1080
      stream 1: audio 2 channels
      stream 2: subtitles (text)
      stream 3: audio 5.1 channels
      stream 4: subtitles (text)

input file 'C.mkv'
      stream 0: video 1280x720
      stream 1: audio 2 channels
      stream 2: subtitles (image)
@end verbatim

@subsubheading Example: automatic stream selection
@example
ffmpeg -i A.avi -i B.mp4 out1.mkv out2.wav -map 1:a -c:a copy out3.mov
@end example
There are three output files specified, and for the first two, no @code{-map} options
are set, so ffmpeg will select streams for these two files automatically.

@file{out1.mkv} is a Matroska container file and accepts video, audio and subtitle streams,
so ffmpeg will try to select one of each type.@*
For video, it will select @code{stream 0} from @file{B.mp4}, which has the highest
resolution among all the input video streams.@*
For audio, it will select @code{stream 3} from @file{B.mp4}, since it has the greatest
number of channels.@*
For subtitles, it will select @code{stream 2} from @file{B.mp4}, which is the first subtitle
stream from among @file{A.avi} and @file{B.mp4}.

@file{out2.wav} accepts only audio streams, so only @code{stream 3} from @file{B.mp4} is
selected.

For @file{out3.mov}, since a @code{-map} option is set, no automatic stream selection will
occur. The @code{-map 1:a} option will select all audio streams from the second input
@file{B.mp4}. No other streams will be included in this output file.

For the first two outputs, all included streams will be transcoded. The encoders chosen will
be the default ones registered by each output format, which may not match the codec of the
selected input streams.

For the third output, codec option for audio streams has been set
to @code{copy}, so no decoding-filtering-encoding operations will occur, or @emph{can} occur.
Packets of selected streams shall be conveyed from the input file and muxed within the output
file.

@subsubheading Example: automatic subtitles selection
@example
ffmpeg -i C.mkv out1.mkv -c:s dvdsub -an out2.mkv
@end example
Although @file{out1.mkv} is a Matroska container file which accepts subtitle streams, only a
video and audio stream shall be selected. The subtitle stream of @file{C.mkv} is image-based
and the default subtitle encoder of the Matroska muxer is text-based, so a transcode operation
for the subtitles is expected to fail and hence the stream isn't selected. However, in
@file{out2.mkv}, a subtitle encoder is specified in the command and so, the subtitle stream is
selected, in addition to the video stream. The presence of @code{-an} disables audio stream
selection for @file{out2.mkv}.

@subsubheading Example: unlabeled filtergraph outputs
@example
ffmpeg -i A.avi -i C.mkv -i B.mp4 -filter_complex "overlay" out1.mp4 out2.srt
@end example
A filtergraph is setup here using the @code{-filter_complex} option and consists of a single
video filter. The @code{overlay} filter requires exactly two video inputs, but none are
specified, so the first two available video streams are used, those of @file{A.avi} and
@file{C.mkv}. The output pad of the filter has no label and so is sent to the first output file
@file{out1.mp4}. Due to this, automatic selection of the video stream is skipped, which would
have selected the stream in @file{B.mp4}. The audio stream with most channels viz. @code{stream 3}
in @file{B.mp4}, is chosen automatically. No subtitle stream is chosen however, since the MP4
format has no default subtitle encoder registered, and the user hasn't specified a subtitle encoder.

The 2nd output file, @file{out2.srt}, only accepts text-based subtitle streams. So, even though
the first subtitle stream available belongs to @file{C.mkv}, it is image-based and hence skipped.
The selected stream, @code{stream 2} in @file{B.mp4}, is the first text-based subtitle stream.

@subsubheading Example: labeled filtergraph outputs
@example
ffmpeg -i A.avi -i B.mp4 -i C.mkv -filter_complex "[1:v]hue=s=0[outv];overlay;aresample" \
       -map '[outv]' -an        out1.mp4 \
                                out2.mkv \
       -map '[outv]' -map 1:a:0 out3.mkv
@end example

The above command will fail, as the output pad labelled @code{[outv]} has been mapped twice.
None of the output files shall be processed.

@example
ffmpeg -i A.avi -i B.mp4 -i C.mkv -filter_complex "[1:v]hue=s=0[outv];overlay;aresample" \
       -an        out1.mp4 \
                  out2.mkv \
       -map 1:a:0 out3.mkv
@end example

This command above will also fail as the hue filter output has a label, @code{[outv]},
and hasn't been mapped anywhere.

The command should be modified as follows,
@example
ffmpeg -i A.avi -i B.mp4 -i C.mkv -filter_complex "[1:v]hue=s=0,split=2[outv1][outv2];overlay;aresample" \
        -map '[outv1]' -an        out1.mp4 \
                                  out2.mkv \
        -map '[outv2]' -map 1:a:0 out3.mkv
@end example
The video stream from @file{B.mp4} is sent to the hue filter, whose output is cloned once using
the split filter, and both outputs labelled. Then a copy each is mapped to the first and third
output files.

The overlay filter, requiring two video inputs, uses the first two unused video streams. Those
are the streams from @file{A.avi} and @file{C.mkv}. The overlay output isn't labelled, so it is
sent to the first output file @file{out1.mp4}, regardless of the presence of the @code{-map} option.

The aresample filter is sent the first unused audio stream, that of @file{A.avi}. Since this filter
output is also unlabelled, it too is mapped to the first output file. The presence of @code{-an}
only suppresses automatic or manual stream selection of audio streams, not outputs sent from
filtergraphs. Both these mapped streams shall be ordered before the mapped stream in @file{out1.mp4}.

The video, audio and subtitle streams mapped to @code{out2.mkv} are entirely determined by
automatic stream selection.

@file{out3.mkv} consists of the cloned video output from the hue filter and the first audio
stream from @file{B.mp4}.
@*

@c man end STREAM SELECTION

@chapter Options
@c man begin OPTIONS

@include fftools-common-opts.texi

@section Main options

@table @option

@item -f @var{fmt} (@emph{input/output})
Force input or output file format. The format is normally auto detected for input
files and guessed from the file extension for output files, so this option is not
needed in most cases.

@item -i @var{url} (@emph{input})
input file url

@item -y (@emph{global})
Overwrite output files without asking.

@item -n (@emph{global})
Do not overwrite output files, and exit immediately if a specified
output file already exists.

@item -stream_loop @var{number} (@emph{input})
Set number of times input stream shall be looped. Loop 0 means no loop,
loop -1 means infinite loop.

@item -recast_media (@emph{global})
Allow forcing a decoder of a different media type than the one
detected or designated by the demuxer. Useful for decoding media
data muxed as data streams.

@item -c[:@var{stream_specifier}] @var{codec} (@emph{input/output,per-stream})
@itemx -codec[:@var{stream_specifier}] @var{codec} (@emph{input/output,per-stream})
Select an encoder (when used before an output file) or a decoder (when used
before an input file) for one or more streams. @var{codec} is the name of a
decoder/encoder or a special value @code{copy} (output only) to indicate that
the stream is not to be re-encoded.

For example
@example
ffmpeg -i INPUT -map 0 -c:v libx264 -c:a copy OUTPUT
@end example
encodes all video streams with libx264 and copies all audio streams.

For each stream, the last matching @code{c} option is applied, so
@example
ffmpeg -i INPUT -map 0 -c copy -c:v:1 libx264 -c:a:137 libvorbis OUTPUT
@end example
will copy all the streams except the second video, which will be encoded with
libx264, and the 138th audio, which will be encoded with libvorbis.

@item -t @var{duration} (@emph{input/output})
When used as an input option (before @code{-i}), limit the @var{duration} of
data read from the input file.

When used as an output option (before an output url), stop writing the
output after its duration reaches @var{duration}.

@var{duration} must be a time duration specification,
see @ref{time duration syntax,,the Time duration section in the ffmpeg-utils(1) manual,ffmpeg-utils}.

-to and -t are mutually exclusive and -t has priority.

@item -to @var{position} (@emph{input/output})
Stop writing the output or reading the input at @var{position}.
@var{position} must be a time duration specification,
see @ref{time duration syntax,,the Time duration section in the ffmpeg-utils(1) manual,ffmpeg-utils}.

-to and -t are mutually exclusive and -t has priority.

@item -fs @var{limit_size} (@emph{output})
Set the file size limit, expressed in bytes. No further chunk of bytes is written
after the limit is exceeded. The size of the output file is slightly more than the
requested file size.

@item -ss @var{position} (@emph{input/output})
When used as an input option (before @code{-i}), seeks in this input file to
@var{position}. Note that in most formats it is not possible to seek exactly,
so @command{ffmpeg} will seek to the closest seek point before @var{position}.
When transcoding and @option{-accurate_seek} is enabled (the default), this
extra segment between the seek point and @var{position} will be decoded and
discarded. When doing stream copy or when @option{-noaccurate_seek} is used, it
will be preserved.

When used as an output option (before an output url), decodes but discards
input until the timestamps reach @var{position}.

@var{position} must be a time duration specification,
see @ref{time duration syntax,,the Time duration section in the ffmpeg-utils(1) manual,ffmpeg-utils}.

@item -sseof @var{position} (@emph{input})

Like the @code{-ss} option but relative to the "end of file". That is negative
values are earlier in the file, 0 is at EOF.

@item -isync @var{input_index} (@emph{input})
Assign an input as a sync source.

This will take the difference between the start times of the target and reference inputs and
offset the timestamps of the target file by that difference. The source timestamps of the two
inputs should derive from the same clock source for expected results. If @code{copyts} is set
then @code{start_at_zero} must also be set. If either of the inputs has no starting timestamp
then no sync adjustment is made.

Acceptable values are those that refer to a valid ffmpeg input index. If the sync reference is
the target index itself or @var{-1}, then no adjustment is made to target timestamps. A sync
reference may not itself be synced to any other input.

Default value is @var{-1}.

@item -itsoffset @var{offset} (@emph{input})
Set the input time offset.

@var{offset} must be a time duration specification,
see @ref{time duration syntax,,the Time duration section in the ffmpeg-utils(1) manual,ffmpeg-utils}.

The offset is added to the timestamps of the input files. Specifying
a positive offset means that the corresponding streams are delayed by
the time duration specified in @var{offset}.

@item -itsscale @var{scale} (@emph{input,per-stream})
Rescale input timestamps. @var{scale} should be a floating point number.

@item -timestamp @var{date} (@emph{output})
Set the recording timestamp in the container.

@var{date} must be a date specification,
see @ref{date syntax,,the Date section in the ffmpeg-utils(1) manual,ffmpeg-utils}.

@item -metadata[:metadata_specifier] @var{key}=@var{value} (@emph{output,per-metadata})
Set a metadata key/value pair.

An optional @var{metadata_specifier} may be given to set metadata
on streams, chapters or programs. See @code{-map_metadata}
documentation for details.

This option overrides metadata set with @code{-map_metadata}. It is
also possible to delete metadata by using an empty value.

For example, for setting the title in the output file:
@example
ffmpeg -i in.avi -metadata title="my title" out.flv
@end example

To set the language of the first audio stream:
@example
ffmpeg -i INPUT -metadata:s:a:0 language=eng OUTPUT
@end example

@item -disposition[:stream_specifier] @var{value} (@emph{output,per-stream})
Sets the disposition for a stream.

By default, the disposition is copied from the input stream, unless the output
stream this option applies to is fed by a complex filtergraph - in that case the
disposition is unset by default.

@var{value} is a sequence of items separated by '+' or '-'. The first item may
also be prefixed with '+' or '-', in which case this option modifies the default
value. Otherwise (the first item is not prefixed) this options overrides the
default value. A '+' prefix adds the given disposition, '-' removes it. It is
also possible to clear the disposition by setting it to 0.

If no @code{-disposition} options were specified for an output file, ffmpeg will
automatically set the 'default' disposition on the first stream of each type,
when there are multiple streams of this type in the output file and no stream of
that type is already marked as default.

The @code{-dispositions} option lists the known dispositions.

For example, to make the second audio stream the default stream:
@example
ffmpeg -i in.mkv -c copy -disposition:a:1 default out.mkv
@end example

To make the second subtitle stream the default stream and remove the default
disposition from the first subtitle stream:
@example
ffmpeg -i in.mkv -c copy -disposition:s:0 0 -disposition:s:1 default out.mkv
@end example

To add an embedded cover/thumbnail:
@example
ffmpeg -i in.mp4 -i IMAGE -map 0 -map 1 -c copy -c:v:1 png -disposition:v:1 attached_pic out.mp4
@end example

Not all muxers support embedded thumbnails, and those who do, only support a few formats, like JPEG or PNG.

@item -program [title=@var{title}:][program_num=@var{program_num}:]st=@var{stream}[:st=@var{stream}...] (@emph{output})

Creates a program with the specified @var{title}, @var{program_num} and adds the specified
@var{stream}(s) to it.

@item -stream_group [map=@var{input_file_id}=@var{stream_group}][type=@var{type}:]st=@var{stream}[:st=@var{stream}][:stg=@var{stream_group}][:id=@var{stream_group_id}...] (@emph{output})

Creates a stream group of the specified @var{type} and @var{stream_group_id}, or by
@var{map}ping an input group, adding the specified @var{stream}(s) and/or previously
defined @var{stream_group}(s) to it.

@var{type} can be one of the following:
@table @option

@item iamf_audio_element
Groups @var{stream}s that belong to the same IAMF Audio Element

For this group @var{type}, the following options are available
@table @option
@item audio_element_type
The Audio Element type. The following values are supported:

@table @option
@item channel
Scalable channel audio representation
@item scene
Ambisonics representation
@end table

@item demixing
Demixing information used to reconstruct a scalable channel audio representation.
This option must be separated from the rest with a ',', and takes the following
key=value options

@table @option
@item parameter_id
An identifier parameters blocks in frames may refer to
@item dmixp_mode
A pre-defined combination of demixing parameters
@end table

@item recon_gain
Recon gain information used to reconstruct a scalable channel audio representation.
This option must be separated from the rest with a ',', and takes the following
key=value options

@table @option
@item parameter_id
An identifier parameters blocks in frames may refer to
@end table

@item layer
A layer defining a Channel Layout in the Audio Element.
This option must be separated from the rest with a ','. Several ',' separated entries
can be defined, and at least one must be set.

It takes the following ":"-separated key=value options

@table @option
@item ch_layout
The layer's channel layout
@item flags
The following flags are available:

@table @option
@item recon_gain
Wether to signal if recon_gain is present as metadata in parameter blocks within frames
@end table

@item output_gain
@item output_gain_flags
Which channels output_gain applies to. The following flags are available:

@table @option
@item FL
@item FR
@item BL
@item BR
@item TFL
@item TFR
@end table

@item ambisonics_mode
The ambisonics mode. This has no effect if audio_element_type is set to channel.

The following values are supported:

@table @option
@item mono
Each ambisonics channel is coded as an individual mono stream in the group
@end table

@end table

@item default_w
Default weight value

@end table

@item iamf_mix_presentation
Groups @var{stream}s that belong to all IAMF Audio Element the same
IAMF Mix Presentation references

For this group @var{type}, the following options are available

@table @option
@item submix
A sub-mix within the Mix Presentation.
This option must be separated from the rest with a ','. Several ',' separated entries
can be defined, and at least one must be set.

It takes the following ":"-separated key=value options

@table @option
@item parameter_id
An identifier parameters blocks in frames may refer to, for post-processing the mixed
audio signal to generate the audio signal for playback
@item parameter_rate
The sample rate duration fields in parameters blocks in frames that refer to this
@var{parameter_id} are expressed as
@item default_mix_gain
Default mix gain value to apply when there are no parameter blocks sharing the same
@var{parameter_id} for a given frame

@item element
References an Audio Element used in this Mix Presentation to generate the final output
audio signal for playback.
This option must be separated from the rest with a '|'. Several '|' separated entries
can be defined, and at least one must be set.

It takes the following ":"-separated key=value options:

@table @option
@item stg
The @var{stream_group_id} for an Audio Element which this sub-mix refers to
@item parameter_id
An identifier parameters blocks in frames may refer to, for applying any processing to
the referenced and rendered Audio Element before being summed with other processed Audio
Elements
@item parameter_rate
The sample rate duration fields in parameters blocks in frames that refer to this
@var{parameter_id} are expressed as
@item default_mix_gain
Default mix gain value to apply when there are no parameter blocks sharing the same
@var{parameter_id} for a given frame
@item annotations
A key=value string describing the sub-mix element where "key" is a string conforming to
BCP-47 that specifies the language for the "value" string. "key" must be the same as the
one in the mix's @var{annotations}
@item headphones_rendering_mode
Indicates whether the input channel-based Audio Element is rendered to stereo loudspeakers
or spatialized with a binaural renderer when played back on headphones.
This has no effect if the referenced Audio Element's @var{audio_element_type} is set to
channel.

The following values are supported:

@table @option
@item stereo
@item binaural
@end table

@end table

@item layout
Specifies the layouts for this sub-mix on which the loudness information was measured.
This option must be separated from the rest with a '|'. Several '|' separated entries
can be defined, and at least one must be set.

It takes the following ":"-separated key=value options:

@table @option
@item layout_type

@table @option
@item loudspeakers
The layout follows the loudspeaker sound system convention of ITU-2051-3.
@item binaural
The layout is binaural.
@end table

@item sound_system
Channel layout matching one of Sound Systems A to J of ITU-2051-3, plus 7.1.2 and 3.1.2
This has no effect if @var{layout_type} is set to binaural.
@item integrated_loudness
The program integrated loudness information, as defined in ITU-1770-4.
@item digital_peak
The digital (sampled) peak value of the audio signal, as defined in ITU-1770-4.
@item true_peak
The true peak of the audio signal, as defined in ITU-1770-4.
@item dialog_anchored_loudness
The Dialogue loudness information, as defined in ITU-1770-4.
@item album_anchored_loudness
The Album loudness information, as defined in ITU-1770-4.
@end table

@end table

@item annotations
A key=value string string describing the mix where "key" is a string conforming to BCP-47
that specifies the language for the "value" string. "key" must be the same as the ones in
all sub-mix element's @var{annotations}s
@end table

@end table

E.g. to create an scalable 5.1 IAMF file from several WAV input files
@example
ffmpeg -i front.wav -i back.wav -i center.wav -i lfe.wav
-map 0:0 -map 1:0 -map 2:0 -map 3:0 -c:a opus
-stream_group type=iamf_audio_element:id=1:st=0:st=1:st=2:st=3,
demixing=parameter_id=998,
recon_gain=parameter_id=101,
layer=ch_layout=stereo,
layer=ch_layout=5.1,
-stream_group type=iamf_mix_presentation:id=2:stg=0:annotations=en-us=Mix_Presentation,
submix=parameter_id=100:parameter_rate=48000|element=stg=0:parameter_id=100:annotations=en-us=Scalable_Submix|layout=sound_system=stereo|layout=sound_system=5.1
-streamid 0:0 -streamid 1:1 -streamid 2:2 -streamid 3:3 output.iamf
@end example

To copy the two stream groups (Audio Element and Mix Presentation) from an input IAMF file with four
streams into an mp4 output
@example
ffmpeg -i input.iamf -c:a copy -stream_group map=0=0:st=0:st=1:st=2:st=3 -stream_group map=0=1:stg=0
-streamid 0:0 -streamid 1:1 -streamid 2:2 -streamid 3:3 output.mp4
@end example

@item -target @var{type} (@emph{output})
Specify target file type (@code{vcd}, @code{svcd}, @code{dvd}, @code{dv},
@code{dv50}). @var{type} may be prefixed with @code{pal-}, @code{ntsc-} or
@code{film-} to use the corresponding standard. All the format options
(bitrate, codecs, buffer sizes) are then set automatically. You can just type:

@example
ffmpeg -i myfile.avi -target vcd /tmp/vcd.mpg
@end example

Nevertheless you can specify additional options as long as you know
they do not conflict with the standard, as in:

@example
ffmpeg -i myfile.avi -target vcd -bf 2 /tmp/vcd.mpg
@end example

The parameters set for each target are as follows.

@strong{VCD}
@example
@var{pal}:
-f vcd -muxrate 1411200 -muxpreload 0.44 -packetsize 2324
-s 352x288 -r 25
-codec:v mpeg1video -g 15 -b:v 1150k -maxrate:v 1150k -minrate:v 1150k -bufsize:v 327680
-ar 44100 -ac 2
-codec:a mp2 -b:a 224k

@var{ntsc}:
-f vcd -muxrate 1411200 -muxpreload 0.44 -packetsize 2324
-s 352x240 -r 30000/1001
-codec:v mpeg1video -g 18 -b:v 1150k -maxrate:v 1150k -minrate:v 1150k -bufsize:v 327680
-ar 44100 -ac 2
-codec:a mp2 -b:a 224k

@var{film}:
-f vcd -muxrate 1411200 -muxpreload 0.44 -packetsize 2324
-s 352x240 -r 24000/1001
-codec:v mpeg1video -g 18 -b:v 1150k -maxrate:v 1150k -minrate:v 1150k -bufsize:v 327680
-ar 44100 -ac 2
-codec:a mp2 -b:a 224k
@end example

@strong{SVCD}
@example
@var{pal}:
-f svcd -packetsize 2324
-s 480x576 -pix_fmt yuv420p -r 25
-codec:v mpeg2video -g 15 -b:v 2040k -maxrate:v 2516k -minrate:v 0 -bufsize:v 1835008 -scan_offset 1
-ar 44100
-codec:a mp2 -b:a 224k

@var{ntsc}:
-f svcd -packetsize 2324
-s 480x480 -pix_fmt yuv420p -r 30000/1001
-codec:v mpeg2video -g 18 -b:v 2040k -maxrate:v 2516k -minrate:v 0 -bufsize:v 1835008 -scan_offset 1
-ar 44100
-codec:a mp2 -b:a 224k

@var{film}:
-f svcd -packetsize 2324
-s 480x480 -pix_fmt yuv420p -r 24000/1001
-codec:v mpeg2video -g 18 -b:v 2040k -maxrate:v 2516k -minrate:v 0 -bufsize:v 1835008 -scan_offset 1
-ar 44100
-codec:a mp2 -b:a 224k
@end example

@strong{DVD}
@example
@var{pal}:
-f dvd -muxrate 10080k -packetsize 2048
-s 720x576 -pix_fmt yuv420p -r 25
-codec:v mpeg2video -g 15 -b:v 6000k -maxrate:v 9000k -minrate:v 0 -bufsize:v 1835008
-ar 48000
-codec:a ac3 -b:a 448k

@var{ntsc}:
-f dvd -muxrate 10080k -packetsize 2048
-s 720x480 -pix_fmt yuv420p -r 30000/1001
-codec:v mpeg2video -g 18 -b:v 6000k -maxrate:v 9000k -minrate:v 0 -bufsize:v 1835008
-ar 48000
-codec:a ac3 -b:a 448k

@var{film}:
-f dvd -muxrate 10080k -packetsize 2048
-s 720x480 -pix_fmt yuv420p -r 24000/1001
-codec:v mpeg2video -g 18 -b:v 6000k -maxrate:v 9000k -minrate:v 0 -bufsize:v 1835008
-ar 48000
-codec:a ac3 -b:a 448k
@end example

@strong{DV}
@example
@var{pal}:
-f dv
-s 720x576 -pix_fmt yuv420p -r 25
-ar 48000 -ac 2

@var{ntsc}:
-f dv
-s 720x480 -pix_fmt yuv411p -r 30000/1001
-ar 48000 -ac 2

@var{film}:
-f dv
-s 720x480 -pix_fmt yuv411p -r 24000/1001
-ar 48000 -ac 2
@end example
The @code{dv50} target is identical to the @code{dv} target except that the pixel format set is @code{yuv422p} for all three standards.

Any user-set value for a parameter above will override the target preset value. In that case, the output may
not comply with the target standard.

@item -dn (@emph{input/output})
As an input option, blocks all data streams of a file from being filtered or
being automatically selected or mapped for any output. See @code{-discard}
option to disable streams individually.

As an output option, disables data recording i.e. automatic selection or
mapping of any data stream. For full manual control see the @code{-map}
option.

@item -dframes @var{number} (@emph{output})
Set the number of data frames to output. This is an obsolete alias for
@code{-frames:d}, which you should use instead.

@item -frames[:@var{stream_specifier}] @var{framecount} (@emph{output,per-stream})
Stop writing to the stream after @var{framecount} frames.

@item -q[:@var{stream_specifier}] @var{q} (@emph{output,per-stream})
@itemx -qscale[:@var{stream_specifier}] @var{q} (@emph{output,per-stream})
Use fixed quality scale (VBR). The meaning of @var{q}/@var{qscale} is
codec-dependent.
If @var{qscale} is used without a @var{stream_specifier} then it applies only
to the video stream, this is to maintain compatibility with previous behavior
and as specifying the same codec specific value to 2 different codecs that is
audio and video generally is not what is intended when no stream_specifier is
used.

@anchor{filter_option}
@item -filter[:@var{stream_specifier}] @var{filtergraph} (@emph{output,per-stream})
Create the filtergraph specified by @var{filtergraph} and use it to
filter the stream.

@var{filtergraph} is a description of the filtergraph to apply to
the stream, and must have a single input and a single output of the
same type of the stream. In the filtergraph, the input is associated
to the label @code{in}, and the output to the label @code{out}. See
the ffmpeg-filters manual for more information about the filtergraph
syntax.

See the @ref{filter_complex_option,,-filter_complex option} if you
want to create filtergraphs with multiple inputs and/or outputs.

@item -reinit_filter[:@var{stream_specifier}] @var{integer} (@emph{input,per-stream})
This boolean option determines if the filtergraph(s) to which this stream is fed gets
reinitialized when input frame parameters change mid-stream. This option is enabled by
default as most video and all audio filters cannot handle deviation in input frame properties.
Upon reinitialization, existing filter state is lost, like e.g. the frame count @code{n}
reference available in some filters. Any frames buffered at time of reinitialization are lost.
The properties where a change triggers reinitialization are,
for video, frame resolution or pixel format;
for audio, sample format, sample rate, channel count or channel layout.

@item -filter_threads @var{nb_threads} (@emph{global})
Defines how many threads are used to process a filter pipeline. Each pipeline
will produce a thread pool with this many threads available for parallel processing.
The default is the number of available CPUs.

@item -pre[:@var{stream_specifier}] @var{preset_name} (@emph{output,per-stream})
Specify the preset for matching stream(s).

@item -stats (@emph{global})
Print encoding progress/statistics. It is on by default, to explicitly
disable it you need to specify @code{-nostats}.

@item -stats_period @var{time} (@emph{global})
Set period at which encoding progress/statistics are updated. Default is 0.5 seconds.

@item -progress @var{url} (@emph{global})
Send program-friendly progress information to @var{url}.

Progress information is written periodically and at the end of
the encoding process. It is made of "@var{key}=@var{value}" lines. @var{key}
consists of only alphanumeric characters. The last key of a sequence of
progress information is always "progress".

The update period is set using @code{-stats_period}.

@anchor{stdin option}
@item -stdin
Enable interaction on standard input. On by default unless standard input is
used as an input. To explicitly disable interaction you need to specify
@code{-nostdin}.

Disabling interaction on standard input is useful, for example, if
ffmpeg is in the background process group. Roughly the same result can
be achieved with @code{ffmpeg ... < /dev/null} but it requires a
shell.

@item -debug_ts (@emph{global})
Print timestamp/latency information. It is off by default. This option is
mostly useful for testing and debugging purposes, and the output
format may change from one version to another, so it should not be
employed by portable scripts.

See also the option @code{-fdebug ts}.

@item -attach @var{filename} (@emph{output})
Add an attachment to the output file. This is supported by a few formats
like Matroska for e.g. fonts used in rendering subtitles. Attachments
are implemented as a specific type of stream, so this option will add
a new stream to the file. It is then possible to use per-stream options
on this stream in the usual way. Attachment streams created with this
option will be created after all the other streams (i.e. those created
with @code{-map} or automatic mappings).

Note that for Matroska you also have to set the mimetype metadata tag:
@example
ffmpeg -i INPUT -attach DejaVuSans.ttf -metadata:s:2 mimetype=application/x-truetype-font out.mkv
@end example
(assuming that the attachment stream will be third in the output file).

@item -dump_attachment[:@var{stream_specifier}] @var{filename} (@emph{input,per-stream})
Extract the matching attachment stream into a file named @var{filename}. If
@var{filename} is empty, then the value of the @code{filename} metadata tag
will be used.

E.g. to extract the first attachment to a file named 'out.ttf':
@example
ffmpeg -dump_attachment:t:0 out.ttf -i INPUT
@end example
To extract all attachments to files determined by the @code{filename} tag:
@example
ffmpeg -dump_attachment:t "" -i INPUT
@end example

Technical note -- attachments are implemented as codec extradata, so this
option can actually be used to extract extradata from any stream, not just
attachments.
@end table

@section Video Options

@table @option
@item -vframes @var{number} (@emph{output})
Set the number of video frames to output. This is an obsolete alias for
@code{-frames:v}, which you should use instead.
@item -r[:@var{stream_specifier}] @var{fps} (@emph{input/output,per-stream})
Set frame rate (Hz value, fraction or abbreviation).

As an input option, ignore any timestamps stored in the file and instead
generate timestamps assuming constant frame rate @var{fps}.
This is not the same as the @option{-framerate} option used for some input formats
like image2 or v4l2 (it used to be the same in older versions of FFmpeg).
If in doubt use @option{-framerate} instead of the input option @option{-r}.

As an output option:
@table @option
@item video encoding
Duplicate or drop frames right before encoding them to achieve constant output
frame rate @var{fps}.

@item video streamcopy
Indicate to the muxer that @var{fps} is the stream frame rate. No data is
dropped or duplicated in this case. This may produce invalid files if @var{fps}
does not match the actual stream frame rate as determined by packet timestamps.
See also the @code{setts} bitstream filter.

@end table

@item -fpsmax[:@var{stream_specifier}] @var{fps} (@emph{output,per-stream})
Set maximum frame rate (Hz value, fraction or abbreviation).

Clamps output frame rate when output framerate is auto-set and is higher than this value.
Useful in batch processing or when input framerate is wrongly detected as very high.
It cannot be set together with @code{-r}. It is ignored during streamcopy.

@item -s[:@var{stream_specifier}] @var{size} (@emph{input/output,per-stream})
Set frame size.

As an input option, this is a shortcut for the @option{video_size} private
option, recognized by some demuxers for which the frame size is either not
stored in the file or is configurable -- e.g. raw video or video grabbers.

As an output option, this inserts the @code{scale} video filter to the
@emph{end} of the corresponding filtergraph. Please use the @code{scale} filter
directly to insert it at the beginning or some other place.

The format is @samp{wxh} (default - same as source).

@item -aspect[:@var{stream_specifier}] @var{aspect} (@emph{output,per-stream})
Set the video display aspect ratio specified by @var{aspect}.

@var{aspect} can be a floating point number string, or a string of the
form @var{num}:@var{den}, where @var{num} and @var{den} are the
numerator and denominator of the aspect ratio. For example "4:3",
"16:9", "1.3333", and "1.7777" are valid argument values.

If used together with @option{-vcodec copy}, it will affect the aspect ratio
stored at container level, but not the aspect ratio stored in encoded
frames, if it exists.

@item -display_rotation[:@var{stream_specifier}] @var{rotation} (@emph{input,per-stream})
Set video rotation metadata.

@var{rotation} is a decimal number specifying the amount in degree by
which the video should be rotated counter-clockwise before being
displayed.

This option overrides the rotation/display transform metadata stored in
the file, if any. When the video is being transcoded (rather than
copied) and @code{-autorotate} is enabled, the video will be rotated at
the filtering stage. Otherwise, the metadata will be written into the
output file if the muxer supports it.

If the @code{-display_hflip} and/or @code{-display_vflip} options are
given, they are applied after the rotation specified by this option.

@item -display_hflip[:@var{stream_specifier}] (@emph{input,per-stream})
Set whether on display the image should be horizontally flipped.

See the @code{-display_rotation} option for more details.

@item -display_vflip[:@var{stream_specifier}] (@emph{input,per-stream})
Set whether on display the image should be vertically flipped.

See the @code{-display_rotation} option for more details.

@item -vn (@emph{input/output})
As an input option, blocks all video streams of a file from being filtered or
being automatically selected or mapped for any output. See @code{-discard}
option to disable streams individually.

As an output option, disables video recording i.e. automatic selection or
mapping of any video stream. For full manual control see the @code{-map}
option.

@item -vcodec @var{codec} (@emph{output})
Set the video codec. This is an alias for @code{-codec:v}.

@item -pass[:@var{stream_specifier}] @var{n} (@emph{output,per-stream})
Select the pass number (1 or 2). It is used to do two-pass
video encoding. The statistics of the video are recorded in the first
pass into a log file (see also the option -passlogfile),
and in the second pass that log file is used to generate the video
at the exact requested bitrate.
On pass 1, you may just deactivate audio and set output to null,
examples for Windows and Unix:
@example
ffmpeg -i foo.mov -c:v libxvid -pass 1 -an -f rawvideo -y NUL
ffmpeg -i foo.mov -c:v libxvid -pass 1 -an -f rawvideo -y /dev/null
@end example

@item -passlogfile[:@var{stream_specifier}] @var{prefix} (@emph{output,per-stream})
Set two-pass log file name prefix to @var{prefix}, the default file name
prefix is ``ffmpeg2pass''. The complete file name will be
@file{PREFIX-N.log}, where N is a number specific to the output
stream

@item -vf @var{filtergraph} (@emph{output})
Create the filtergraph specified by @var{filtergraph} and use it to
filter the stream.

This is an alias for @code{-filter:v}, see the @ref{filter_option,,-filter option}.

@item -autorotate
Automatically rotate the video according to file metadata. Enabled by
default, use @option{-noautorotate} to disable it.

@item -autoscale
Automatically scale the video according to the resolution of first frame.
Enabled by default, use @option{-noautoscale} to disable it. When autoscale is
disabled, all output frames of filter graph might not be in the same resolution
and may be inadequate for some encoder/muxer. Therefore, it is not recommended
to disable it unless you really know what you are doing.
Disable autoscale at your own risk.
@end table

@section Advanced Video options

@table @option
@item -pix_fmt[:@var{stream_specifier}] @var{format} (@emph{input/output,per-stream})
Set pixel format. Use @code{-pix_fmts} to show all the supported
pixel formats.
If the selected pixel format can not be selected, ffmpeg will print a
warning and select the best pixel format supported by the encoder.
If @var{pix_fmt} is prefixed by a @code{+}, ffmpeg will exit with an error
if the requested pixel format can not be selected, and automatic conversions
inside filtergraphs are disabled.
If @var{pix_fmt} is a single @code{+}, ffmpeg selects the same pixel format
as the input (or graph output) and automatic conversions are disabled.

@item -sws_flags @var{flags} (@emph{input/output})
Set default flags for the libswscale library. These flags are used by
automatically inserted @code{scale} filters and those within simple
filtergraphs, if not overridden within the filtergraph definition.

See the @ref{scaler_options,,ffmpeg-scaler manual,ffmpeg-scaler} for a list
of scaler options.

@item -rc_override[:@var{stream_specifier}] @var{override} (@emph{output,per-stream})
Rate control override for specific intervals, formatted as "int,int,int"
list separated with slashes. Two first values are the beginning and
end frame numbers, last one is quantizer to use if positive, or quality
factor if negative.

@item -vstats
Dump video coding statistics to @file{vstats_HHMMSS.log}. See the
@ref{vstats_file_format,,vstats file format} section for the format description.

@item -vstats_file @var{file}
Dump video coding statistics to @var{file}. See the
@ref{vstats_file_format,,vstats file format} section for the format description.

@item -vstats_version @var{file}
Specify which version of the vstats format to use. Default is @code{2}. See the
@ref{vstats_file_format,,vstats file format} section for the format description.

@item -vtag @var{fourcc/tag} (@emph{output})
Force video tag/fourcc. This is an alias for @code{-tag:v}.

@item -force_key_frames[:@var{stream_specifier}] @var{time}[,@var{time}...] (@emph{output,per-stream})
@item -force_key_frames[:@var{stream_specifier}] expr:@var{expr} (@emph{output,per-stream})
@item -force_key_frames[:@var{stream_specifier}] source (@emph{output,per-stream})

@var{force_key_frames} can take arguments of the following form:

@table @option

@item @var{time}[,@var{time}...]
If the argument consists of timestamps, ffmpeg will round the specified times to the nearest
output timestamp as per the encoder time base and force a keyframe at the first frame having
timestamp equal or greater than the computed timestamp. Note that if the encoder time base is too
coarse, then the keyframes may be forced on frames with timestamps lower than the specified time.
The default encoder time base is the inverse of the output framerate but may be set otherwise
via @code{-enc_time_base}.

If one of the times is "@code{chapters}[@var{delta}]", it is expanded into
the time of the beginning of all chapters in the file, shifted by
@var{delta}, expressed as a time in seconds.
This option can be useful to ensure that a seek point is present at a
chapter mark or any other designated place in the output file.

For example, to insert a key frame at 5 minutes, plus key frames 0.1 second
before the beginning of every chapter:
@example
-force_key_frames 0:05:00,chapters-0.1
@end example

@item expr:@var{expr}
If the argument is prefixed with @code{expr:}, the string @var{expr}
is interpreted like an expression and is evaluated for each frame. A
key frame is forced in case the evaluation is non-zero.

The expression in @var{expr} can contain the following constants:
@table @option
@item n
the number of current processed frame, starting from 0
@item n_forced
the number of forced frames
@item prev_forced_n
the number of the previous forced frame, it is @code{NAN} when no
keyframe was forced yet
@item prev_forced_t
the time of the previous forced frame, it is @code{NAN} when no
keyframe was forced yet
@item t
the time of the current processed frame
@end table

For example to force a key frame every 5 seconds, you can specify:
@example
-force_key_frames expr:gte(t,n_forced*5)
@end example

To force a key frame 5 seconds after the time of the last forced one,
starting from second 13:
@example
-force_key_frames expr:if(isnan(prev_forced_t),gte(t,13),gte(t,prev_forced_t+5))
@end example

@item source
If the argument is @code{source}, ffmpeg will force a key frame if
the current frame being encoded is marked as a key frame in its source.
In cases where this particular source frame has to be dropped,
enforce the next available frame to become a key frame instead.

@end table

Note that forcing too many keyframes is very harmful for the lookahead
algorithms of certain encoders: using fixed-GOP options or similar
would be more efficient.

@item -apply_cropping[:@var{stream_specifier}] @var{source} (@emph{input,per-stream})
Automatically crop the video after decoding according to file metadata.
Default is @emph{all}.

@table @option
@item none (0)
Don't apply any cropping metadata.
@item all (1)
Apply both codec and container level croppping. This is the default mode.
@item codec (2)
Apply codec level croppping.
@item container (3)
Apply container level croppping.
@end table

@item -copyinkf[:@var{stream_specifier}] (@emph{output,per-stream})
When doing stream copy, copy also non-key frames found at the
beginning.

@item -init_hw_device @var{type}[=@var{name}][:@var{device}[,@var{key=value}...]]
Initialise a new hardware device of type @var{type} called @var{name}, using the
given device parameters.
If no name is specified it will receive a default name of the form "@var{type}%d".

The meaning of @var{device} and the following arguments depends on the
device type:
@table @option

@item cuda
@var{device} is the number of the CUDA device.

The following options are recognized:
@table @option
@item primary_ctx
If set to 1, uses the primary device context instead of creating a new one.
@end table

Examples:
@table @emph
@item -init_hw_device cuda:1
Choose the second device on the system.

@item -init_hw_device cuda:0,primary_ctx=1
Choose the first device and use the primary device context.
@end table

@item dxva2
@var{device} is the number of the Direct3D 9 display adapter.

@item d3d11va
@var{device} is the number of the Direct3D 11 display adapter.
If not specified, it will attempt to use the default Direct3D 11 display adapter
or the first Direct3D 11 display adapter whose hardware VendorId is specified
by @samp{vendor_id}.

Examples:
@table @emph
@item -init_hw_device d3d11va
Create a d3d11va device on the default Direct3D 11 display adapter.

@item -init_hw_device d3d11va:1
Create a d3d11va device on the Direct3D 11 display adapter specified by index 1.

@item -init_hw_device d3d11va:,vendor_id=0x8086
Create a d3d11va device on the first Direct3D 11 display adapter whose hardware VendorId is 0x8086.
@end table

@item vaapi
@var{device} is either an X11 display name, a DRM render node or a DirectX adapter index.
If not specified, it will attempt to open the default X11 display (@emph{$DISPLAY})
and then the first DRM render node (@emph{/dev/dri/renderD128}), or the default
DirectX adapter on Windows.

The following options are recognized:
@table @option
@item kernel_driver
When @var{device} is not specified, use this option to specify the name of the kernel
driver associated with the desired device. This option is available only when
the hardware acceleration method @emph{drm} and @emph{vaapi} are enabled.
@item vendor_id
When @var{device} and @var{kernel_driver} are not specified, use this option to specify
the vendor id associated with the desired device. This option is available only when the
hardware acceleration method @emph{drm} and @emph{vaapi} are enabled and @emph{kernel_driver}
is not specified.
@end table

Examples:
@table @emph
@item -init_hw_device vaapi
Create a vaapi device on the default device.

@item -init_hw_device vaapi:/dev/dri/renderD129
Create a vaapi device on DRM render node @file{/dev/dri/renderD129}.

@item -init_hw_device vaapi:1
Create a vaapi device on DirectX adapter 1.

@item -init_hw_device vaapi:,kernel_driver=i915
Create a vaapi device on a device associated with kernel driver @samp{i915}.

@item -init_hw_device vaapi:,vendor_id=0x8086
Create a vaapi device on a device associated with vendor id @samp{0x8086}.
@end table

@item vdpau
@var{device} is an X11 display name.
If not specified, it will attempt to open the default X11 display (@emph{$DISPLAY}).

@item qsv
@var{device} selects a value in @samp{MFX_IMPL_*}. Allowed values are:
@table @option
@item auto
@item sw
@item hw
@item auto_any
@item hw_any
@item hw2
@item hw3
@item hw4
@end table
If not specified, @samp{auto_any} is used.
(Note that it may be easier to achieve the desired result for QSV by creating the
platform-appropriate subdevice (@samp{dxva2} or @samp{d3d11va} or @samp{vaapi}) and then deriving a
QSV device from that.)

The following options are recognized:
@table @option
@item child_device
Specify a DRM render node on Linux or DirectX adapter on Windows.
@item child_device_type
Choose platform-appropriate subdevice type. On Windows @samp{d3d11va} is used
as default subdevice type when @code{--enable-libvpl} is specified at configuration time,
@samp{dxva2} is used as default subdevice type when @code{--enable-libmfx} is specified at
configuration time. On Linux user can use @samp{vaapi} only as subdevice type.
@end table

Examples:
@table @emph
@item -init_hw_device qsv:hw,child_device=/dev/dri/renderD129
Create a QSV device with @samp{MFX_IMPL_HARDWARE} on DRM render node @file{/dev/dri/renderD129}.

@item -init_hw_device qsv:hw,child_device=1
Create a QSV device with @samp{MFX_IMPL_HARDWARE} on DirectX adapter 1.

@item -init_hw_device qsv:hw,child_device_type=d3d11va
Choose the GPU subdevice with type @samp{d3d11va} and create QSV device with @samp{MFX_IMPL_HARDWARE}.

@item -init_hw_device qsv:hw,child_device_type=dxva2
Choose the GPU subdevice with type @samp{dxva2} and create QSV device with @samp{MFX_IMPL_HARDWARE}.

@item -init_hw_device qsv:hw,child_device=1,child_device_type=d3d11va
Create a QSV device with @samp{MFX_IMPL_HARDWARE} on DirectX adapter 1 with subdevice type @samp{d3d11va}.

@item -init_hw_device vaapi=va:/dev/dri/renderD129 -init_hw_device qsv=hw1@@@var{va}
Create a VAAPI device called @samp{va} on @file{/dev/dri/renderD129}, then derive a QSV device called @samp{hw1}
from device @samp{va}.

@end table

@item opencl
@var{device} selects the platform and device as @emph{platform_index.device_index}.

The set of devices can also be filtered using the key-value pairs to find only
devices matching particular platform or device strings.

The strings usable as filters are:
@table @option
@item platform_profile
@item platform_version
@item platform_name
@item platform_vendor
@item platform_extensions
@item device_name
@item device_vendor
@item driver_version
@item device_version
@item device_profile
@item device_extensions
@item device_type
@end table

The indices and filters must together uniquely select a device.

Examples:
@table @emph
@item -init_hw_device opencl:0.1
Choose the second device on the first platform.

@item -init_hw_device opencl:,device_name=Foo9000
Choose the device with a name containing the string @emph{Foo9000}.

@item -init_hw_device opencl:1,device_type=gpu,device_extensions=cl_khr_fp16
Choose the GPU device on the second platform supporting the @emph{cl_khr_fp16}
extension.
@end table

@item vulkan
If @var{device} is an integer, it selects the device by its index in a
system-dependent list of devices.  If @var{device} is any other string, it
selects the first device with a name containing that string as a substring.

The following options are recognized:
@table @option
@item debug
If set to 1, enables the validation layer, if installed.
@item linear_images
If set to 1, images allocated by the hwcontext will be linear and locally mappable.
@item instance_extensions
A plus separated list of additional instance extensions to enable.
@item device_extensions
A plus separated list of additional device extensions to enable.
@end table

Examples:
@table @emph
@item -init_hw_device vulkan:1
Choose the second device on the system.

@item -init_hw_device vulkan:RADV
Choose the first device with a name containing the string @emph{RADV}.

@item -init_hw_device vulkan:0,instance_extensions=VK_KHR_wayland_surface+VK_KHR_xcb_surface
Choose the first device and enable the Wayland and XCB instance extensions.
@end table

@end table

@item -init_hw_device @var{type}[=@var{name}]@@@var{source}
Initialise a new hardware device of type @var{type} called @var{name},
deriving it from the existing device with the name @var{source}.

@item -init_hw_device list
List all hardware device types supported in this build of ffmpeg.

@item -filter_hw_device @var{name}
Pass the hardware device called @var{name} to all filters in any filter graph.
This can be used to set the device to upload to with the @code{hwupload} filter,
or the device to map to with the @code{hwmap} filter.  Other filters may also
make use of this parameter when they require a hardware device.  Note that this
is typically only required when the input is not already in hardware frames -
when it is, filters will derive the device they require from the context of the
frames they receive as input.

This is a global setting, so all filters will receive the same device.

@item -hwaccel[:@var{stream_specifier}] @var{hwaccel} (@emph{input,per-stream})
Use hardware acceleration to decode the matching stream(s). The allowed values
of @var{hwaccel} are:
@table @option
@item none
Do not use any hardware acceleration (the default).

@item auto
Automatically select the hardware acceleration method.

@item vdpau
Use VDPAU (Video Decode and Presentation API for Unix) hardware acceleration.

@item dxva2
Use DXVA2 (DirectX Video Acceleration) hardware acceleration.

@item d3d11va
Use D3D11VA (DirectX Video Acceleration) hardware acceleration.

@item vaapi
Use VAAPI (Video Acceleration API) hardware acceleration.

@item qsv
Use the Intel QuickSync Video acceleration for video transcoding.

Unlike most other values, this option does not enable accelerated decoding (that
is used automatically whenever a qsv decoder is selected), but accelerated
transcoding, without copying the frames into the system memory.

For it to work, both the decoder and the encoder must support QSV acceleration
and no filters must be used.
@end table

This option has no effect if the selected hwaccel is not available or not
supported by the chosen decoder.

Note that most acceleration methods are intended for playback and will not be
faster than software decoding on modern CPUs. Additionally, @command{ffmpeg}
will usually need to copy the decoded frames from the GPU memory into the system
memory, resulting in further performance loss. This option is thus mainly
useful for testing.

@item -hwaccel_device[:@var{stream_specifier}] @var{hwaccel_device} (@emph{input,per-stream})
Select a device to use for hardware acceleration.

This option only makes sense when the @option{-hwaccel} option is also specified.
It can either refer to an existing device created with @option{-init_hw_device}
by name, or it can create a new device as if
@samp{-init_hw_device} @var{type}:@var{hwaccel_device}
were called immediately before.

@item -hwaccels
List all hardware acceleration components enabled in this build of ffmpeg.
Actual runtime availability depends on the hardware and its suitable driver
being installed.

@item -fix_sub_duration_heartbeat[:@var{stream_specifier}]
Set a specific output video stream as the heartbeat stream according to which
to split and push through currently in-progress subtitle upon receipt of a
random access packet.

This lowers the latency of subtitles for which the end packet or the following
subtitle has not yet been received. As a drawback, this will most likely lead
to duplication of subtitle events in order to cover the full duration, so
when dealing with use cases where latency of when the subtitle event is passed
on to output is not relevant this option should not be utilized.

Requires @option{-fix_sub_duration} to be set for the relevant input subtitle
stream for this to have any effect, as well as for the input subtitle stream
having to be directly mapped to the same output in which the heartbeat stream
resides.

@end table

@section Audio Options

@table @option
@item -aframes @var{number} (@emph{output})
Set the number of audio frames to output. This is an obsolete alias for
@code{-frames:a}, which you should use instead.
@item -ar[:@var{stream_specifier}] @var{freq} (@emph{input/output,per-stream})
Set the audio sampling frequency. For output streams it is set by
default to the frequency of the corresponding input stream. For input
streams this option only makes sense for audio grabbing devices and raw
demuxers and is mapped to the corresponding demuxer options.
@item -aq @var{q} (@emph{output})
Set the audio quality (codec-specific, VBR). This is an alias for -q:a.
@item -ac[:@var{stream_specifier}] @var{channels} (@emph{input/output,per-stream})
Set the number of audio channels. For output streams it is set by
default to the number of input audio channels. For input streams
this option only makes sense for audio grabbing devices and raw demuxers
and is mapped to the corresponding demuxer options.
@item -an (@emph{input/output})
As an input option, blocks all audio streams of a file from being filtered or
being automatically selected or mapped for any output. See @code{-discard}
option to disable streams individually.

As an output option, disables audio recording i.e. automatic selection or
mapping of any audio stream. For full manual control see the @code{-map}
option.
@item -acodec @var{codec} (@emph{input/output})
Set the audio codec. This is an alias for @code{-codec:a}.
@item -sample_fmt[:@var{stream_specifier}] @var{sample_fmt} (@emph{output,per-stream})
Set the audio sample format. Use @code{-sample_fmts} to get a list
of supported sample formats.

@item -af @var{filtergraph} (@emph{output})
Create the filtergraph specified by @var{filtergraph} and use it to
filter the stream.

This is an alias for @code{-filter:a}, see the @ref{filter_option,,-filter option}.
@end table

@section Advanced Audio options

@table @option
@item -atag @var{fourcc/tag} (@emph{output})
Force audio tag/fourcc. This is an alias for @code{-tag:a}.
@item -ch_layout[:@var{stream_specifier}] @var{layout} (@emph{input/output,per-stream})
Alias for @code{-channel_layout}.
@item -channel_layout[:@var{stream_specifier}] @var{layout} (@emph{input/output,per-stream})
Set the audio channel layout. For output streams it is set by default to the
input channel layout. For input streams it overrides the channel layout of the
input. Not all decoders respect the overridden channel layout. This option
also sets the channel layout for audio grabbing devices and raw demuxers
and is mapped to the corresponding demuxer option.
@item -guess_layout_max @var{channels} (@emph{input,per-stream})
If some input channel layout is not known, try to guess only if it
corresponds to at most the specified number of channels. For example, 2
tells to @command{ffmpeg} to recognize 1 channel as mono and 2 channels as
stereo but not 6 channels as 5.1. The default is to always try to guess. Use
0 to disable all guessing. Using the @code{-channel_layout} option to
explicitly specify an input layout also disables guessing.
@end table

@section Subtitle options

@table @option
@item -scodec @var{codec} (@emph{input/output})
Set the subtitle codec. This is an alias for @code{-codec:s}.
@item -sn (@emph{input/output})
As an input option, blocks all subtitle streams of a file from being filtered or
being automatically selected or mapped for any output. See @code{-discard}
option to disable streams individually.

As an output option, disables subtitle recording i.e. automatic selection or
mapping of any subtitle stream. For full manual control see the @code{-map}
option.
@end table

@section Advanced Subtitle options

@table @option

@item -fix_sub_duration
Fix subtitles durations. For each subtitle, wait for the next packet in the
same stream and adjust the duration of the first to avoid overlap. This is
necessary with some subtitles codecs, especially DVB subtitles, because the
duration in the original packet is only a rough estimate and the end is
actually marked by an empty subtitle frame. Failing to use this option when
necessary can result in exaggerated durations or muxing failures due to
non-monotonic timestamps.

Note that this option will delay the output of all data until the next
subtitle packet is decoded: it may increase memory consumption and latency a
lot.

@item -canvas_size @var{size}
Set the size of the canvas used to render subtitles.

@end table

@section Advanced options

@table @option
@item -map [-]@var{input_file_id}[:@var{stream_specifier}][:@var{view_specifier}][:?] | @var{[linklabel]} (@emph{output})

Create one or more streams in the output file. This option has two forms for
specifying the data source(s): the first selects one or more streams from some
input file (specified with @code{-i}), the second takes an output from some
complex filtergraph (specified with @code{-filter_complex}).

In the first form, an output stream is created for every stream from the input
file with the index @var{input_file_id}. If @var{stream_specifier} is given,
only those streams that match the specifier are used (see the
@ref{Stream specifiers} section for the @var{stream_specifier} syntax).

A @code{-} character before the stream identifier creates a "negative" mapping.
It disables matching streams from already created mappings.

An optional @var{view_specifier} may be given after the stream specifier, which
for multiview video specifies the view to be used. The view specifier may have
one of the following formats:
@table @option
@item view:@var{view_id}
select a view by its ID; @var{view_id} may be set to 'all' to use all the views
interleaved into one stream;

@item vidx:@var{view_idx}
select a view by its index; i.e. 0 is the base view, 1 is the first non-base
view, etc.

@item vpos:@var{position}
select a view by its display position; @var{position} may be @code{left} or
@code{right}
@end table
The default for transcoding is to only use the base view, i.e. the equivalent of
@code{vidx:0}. For streamcopy, view specifiers are not supported and all views
are always copied.

A trailing @code{?} after the stream index will allow the map to be
optional: if the map matches no streams the map will be ignored instead
of failing. Note the map will still fail if an invalid input file index
is used; such as if the map refers to a non-existent input.

An alternative @var{[linklabel]} form will map outputs from complex filter
graphs (see the @option{-filter_complex} option) to the output file.
@var{linklabel} must correspond to a defined output link label in the graph.

This option may be specified multiple times, each adding more streams to the
output file. Any given input stream may also be mapped any number of times as a
source for different output streams, e.g. in order to use different encoding
options and/or filters. The streams are created in the output in the same order
in which the @code{-map} options are given on the commandline.

Using this option disables the default mappings for this output file.

Examples:

@table @emph

@item map everything
To map ALL streams from the first input file to output
@example
ffmpeg -i INPUT -map 0 output
@end example

@item select specific stream
If you have two audio streams in the first input file, these streams are
identified by @var{0:0} and @var{0:1}. You can use @code{-map} to select which
streams to place in an output file. For example:
@example
ffmpeg -i INPUT -map 0:1 out.wav
@end example
will map the second input stream in @file{INPUT} to the (single) output stream
in @file{out.wav}.

@item create multiple streams
To select the stream with index 2 from input file @file{a.mov} (specified by the
identifier @var{0:2}), and stream with index 6 from input @file{b.mov}
(specified by the identifier @var{1:6}), and copy them to the output file
@file{out.mov}:
@example
ffmpeg -i a.mov -i b.mov -c copy -map 0:2 -map 1:6 out.mov
@end example

@item create multiple streams 2
To select all video and the third audio stream from an input file:
@example
ffmpeg -i INPUT -map 0:v -map 0:a:2 OUTPUT
@end example

@item negative map
To map all the streams except the second audio, use negative mappings
@example
ffmpeg -i INPUT -map 0 -map -0:a:1 OUTPUT
@end example

@item optional map
To map the video and audio streams from the first input, and using the
trailing @code{?}, ignore the audio mapping if no audio streams exist in
the first input:
@example
ffmpeg -i INPUT -map 0:v -map 0:a? OUTPUT
@end example

@item map by language
To pick the English audio stream:
@example
ffmpeg -i INPUT -map 0:m:language:eng OUTPUT
@end example

@end table

@item -ignore_unknown
Ignore input streams with unknown type instead of failing if copying
such streams is attempted.

@item -copy_unknown
Allow input streams with unknown type to be copied instead of failing if copying
such streams is attempted.

@item -map_metadata[:@var{metadata_spec_out}] @var{infile}[:@var{metadata_spec_in}] (@emph{output,per-metadata})
Set metadata information of the next output file from @var{infile}. Note that
those are file indices (zero-based), not filenames.
Optional @var{metadata_spec_in/out} parameters specify, which metadata to copy.
A metadata specifier can have the following forms:
@table @option
@item @var{g}
global metadata, i.e. metadata that applies to the whole file

@item @var{s}[:@var{stream_spec}]
per-stream metadata. @var{stream_spec} is a stream specifier as described
in the @ref{Stream specifiers} chapter. In an input metadata specifier, the first
matching stream is copied from. In an output metadata specifier, all matching
streams are copied to.

@item @var{c}:@var{chapter_index}
per-chapter metadata. @var{chapter_index} is the zero-based chapter index.

@item @var{p}:@var{program_index}
per-program metadata. @var{program_index} is the zero-based program index.
@end table
If metadata specifier is omitted, it defaults to global.

By default, global metadata is copied from the first input file,
per-stream and per-chapter metadata is copied along with streams/chapters. These
default mappings are disabled by creating any mapping of the relevant type. A negative
file index can be used to create a dummy mapping that just disables automatic copying.

For example to copy metadata from the first stream of the input file to global metadata
of the output file:
@example
ffmpeg -i in.ogg -map_metadata 0:s:0 out.mp3
@end example

To do the reverse, i.e. copy global metadata to all audio streams:
@example
ffmpeg -i in.mkv -map_metadata:s:a 0:g out.mkv
@end example
Note that simple @code{0} would work as well in this example, since global
metadata is assumed by default.

@item -map_chapters @var{input_file_index} (@emph{output})
Copy chapters from input file with index @var{input_file_index} to the next
output file. If no chapter mapping is specified, then chapters are copied from
the first input file with at least one chapter. Use a negative file index to
disable any chapter copying.

@item -benchmark (@emph{global})
Show benchmarking information at the end of an encode.
Shows real, system and user time used and maximum memory consumption.
Maximum memory consumption is not supported on all systems,
it will usually display as 0 if not supported.
@item -benchmark_all (@emph{global})
Show benchmarking information during the encode.
Shows real, system and user time used in various steps (audio/video encode/decode).
@item -timelimit @var{duration} (@emph{global})
Exit after ffmpeg has been running for @var{duration} seconds in CPU user time.
@item -dump (@emph{global})
Dump each input packet to stderr.
@item -hex (@emph{global})
When dumping packets, also dump the payload.
@item -readrate @var{speed} (@emph{input})
Limit input read speed.

Its value is a floating-point positive number which represents the maximum duration of
media, in seconds, that should be ingested in one second of wallclock time.
Default value is zero and represents no imposed limitation on speed of ingestion.
Value @code{1} represents real-time speed and is equivalent to @code{-re}.

Mainly used to simulate a capture device or live input stream (e.g. when reading from a file).
Should not be used with a low value when input is an actual capture device or live stream as
it may cause packet loss.

It is useful for when flow speed of output packets is important, such as live streaming.
@item -re (@emph{input})
Read input at native frame rate. This is equivalent to setting @code{-readrate 1}.
@item -readrate_initial_burst @var{seconds}
Set an initial read burst time, in seconds, after which @option{-re/-readrate}
will be enforced.
@item -vsync @var{parameter} (@emph{global})
@itemx -fps_mode[:@var{stream_specifier}] @var{parameter} (@emph{output,per-stream})
Set video sync method / framerate mode. vsync is applied to all output video streams
but can be overridden for a stream by setting fps_mode. vsync is deprecated and will be
removed in the future.

For compatibility reasons some of the values for vsync can be specified as numbers (shown
in parentheses in the following table).

@table @option
@item passthrough (0)
Each frame is passed with its timestamp from the demuxer to the muxer.
@item cfr (1)
Frames will be duplicated and dropped to achieve exactly the requested
constant frame rate.
@item vfr (2)
Frames are passed through with their timestamp or dropped so as to
prevent 2 frames from having the same timestamp.
@item auto (-1)
Chooses between cfr and vfr depending on muxer capabilities. This is the
default method.
@end table

Note that the timestamps may be further modified by the muxer, after this.
For example, in the case that the format option @option{avoid_negative_ts}
is enabled.

With -map you can select from which stream the timestamps should be
taken. You can leave either video or audio unchanged and sync the
remaining stream(s) to the unchanged one.

@item -frame_drop_threshold @var{parameter}
Frame drop threshold, which specifies how much behind video frames can
be before they are dropped. In frame rate units, so 1.0 is one frame.
The default is -1.1. One possible usecase is to avoid framedrops in case
of noisy timestamps or to increase frame drop precision in case of exact
timestamps.

@item -apad @var{parameters} (@emph{output,per-stream})
Pad the output audio stream(s). This is the same as applying @code{-af apad}.
Argument is a string of filter parameters composed the same as with the @code{apad} filter.
@code{-shortest} must be set for this output for the option to take effect.

@item -copyts
Do not process input timestamps, but keep their values without trying
to sanitize them. In particular, do not remove the initial start time
offset value.

Note that, depending on the @option{vsync} option or on specific muxer
processing (e.g. in case the format option @option{avoid_negative_ts}
is enabled) the output timestamps may mismatch with the input
timestamps even when this option is selected.

@item -start_at_zero
When used with @option{copyts}, shift input timestamps so they start at zero.

This means that using e.g. @code{-ss 50} will make output timestamps start at
50 seconds, regardless of what timestamp the input file started at.

@item -copytb @var{mode}
Specify how to set the encoder timebase when stream copying.  @var{mode} is an
integer numeric value, and can assume one of the following values:

@table @option
@item 1
Use the demuxer timebase.

The time base is copied to the output encoder from the corresponding input
demuxer. This is sometimes required to avoid non monotonically increasing
timestamps when copying video streams with variable frame rate.

@item 0
Use the decoder timebase.

The time base is copied to the output encoder from the corresponding input
decoder.

@item -1
Try to make the choice automatically, in order to generate a sane output.
@end table

Default value is -1.

@item -enc_time_base[:@var{stream_specifier}] @var{timebase} (@emph{output,per-stream})
Set the encoder timebase. @var{timebase} can assume one of the following values:

@table @option
@item 0
Assign a default value according to the media type.

For video - use 1/framerate, for audio - use 1/samplerate.

@item demux
Use the timebase from the demuxer.

@item filter
Use the timebase from the filtergraph.

@item a positive number
Use the provided number as the timebase.

This field can be provided as a ratio of two integers (e.g. 1:24, 1:48000)
or as a decimal number (e.g. 0.04166, 2.0833e-5)
@end table

Default value is 0.

@item -bitexact (@emph{input/output})
Enable bitexact mode for (de)muxer and (de/en)coder
@item -shortest (@emph{output})
Finish encoding when the shortest output stream ends.

Note that this option may require buffering frames, which introduces extra
latency. The maximum amount of this latency may be controlled with the
@code{-shortest_buf_duration} option.

@item -shortest_buf_duration @var{duration} (@emph{output})
The @code{-shortest} option may require buffering potentially large amounts
of data when at least one of the streams is "sparse" (i.e. has large gaps
between frames – this is typically the case for subtitles).

This option controls the maximum duration of buffered frames in seconds.
Larger values may allow the @code{-shortest} option to produce more accurate
results, but increase memory use and latency.

The default value is 10 seconds.

@item -dts_delta_threshold @var{threshold}
Timestamp discontinuity delta threshold, expressed as a decimal number
of seconds.

The timestamp discontinuity correction enabled by this option is only
applied to input formats accepting timestamp discontinuity (for which
the @code{AVFMT_TS_DISCONT} flag is enabled), e.g. MPEG-TS and HLS, and
is automatically disabled when employing the @code{-copyts} option
(unless wrapping is detected).

If a timestamp discontinuity is detected whose absolute value is
greater than @var{threshold}, ffmpeg will remove the discontinuity by
decreasing/increasing the current DTS and PTS by the corresponding
delta value.

The default value is 10.

@item -dts_error_threshold @var{threshold}
Timestamp error delta threshold, expressed as a decimal number of
seconds.

The timestamp correction enabled by this option is only applied to
input formats not accepting timestamp discontinuity (for which the
@code{AVFMT_TS_DISCONT} flag is not enabled).

If a timestamp discontinuity is detected whose absolute value is
greater than @var{threshold}, ffmpeg will drop the PTS/DTS timestamp
value.

The default value is @code{3600*30} (30 hours), which is arbitrarily
picked and quite conservative.

@item -muxdelay @var{seconds} (@emph{output})
Set the maximum demux-decode delay.
@item -muxpreload @var{seconds} (@emph{output})
Set the initial demux-decode delay.
@item -streamid @var{output-stream-index}:@var{new-value} (@emph{output})
Assign a new stream-id value to an output stream. This option should be
specified prior to the output filename to which it applies.
For the situation where multiple output files exist, a streamid
may be reassigned to a different value.

For example, to set the stream 0 PID to 33 and the stream 1 PID to 36 for
an output mpegts file:
@example
ffmpeg -i inurl -streamid 0:33 -streamid 1:36 out.ts
@end example

@item -bsf[:@var{stream_specifier}] @var{bitstream_filters} (@emph{input/output,per-stream})
Apply bitstream filters to matching streams. The filters are applied to each
packet as it is received from the demuxer (when used as an input option) or
before it is sent to the muxer (when used as an output option).

@var{bitstream_filters} is a comma-separated list of bitstream filter
specifications, each of the form
@example
@var{filter}[=@var{optname0}=@var{optval0}:@var{optname1}=@var{optval1}:...]
@end example
Any of the ',=:' characters that are to be a part of an option value need to be
escaped with a backslash.

Use the @code{-bsfs} option to get the list of bitstream filters.

E.g.
@example
ffmpeg -bsf:v h264_mp4toannexb -i h264.mp4 -c:v copy -an out.h264
@end example
applies the @code{h264_mp4toannexb} bitstream filter (which converts
MP4-encapsulated H.264 stream to Annex B) to the @emph{input} video stream.

On the other hand,
@example
ffmpeg -i file.mov -an -vn -bsf:s mov2textsub -c:s copy -f rawvideo sub.txt
@end example
applies the @code{mov2textsub} bitstream filter (which extracts text from MOV
subtitles) to the @emph{output} subtitle stream. Note, however, that since both
examples use @code{-c copy}, it matters little whether the filters are applied
on input or output - that would change if transcoding was happening.

@item -tag[:@var{stream_specifier}] @var{codec_tag} (@emph{input/output,per-stream})
Force a tag/fourcc for matching streams.

@item -timecode @var{hh}:@var{mm}:@var{ss}SEP@var{ff}
Specify Timecode for writing. @var{SEP} is ':' for non drop timecode and ';'
(or '.') for drop.
@example
ffmpeg -i input.mpg -timecode 01:02:03.04 -r 30000/1001 -s ntsc output.mpg
@end example

@anchor{filter_complex_option}
@item -filter_complex @var{filtergraph} (@emph{global})
Define a complex filtergraph, i.e. one with arbitrary number of inputs and/or
outputs. For simple graphs -- those with one input and one output of the same
type -- see the @option{-filter} options. @var{filtergraph} is a description of
the filtergraph, as described in the ``Filtergraph syntax'' section of the
ffmpeg-filters manual. This option may be specified multiple times - each use
creates a new complex filtergraph.

Inputs to a complex filtergraph may come from different source types,
distinguished by the format of the corresponding link label:
@itemize
@item
To connect an input stream, use @code{[file_index:stream_specifier]} (i.e. the
same syntax as @option{-map}). If @var{stream_specifier} matches multiple
streams, the first one will be used. For multiview video, the stream specifier
may be followed by the view specifier, see documentation for the @option{-map}
option for its syntax.

@item
To connect a loopback decoder use [dec:@var{dec_idx}], where @var{dec_idx} is
the index of the loopback decoder to be connected to given input. For multiview
video, the decoder index may be followed by the view specifier, see
documentation for the @option{-map} option for its syntax.

@item
To connect an output from another complex filtergraph, use its link label. E.g
the following example:

@example
ffmpeg -i input.mkv \
  -filter_complex '[0:v]scale=size=hd1080,split=outputs=2[for_enc][orig_scaled]' \
  -c:v libx264 -map '[for_enc]' output.mkv \
  -dec 0:0 \
  -filter_complex '[dec:0][orig_scaled]hstack[stacked]' \
  -map '[stacked]' -c:v ffv1 comparison.mkv
@end example

reads an input video and
@itemize
@item
(line 2) uses a complex filtergraph with one input and two outputs
to scale the video to 1920x1080 and duplicate the result to both
outputs;

@item
(line 3) encodes one scaled output with @code{libx264} and writes the result to
@file{output.mkv};

@item
(line 4) decodes this encoded stream with a loopback decoder;

@item
(line 5) places the output of the loopback decoder (i.e. the
@code{libx264}-encoded video) side by side with the scaled original input;

@item
(line 6) combined video is then losslessly encoded and written into
@file{comparison.mkv}.

@end itemize

Note that the two filtergraphs cannot be combined into one, because then there
would be a cycle in the transcoding pipeline (filtergraph output goes to
encoding, from there to decoding, then back to the same graph), and such cycles
are not allowed.

@end itemize

An unlabeled input will be connected to the first unused input stream of the
matching type.

Output link labels are referred to with @option{-map}. Unlabeled outputs are
added to the first output file.

Note that with this option it is possible to use only lavfi sources without
normal input files.

For example, to overlay an image over video
@example
ffmpeg -i video.mkv -i image.png -filter_complex '[0:v][1:v]overlay[out]' -map
'[out]' out.mkv
@end example
Here @code{[0:v]} refers to the first video stream in the first input file,
which is linked to the first (main) input of the overlay filter. Similarly the
first video stream in the second input is linked to the second (overlay) input
of overlay.

Assuming there is only one video stream in each input file, we can omit input
labels, so the above is equivalent to
@example
ffmpeg -i video.mkv -i image.png -filter_complex 'overlay[out]' -map
'[out]' out.mkv
@end example

Furthermore we can omit the output label and the single output from the filter
graph will be added to the output file automatically, so we can simply write
@example
ffmpeg -i video.mkv -i image.png -filter_complex 'overlay' out.mkv
@end example

As a special exception, you can use a bitmap subtitle stream as input: it
will be converted into a video with the same size as the largest video in
the file, or 720x576 if no video is present. Note that this is an
experimental and temporary solution. It will be removed once libavfilter has
proper support for subtitles.

For example, to hardcode subtitles on top of a DVB-T recording stored in
MPEG-TS format, delaying the subtitles by 1 second:
@example
ffmpeg -i input.ts -filter_complex \
  '[#0x2ef] setpts=PTS+1/TB [sub] ; [#0x2d0] [sub] overlay' \
  -sn -map '#0x2dc' output.mkv
@end example
(0x2d0, 0x2dc and 0x2ef are the MPEG-TS PIDs of respectively the video,
audio and subtitles streams; 0:0, 0:3 and 0:7 would have worked too)

To generate 5 seconds of pure red video using lavfi @code{color} source:
@example
ffmpeg -filter_complex 'color=c=red' -t 5 out.mkv
@end example

@item -filter_complex_threads @var{nb_threads} (@emph{global})
Defines how many threads are used to process a filter_complex graph.
Similar to filter_threads but used for @code{-filter_complex} graphs only.
The default is the number of available CPUs.

@item -lavfi @var{filtergraph} (@emph{global})
Define a complex filtergraph, i.e. one with arbitrary number of inputs and/or
outputs. Equivalent to @option{-filter_complex}.

@item -accurate_seek (@emph{input})
This option enables or disables accurate seeking in input files with the
@option{-ss} option. It is enabled by default, so seeking is accurate when
transcoding. Use @option{-noaccurate_seek} to disable it, which may be useful
e.g. when copying some streams and transcoding the others.

@item -seek_timestamp (@emph{input})
This option enables or disables seeking by timestamp in input files with the
@option{-ss} option. It is disabled by default. If enabled, the argument
to the @option{-ss} option is considered an actual timestamp, and is not
offset by the start time of the file. This matters only for files which do
not start from timestamp 0, such as transport streams.

@item -thread_queue_size @var{size} (@emph{input/output})
For input, this option sets the maximum number of queued packets when reading
from the file or device. With low latency / high rate live streams, packets may
be discarded if they are not read in a timely manner; setting this value can
force ffmpeg to use a separate input thread and read packets as soon as they
arrive. By default ffmpeg only does this if multiple inputs are specified.

For output, this option specified the maximum number of packets that may be
queued to each muxing thread.

@item -sdp_file @var{file} (@emph{global})
Print sdp information for an output stream to @var{file}.
This allows dumping sdp information when at least one output isn't an
rtp stream. (Requires at least one of the output formats to be rtp).

@item -discard (@emph{input})
Allows discarding specific streams or frames from streams.
Any input stream can be fully discarded, using value @code{all} whereas
selective discarding of frames from a stream occurs at the demuxer
and is not supported by all demuxers.

@table @option
@item none
Discard no frame.

@item default
Default, which discards no frames.

@item noref
Discard all non-reference frames.

@item bidir
Discard all bidirectional frames.

@item nokey
Discard all frames excepts keyframes.

@item all
Discard all frames.
@end table

@item -abort_on @var{flags} (@emph{global})
Stop and abort on various conditions. The following flags are available:

@table @option
@item empty_output
No packets were passed to the muxer, the output is empty.
@item empty_output_stream
No packets were passed to the muxer in some of the output streams.
@end table

@item -max_error_rate (@emph{global})
Set fraction of decoding frame failures across all inputs which when crossed
ffmpeg will return exit code 69. Crossing this threshold does not terminate
processing. Range is a floating-point number between 0 to 1. Default is 2/3.

@item -xerror (@emph{global})
Stop and exit on error

@item -max_muxing_queue_size @var{packets} (@emph{output,per-stream})
When transcoding audio and/or video streams, ffmpeg will not begin writing into
the output until it has one packet for each such stream. While waiting for that
to happen, packets for other streams are buffered. This option sets the size of
this buffer, in packets, for the matching output stream.

The default value of this option should be high enough for most uses, so only
touch this option if you are sure that you need it.

@item -muxing_queue_data_threshold @var{bytes} (@emph{output,per-stream})
This is a minimum threshold until which the muxing queue size is not taken into
account. Defaults to 50 megabytes per stream, and is based on the overall size
of packets passed to the muxer.

@item -auto_conversion_filters (@emph{global})
Enable automatically inserting format conversion filters in all filter
graphs, including those defined by @option{-vf}, @option{-af},
@option{-filter_complex} and @option{-lavfi}. If filter format negotiation
requires a conversion, the initialization of the filters will fail.
Conversions can still be performed by inserting the relevant conversion
filter (scale, aresample) in the graph.
On by default, to explicitly disable it you need to specify
@code{-noauto_conversion_filters}.

@item -bits_per_raw_sample[:@var{stream_specifier}] @var{value} (@emph{output,per-stream})
Declare the number of bits per raw sample in the given output stream to be
@var{value}. Note that this option sets the information provided to the
encoder/muxer, it does not change the stream to conform to this value. Setting
values that do not match the stream properties may result in encoding failures
or invalid output files.

@anchor{stats_enc_options}
@item -stats_enc_pre[:@var{stream_specifier}] @var{path} (@emph{output,per-stream})
@item -stats_enc_post[:@var{stream_specifier}] @var{path} (@emph{output,per-stream})
@item -stats_mux_pre[:@var{stream_specifier}] @var{path} (@emph{output,per-stream})
Write per-frame encoding information about the matching streams into the file
given by @var{path}.

@option{-stats_enc_pre} writes information about raw video or audio frames right
before they are sent for encoding, while @option{-stats_enc_post} writes
information about encoded packets as they are received from the encoder.
@option{-stats_mux_pre} writes information about packets just as they are about to
be sent to the muxer. Every frame or packet produces one line in the specified
file. The format of this line is controlled by @option{-stats_enc_pre_fmt} /
@option{-stats_enc_post_fmt} / @option{-stats_mux_pre_fmt}.

When stats for multiple streams are written into a single file, the lines
corresponding to different streams will be interleaved. The precise order of
this interleaving is not specified and not guaranteed to remain stable between
different invocations of the program, even with the same options.

@item -stats_enc_pre_fmt[:@var{stream_specifier}] @var{format_spec} (@emph{output,per-stream})
@item -stats_enc_post_fmt[:@var{stream_specifier}] @var{format_spec} (@emph{output,per-stream})
@item -stats_mux_pre_fmt[:@var{stream_specifier}] @var{format_spec} (@emph{output,per-stream})
Specify the format for the lines written with @option{-stats_enc_pre} /
@option{-stats_enc_post} / @option{-stats_mux_pre}.

@var{format_spec} is a string that may contain directives of the form
@var{@{fmt@}}. @var{format_spec} is backslash-escaped --- use \@{, \@}, and \\
to write a literal @{, @}, or \, respectively, into the output.

The directives given with @var{fmt} may be one of the following:
@table @option
@item fidx
Index of the output file.

@item sidx
Index of the output stream in the file.

@item n
Frame number. Pre-encoding: number of frames sent to the encoder so far.
Post-encoding: number of packets received from the encoder so far.
Muxing: number of packets submitted to the muxer for this stream so far.

@item ni
Input frame number. Index of the input frame (i.e. output by a decoder) that
corresponds to this output frame or packet. -1 if unavailable.

@item tb
Timebase in which this frame/packet's timestamps are expressed, as a rational
number @var{num/den}. Note that encoder and muxer may use different timebases.

@item tbi
Timebase for @var{ptsi}, as a rational number @var{num/den}. Available when
@var{ptsi} is available, @var{0/1} otherwise.

@item pts
Presentation timestamp of the frame or packet, as an integer. Should be
multiplied by the timebase to compute presentation time.

@item ptsi
Presentation timestamp of the input frame (see @var{ni}), as an integer. Should
be multiplied by @var{tbi} to compute presentation time. Printed as
(2^63 - 1 = 9223372036854775807) when not available.

@item t
Presentation time of the frame or packet, as a decimal number. Equal to
@var{pts} multiplied by @var{tb}.

@item ti
Presentation time of the input frame (see @var{ni}), as a decimal number. Equal
to @var{ptsi} multiplied by @var{tbi}. Printed as inf when not available.

@item dts (@emph{packet})
Decoding timestamp of the packet, as an integer. Should be multiplied by the
timebase to compute presentation time.

@item dt (@emph{packet})
Decoding time of the frame or packet, as a decimal number. Equal to
@var{dts} multiplied by @var{tb}.

@item sn (@emph{frame,audio})
Number of audio samples sent to the encoder so far.

@item samp (@emph{frame,audio})
Number of audio samples in the frame.

@item size (@emph{packet})
Size of the encoded packet in bytes.

@item br (@emph{packet})
Current bitrate in bits per second.

@item abr (@emph{packet})
Average bitrate for the whole stream so far, in bits per second, -1 if it cannot
be determined at this point.

@item key (@emph{packet})
Character 'K' if the packet contains a keyframe, character 'N' otherwise.
@end table

Directives tagged with @emph{packet} may only be used with
@option{-stats_enc_post_fmt} and @option{-stats_mux_pre_fmt}.

Directives tagged with @emph{frame} may only be used with
@option{-stats_enc_pre_fmt}.

Directives tagged with @emph{audio} may only be used with audio streams.

The default format strings are:
@table @option
@item pre-encoding
@{fidx@} @{sidx@} @{n@} @{t@}
@item post-encoding
@{fidx@} @{sidx@} @{n@} @{t@}
@end table
In the future, new items may be added to the end of the default formatting
strings. Users who depend on the format staying exactly the same, should
prescribe it manually.

Note that stats for different streams written into the same file may have
different formats.

@end table

@section Preset files
A preset file contains a sequence of @var{option}=@var{value} pairs,
one for each line, specifying a sequence of options which would be
awkward to specify on the command line. Lines starting with the hash
('#') character are ignored and are used to provide comments. Check
the @file{presets} directory in the FFmpeg source tree for examples.

There are two types of preset files: ffpreset and avpreset files.

@subsection ffpreset files
ffpreset files are specified with the @code{vpre}, @code{apre},
@code{spre}, and @code{fpre} options. The @code{fpre} option takes the
filename of the preset instead of a preset name as input and can be
used for any kind of codec. For the @code{vpre}, @code{apre}, and
@code{spre} options, the options specified in a preset file are
applied to the currently selected codec of the same type as the preset
option.

The argument passed to the @code{vpre}, @code{apre}, and @code{spre}
preset options identifies the preset file to use according to the
following rules:

First ffmpeg searches for a file named @var{arg}.ffpreset in the
directories @file{$FFMPEG_DATADIR} (if set), and @file{$HOME/.ffmpeg}, and in
the datadir defined at configuration time (usually @file{PREFIX/share/ffmpeg})
or in a @file{ffpresets} folder along the executable on win32,
in that order. For example, if the argument is @code{libvpx-1080p}, it will
search for the file @file{libvpx-1080p.ffpreset}.

If no such file is found, then ffmpeg will search for a file named
@var{codec_name}-@var{arg}.ffpreset in the above-mentioned
directories, where @var{codec_name} is the name of the codec to which
the preset file options will be applied. For example, if you select
the video codec with @code{-vcodec libvpx} and use @code{-vpre 1080p},
then it will search for the file @file{libvpx-1080p.ffpreset}.

@subsection avpreset files
avpreset files are specified with the @code{pre} option. They work similar to
ffpreset files, but they only allow encoder- specific options. Therefore, an
@var{option}=@var{value} pair specifying an encoder cannot be used.

When the @code{pre} option is specified, ffmpeg will look for files with the
suffix .avpreset in the directories @file{$AVCONV_DATADIR} (if set), and
@file{$HOME/.avconv}, and in the datadir defined at configuration time (usually
@file{PREFIX/share/ffmpeg}), in that order.

First ffmpeg searches for a file named @var{codec_name}-@var{arg}.avpreset in
the above-mentioned directories, where @var{codec_name} is the name of the codec
to which the preset file options will be applied. For example, if you select the
video codec with @code{-vcodec libvpx} and use @code{-pre 1080p}, then it will
search for the file @file{libvpx-1080p.avpreset}.

If no such file is found, then ffmpeg will search for a file named
@var{arg}.avpreset in the same directories.

@anchor{vstats_file_format}
@section vstats file format
The @code{-vstats} and @code{-vstats_file} options enable generation of a file
containing statistics about the generated video outputs.

The @code{-vstats_version} option controls the format version of the generated
file.

With version @code{1} the format is:
@example
frame= @var{FRAME} q= @var{FRAME_QUALITY} PSNR= @var{PSNR} f_size= @var{FRAME_SIZE} s_size= @var{STREAM_SIZE}kB time= @var{TIMESTAMP} br= @var{BITRATE}kbits/s avg_br= @var{AVERAGE_BITRATE}kbits/s
@end example

With version @code{2} the format is:
@example
out= @var{OUT_FILE_INDEX} st= @var{OUT_FILE_STREAM_INDEX} frame= @var{FRAME_NUMBER} q= @var{FRAME_QUALITY}f PSNR= @var{PSNR} f_size= @var{FRAME_SIZE} s_size= @var{STREAM_SIZE}kB time= @var{TIMESTAMP} br= @var{BITRATE}kbits/s avg_br= @var{AVERAGE_BITRATE}kbits/s
@end example

The value corresponding to each key is described below:
@table @option
@item avg_br
average bitrate expressed in Kbits/s

@item br
bitrate expressed in Kbits/s

@item frame
number of encoded frame

@item out
out file index

@item PSNR
Peak Signal to Noise Ratio

@item q
quality of the frame

@item f_size
encoded packet size expressed as number of bytes

@item s_size
stream size expressed in KiB

@item st
out file stream index

@item time
time of the packet

@item type
picture type
@end table

See also the @ref{stats_enc_options,,-stats_enc options} for an alternative way
to show encoding statistics.

@c man end OPTIONS

@chapter Examples
@c man begin EXAMPLES

@section Video and Audio grabbing

If you specify the input format and device then ffmpeg can grab video
and audio directly.

@example
ffmpeg -f oss -i /dev/dsp -f video4linux2 -i /dev/video0 /tmp/out.mpg
@end example

Or with an ALSA audio source (mono input, card id 1) instead of OSS:
@example
ffmpeg -f alsa -ac 1 -i hw:1 -f video4linux2 -i /dev/video0 /tmp/out.mpg
@end example

Note that you must activate the right video source and channel before
launching ffmpeg with any TV viewer such as
@uref{http://linux.bytesex.org/xawtv/, xawtv} by Gerd Knorr. You also
have to set the audio recording levels correctly with a
standard mixer.

@section X11 grabbing

Grab the X11 display with ffmpeg via

@example
ffmpeg -f x11grab -video_size cif -framerate 25 -i :0.0 /tmp/out.mpg
@end example

0.0 is display.screen number of your X11 server, same as
the DISPLAY environment variable.

@example
ffmpeg -f x11grab -video_size cif -framerate 25 -i :0.0+10,20 /tmp/out.mpg
@end example

0.0 is display.screen number of your X11 server, same as the DISPLAY environment
variable. 10 is the x-offset and 20 the y-offset for the grabbing.

@section Video and Audio file format conversion

Any supported file format and protocol can serve as input to ffmpeg:

Examples:
@itemize
@item
You can use YUV files as input:

@example
ffmpeg -i /tmp/test%d.Y /tmp/out.mpg
@end example

It will use the files:
@example
/tmp/test0.Y, /tmp/test0.U, /tmp/test0.V,
/tmp/test1.Y, /tmp/test1.U, /tmp/test1.V, etc...
@end example

The Y files use twice the resolution of the U and V files. They are
raw files, without header. They can be generated by all decent video
decoders. You must specify the size of the image with the @option{-s} option
if ffmpeg cannot guess it.

@item
You can input from a raw YUV420P file:

@example
ffmpeg -i /tmp/test.yuv /tmp/out.avi
@end example

test.yuv is a file containing raw YUV planar data. Each frame is composed
of the Y plane followed by the U and V planes at half vertical and
horizontal resolution.

@item
You can output to a raw YUV420P file:

@example
ffmpeg -i mydivx.avi hugefile.yuv
@end example

@item
You can set several input files and output files:

@example
ffmpeg -i /tmp/a.wav -s 640x480 -i /tmp/a.yuv /tmp/a.mpg
@end example

Converts the audio file a.wav and the raw YUV video file a.yuv
to MPEG file a.mpg.

@item
You can also do audio and video conversions at the same time:

@example
ffmpeg -i /tmp/a.wav -ar 22050 /tmp/a.mp2
@end example

Converts a.wav to MPEG audio at 22050 Hz sample rate.

@item
You can encode to several formats at the same time and define a
mapping from input stream to output streams:

@example
ffmpeg -i /tmp/a.wav -map 0:a -b:a 64k /tmp/a.mp2 -map 0:a -b:a 128k /tmp/b.mp2
@end example

Converts a.wav to a.mp2 at 64 kbits and to b.mp2 at 128 kbits. '-map
file:index' specifies which input stream is used for each output
stream, in the order of the definition of output streams.

@item
You can transcode decrypted VOBs:

@example
ffmpeg -i snatch_1.vob -f avi -c:v mpeg4 -b:v 800k -g 300 -bf 2 -c:a libmp3lame -b:a 128k snatch.avi
@end example

This is a typical DVD ripping example; the input is a VOB file, the
output an AVI file with MPEG-4 video and MP3 audio. Note that in this
command we use B-frames so the MPEG-4 stream is DivX5 compatible, and
GOP size is 300 which means one intra frame every 10 seconds for 29.97fps
input video. Furthermore, the audio stream is MP3-encoded so you need
to enable LAME support by passing @code{--enable-libmp3lame} to configure.
The mapping is particularly useful for DVD transcoding
to get the desired audio language.

NOTE: To see the supported input formats, use @code{ffmpeg -demuxers}.

@item
You can extract images from a video, or create a video from many images:

For extracting images from a video:
@example
ffmpeg -i foo.avi -r 1 -s WxH -f image2 foo-%03d.jpeg
@end example

This will extract one video frame per second from the video and will
output them in files named @file{foo-001.jpeg}, @file{foo-002.jpeg},
etc. Images will be rescaled to fit the new WxH values.

If you want to extract just a limited number of frames, you can use the
above command in combination with the @code{-frames:v} or @code{-t} option,
or in combination with -ss to start extracting from a certain point in time.

For creating a video from many images:
@example
ffmpeg -f image2 -framerate 12 -i foo-%03d.jpeg -s WxH foo.avi
@end example

The syntax @code{foo-%03d.jpeg} specifies to use a decimal number
composed of three digits padded with zeroes to express the sequence
number. It is the same syntax supported by the C printf function, but
only formats accepting a normal integer are suitable.

When importing an image sequence, -i also supports expanding
shell-like wildcard patterns (globbing) internally, by selecting the
image2-specific @code{-pattern_type glob} option.

For example, for creating a video from filenames matching the glob pattern
@code{foo-*.jpeg}:
@example
ffmpeg -f image2 -pattern_type glob -framerate 12 -i 'foo-*.jpeg' -s WxH foo.avi
@end example

@item
You can put many streams of the same type in the output:

@example
ffmpeg -i test1.avi -i test2.avi -map 1:1 -map 1:0 -map 0:1 -map 0:0 -c copy -y test12.nut
@end example

The resulting output file @file{test12.nut} will contain the first four streams
from the input files in reverse order.

@item
To force CBR video output:
@example
ffmpeg -i myfile.avi -b 4000k -minrate 4000k -maxrate 4000k -bufsize 1835k out.m2v
@end example

@item
The four options lmin, lmax, mblmin and mblmax use 'lambda' units,
but you may use the QP2LAMBDA constant to easily convert from 'q' units:
@example
ffmpeg -i src.ext -lmax 21*QP2LAMBDA dst.ext
@end example

@end itemize
@c man end EXAMPLES

@include config.texi
@ifset config-all
@ifset config-avutil
@include utils.texi
@end ifset
@ifset config-avcodec
@include codecs.texi
@include bitstream_filters.texi
@end ifset
@ifset config-avformat
@include formats.texi
@include protocols.texi
@end ifset
@ifset config-avdevice
@include devices.texi
@end ifset
@ifset config-swresample
@include resampler.texi
@end ifset
@ifset config-swscale
@include scaler.texi
@end ifset
@ifset config-avfilter
@include filters.texi
@end ifset
@include general_contents.texi
@end ifset

@chapter See Also

@ifhtml
@ifset config-all
@url{ffmpeg.html,ffmpeg}
@end ifset
@ifset config-not-all
@url{ffmpeg-all.html,ffmpeg-all},
@end ifset
@url{ffplay.html,ffplay}, @url{ffprobe.html,ffprobe},
@url{ffmpeg-utils.html,ffmpeg-utils},
@url{ffmpeg-scaler.html,ffmpeg-scaler},
@url{ffmpeg-resampler.html,ffmpeg-resampler},
@url{ffmpeg-codecs.html,ffmpeg-codecs},
@url{ffmpeg-bitstream-filters.html,ffmpeg-bitstream-filters},
@url{ffmpeg-formats.html,ffmpeg-formats},
@url{ffmpeg-devices.html,ffmpeg-devices},
@url{ffmpeg-protocols.html,ffmpeg-protocols},
@url{ffmpeg-filters.html,ffmpeg-filters}
@end ifhtml

@ifnothtml
@ifset config-all
ffmpeg(1),
@end ifset
@ifset config-not-all
ffmpeg-all(1),
@end ifset
ffplay(1), ffprobe(1),
ffmpeg-utils(1), ffmpeg-scaler(1), ffmpeg-resampler(1),
ffmpeg-codecs(1), ffmpeg-bitstream-filters(1), ffmpeg-formats(1),
ffmpeg-devices(1), ffmpeg-protocols(1), ffmpeg-filters(1)
@end ifnothtml

@include authors.texi

@ignore

@setfilename ffmpeg
@settitle ffmpeg media converter

@end ignore

@bye
