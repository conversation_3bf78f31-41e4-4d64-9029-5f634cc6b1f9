\input texinfo @c -*- texinfo -*-
@documentencoding UTF-8

@settitle ffprobe Documentation
@titlepage
@center @titlefont{ffprobe Documentation}
@end titlepage

@top

@contents

@chapter Synopsis

ffprobe [@var{options}] @file{input_url}

@chapter Description
@c man begin DESCRIPTION

ffprobe gathers information from multimedia streams and prints it in
human- and machine-readable fashion.

For example it can be used to check the format of the container used
by a multimedia stream and the format and type of each media stream
contained in it.

If a url is specified in input, f<PERSON><PERSON><PERSON> will try to open and
probe the url content. If the url cannot be opened or recognized as
a multimedia file, a positive exit code is returned.

If no output is specified as output with @option{o} ffp<PERSON> will write
to stdout.

ffprobe may be employed both as a standalone application or in
combination with a textual filter, which may perform more
sophisticated processing, e.g. statistical processing or plotting.

Options are used to list some of the formats supported by ffprobe or
for specifying which information to display, and for setting how
ff<PERSON><PERSON> will show it.

ffprobe output is designed to be easily parsable by a textual filter,
and consists of one or more sections of a form defined by the selected
writer, which is specified by the @option{output_format} option.

Sections may contain other nested sections, and are identified by a
name (which may be shared by other sections), and an unique
name. See the output of @option{sections}.

Metadata tags stored in the container or in the streams are recognized
and printed in the corresponding "FORMAT", "STREAM", "STREAM_GROUP_STREAM"
or "PROGRAM_STREAM" section.

@c man end

@chapter Options
@c man begin OPTIONS

@include fftools-common-opts.texi

@section Main options

@table @option

@item -f @var{format}
Force format to use.

@item -unit
Show the unit of the displayed values.

@item -prefix
Use SI prefixes for the displayed values.
Unless the "-byte_binary_prefix" option is used all the prefixes
are decimal.

@item -byte_binary_prefix
Force the use of binary prefixes for byte values.

@item -sexagesimal
Use sexagesimal format HH:MM:SS.MICROSECONDS for time values.

@item -pretty
Prettify the format of the displayed values, it corresponds to the
options "-unit -prefix -byte_binary_prefix -sexagesimal".

@item -output_format, -of, -print_format @var{writer_name}[=@var{writer_options}]
Set the output printing format.

@var{writer_name} specifies the name of the writer, and
@var{writer_options} specifies the options to be passed to the writer.

For example for printing the output in JSON format, specify:
@example
-output_format json
@end example

For more details on the available output printing formats, see the
Writers section below.

@item -sections
Print sections structure and section information, and exit. The output
is not meant to be parsed by a machine.

@item -select_streams @var{stream_specifier}
Select only the streams specified by @var{stream_specifier}. This
option affects only the options related to streams
(e.g. @code{show_streams}, @code{show_packets}, etc.).

For example to show only audio streams, you can use the command:
@example
ffprobe -show_streams -select_streams a INPUT
@end example

To show only video packets belonging to the video stream with index 1:
@example
ffprobe -show_packets -select_streams v:1 INPUT
@end example

@item -show_data
Show payload data, as a hexadecimal and ASCII dump. Coupled with
@option{-show_packets}, it will dump the packets' data. Coupled with
@option{-show_streams}, it will dump the codec extradata.

The dump is printed as the "data" field. It may contain newlines.

@item -show_data_hash @var{algorithm}
Show a hash of payload data, for packets with @option{-show_packets} and for
codec extradata with @option{-show_streams}.

@item -show_error
Show information about the error found when trying to probe the input.

The error information is printed within a section with name "ERROR".

@item -show_format
Show information about the container format of the input multimedia
stream.

All the container format information is printed within a section with
name "FORMAT".

@item -show_format_entry @var{name}
Like @option{-show_format}, but only prints the specified entry of the
container format information, rather than all. This option may be given more
than once, then all specified entries will be shown.

This option is deprecated, use @code{show_entries} instead.

@item -show_entries @var{section_entries}
Set list of entries to show.

Entries are specified according to the following
syntax. @var{section_entries} contains a list of section entries
separated by @code{:}. Each section entry is composed by a section
name (or unique name), optionally followed by a list of entries local
to that section, separated by @code{,}.

If section name is specified but is followed by no @code{=}, all
entries are printed to output, together with all the contained
sections. Otherwise only the entries specified in the local section
entries list are printed. In particular, if @code{=} is specified but
the list of local entries is empty, then no entries will be shown for
that section.

Note that the order of specification of the local section entries is
not honored in the output, and the usual display order will be
retained.

The formal syntax is given by:
@example
@var{LOCAL_SECTION_ENTRIES} ::= @var{SECTION_ENTRY_NAME}[,@var{LOCAL_SECTION_ENTRIES}]
@var{SECTION_ENTRY}         ::= @var{SECTION_NAME}[=[@var{LOCAL_SECTION_ENTRIES}]]
@var{SECTION_ENTRIES}       ::= @var{SECTION_ENTRY}[:@var{SECTION_ENTRIES}]
@end example

For example, to show only the index and type of each stream, and the PTS
time, duration time, and stream index of the packets, you can specify
the argument:
@example
packet=pts_time,duration_time,stream_index : stream=index,codec_type
@end example

To show all the entries in the section "format", but only the codec
type in the section "stream", specify the argument:
@example
format : stream=codec_type
@end example

To show all the tags in the stream and format sections:
@example
stream_tags : format_tags
@end example

To show only the @code{title} tag (if available) in the stream
sections:
@example
stream_tags=title
@end example

@item -show_packets
Show information about each packet contained in the input multimedia
stream.

The information for each single packet is printed within a dedicated
section with name "PACKET".

@item -show_frames
Show information about each frame and subtitle contained in the input
multimedia stream.

The information for each single frame is printed within a dedicated
section with name "FRAME" or "SUBTITLE".

@item -show_log @var{loglevel}
Show logging information from the decoder about each frame according to
the value set in @var{loglevel}, (see @code{-loglevel}). This option requires @code{-show_frames}.

The information for each log message is printed within a dedicated
section with name "LOG".

@item -show_streams
Show information about each media stream contained in the input
multimedia stream.

Each media stream information is printed within a dedicated section
with name "STREAM".

@item -show_programs
Show information about programs and their streams contained in the input
multimedia stream.

Each media stream information is printed within a dedicated section
with name "PROGRAM_STREAM".

@item -show_stream_groups
Show information about stream groups and their streams contained in the
input multimedia stream.

Each media stream information is printed within a dedicated section
with name "STREAM_GROUP_STREAM".

@item -show_chapters
Show information about chapters stored in the format.

Each chapter is printed within a dedicated section with name "CHAPTER".

@item -count_frames
Count the number of frames per stream and report it in the
corresponding stream section.

@item -count_packets
Count the number of packets per stream and report it in the
corresponding stream section.

@item -read_intervals @var{read_intervals}

Read only the specified intervals. @var{read_intervals} must be a
sequence of interval specifications separated by ",".
@command{ffprobe} will seek to the interval starting point, and will
continue reading from that.

Each interval is specified by two optional parts, separated by "%".

The first part specifies the interval start position. It is
interpreted as an absolute position, or as a relative offset from the
current position if it is preceded by the "+" character. If this first
part is not specified, no seeking will be performed when reading this
interval.

The second part specifies the interval end position. It is interpreted
as an absolute position, or as a relative offset from the current
position if it is preceded by the "+" character. If the offset
specification starts with "#", it is interpreted as the number of
packets to read (not including the flushing packets) from the interval
start. If no second part is specified, the program will read until the
end of the input.

Note that seeking is not accurate, thus the actual interval start
point may be different from the specified position. Also, when an
interval duration is specified, the absolute end time will be computed
by adding the duration to the interval start point found by seeking
the file, rather than to the specified start value.

The formal syntax is given by:
@example
@var{INTERVAL}  ::= [@var{START}|+@var{START_OFFSET}][%[@var{END}|+@var{END_OFFSET}]]
@var{INTERVALS} ::= @var{INTERVAL}[,@var{INTERVALS}]
@end example

A few examples follow.
@itemize
@item
Seek to time 10, read packets until 20 seconds after the found seek
point, then seek to position @code{01:30} (1 minute and thirty
seconds) and read packets until position @code{01:45}.
@example
10%+20,01:30%01:45
@end example

@item
Read only 42 packets after seeking to position @code{01:23}:
@example
01:23%+#42
@end example

@item
Read only the first 20 seconds from the start:
@example
%+20
@end example

@item
Read from the start until position @code{02:30}:
@example
%02:30
@end example
@end itemize

@item -show_private_data, -private
Show private data, that is data depending on the format of the
particular shown element.
This option is enabled by default, but you may need to disable it
for specific uses, for example when creating XSD-compliant XML output.

@item -show_program_version
Show information related to program version.

Version information is printed within a section with name
"PROGRAM_VERSION".

@item -show_library_versions
Show information related to library versions.

Version information for each library is printed within a section with
name "LIBRARY_VERSION".

@item -show_versions
Show information related to program and library versions. This is the
equivalent of setting both @option{-show_program_version} and
@option{-show_library_versions} options.

@item -show_pixel_formats
Show information about all pixel formats supported by FFmpeg.

Pixel format information for each format is printed within a section
with name "PIXEL_FORMAT".

@item -show_optional_fields @var{value}
Some writers viz. JSON and XML, omit the printing of fields with invalid or non-applicable values,
while other writers always print them. This option enables one to control this behaviour.
Valid values are @code{always}/@code{1}, @code{never}/@code{0} and @code{auto}/@code{-1}.
Default is @var{auto}.

@item -bitexact
Force bitexact output, useful to produce output which is not dependent
on the specific build.

@item -i @var{input_url}
Read @var{input_url}.

@item -o @var{output_url}
Write output to @var{output_url}. If not specified, the output is sent
to stdout.

@end table
@c man end

@chapter Writers
@c man begin WRITERS

A writer defines the output format adopted by @command{ffprobe}, and will be
used for printing all the parts of the output.

A writer may accept one or more arguments, which specify the options
to adopt. The options are specified as a list of @var{key}=@var{value}
pairs, separated by ":".

All writers support the following options:

@table @option
@item string_validation, sv
Set string validation mode.

The following values are accepted.
@table @samp
@item fail
The writer will fail immediately in case an invalid string (UTF-8)
sequence or code point is found in the input. This is especially
useful to validate input metadata.

@item ignore
Any validation error will be ignored. This will result in possibly
broken output, especially with the json or xml writer.

@item replace
The writer will substitute invalid UTF-8 sequences or code points with
the string specified with the @option{string_validation_replacement}.
@end table

Default value is @samp{replace}.

@item string_validation_replacement, svr
Set replacement string to use in case @option{string_validation} is
set to @samp{replace}.

In case the option is not specified, the writer will assume the empty
string, that is it will remove the invalid sequences from the input
strings.
@end table

A description of the currently available writers follows.

@section default
Default format.

Print each section in the form:
@example
[SECTION]
key1=val1
...
keyN=valN
[/SECTION]
@end example

Metadata tags are printed as a line in the corresponding FORMAT, STREAM,
STREAM_GROUP_STREAM or PROGRAM_STREAM section, and are prefixed by the
string "TAG:".

A description of the accepted options follows.

@table @option

@item nokey, nk
If set to 1 specify not to print the key of each field. Default value
is 0.

@item noprint_wrappers, nw
If set to 1 specify not to print the section header and footer.
Default value is 0.
@end table

@section compact, csv
Compact and CSV format.

The @code{csv} writer is equivalent to @code{compact}, but supports
different defaults.

Each section is printed on a single line.
If no option is specified, the output has the form:
@example
section|key1=val1| ... |keyN=valN
@end example

Metadata tags are printed in the corresponding "format" or "stream"
section. A metadata tag key, if printed, is prefixed by the string
"tag:".

The description of the accepted options follows.

@table @option

@item item_sep, s
Specify the character to use for separating fields in the output line.
It must be a single printable character, it is "|" by default ("," for
the @code{csv} writer).

@item nokey, nk
If set to 1 specify not to print the key of each field. Its default
value is 0 (1 for the @code{csv} writer).

@item escape, e
Set the escape mode to use, default to "c" ("csv" for the @code{csv}
writer).

It can assume one of the following values:
@table @option
@item c
Perform C-like escaping. Strings containing a newline (@samp{\n}), carriage
return (@samp{\r}), a tab (@samp{\t}), a form feed (@samp{\f}), the escaping
character (@samp{\}) or the item separator character @var{SEP} are escaped
using C-like fashioned escaping, so that a newline is converted to the
sequence @samp{\n}, a carriage return to @samp{\r}, @samp{\} to @samp{\\} and
the separator @var{SEP} is converted to @samp{\@var{SEP}}.

@item csv
Perform CSV-like escaping, as described in RFC4180.  Strings
containing a newline (@samp{\n}), a carriage return (@samp{\r}), a double quote
(@samp{"}), or @var{SEP} are enclosed in double-quotes.

@item none
Perform no escaping.
@end table

@item print_section, p
Print the section name at the beginning of each line if the value is
@code{1}, disable it with value set to @code{0}. Default value is
@code{1}.

@end table

@section flat
Flat format.

A free-form output where each line contains an explicit key=value, such as
"streams.stream.3.tags.foo=bar". The output is shell escaped, so it can be
directly embedded in sh scripts as long as the separator character is an
alphanumeric character or an underscore (see @var{sep_char} option).

The description of the accepted options follows.

@table @option
@item sep_char, s
Separator character used to separate the chapter, the section name, IDs and
potential tags in the printed field key.

Default value is @samp{.}.

@item hierarchical, h
Specify if the section name specification should be hierarchical. If
set to 1, and if there is more than one section in the current
chapter, the section name will be prefixed by the name of the
chapter. A value of 0 will disable this behavior.

Default value is 1.
@end table

@section ini
INI format output.

Print output in an INI based format.

The following conventions are adopted:

@itemize
@item
all key and values are UTF-8
@item
@samp{.} is the subgroup separator
@item
newline, @samp{\t}, @samp{\f}, @samp{\b} and the following characters are
escaped
@item
@samp{\} is the escape character
@item
@samp{#} is the comment indicator
@item
@samp{=} is the key/value separator
@item
@samp{:} is not used but usually parsed as key/value separator
@end itemize

This writer accepts options as a list of @var{key}=@var{value} pairs,
separated by @samp{:}.

The description of the accepted options follows.

@table @option
@item hierarchical, h
Specify if the section name specification should be hierarchical. If
set to 1, and if there is more than one section in the current
chapter, the section name will be prefixed by the name of the
chapter. A value of 0 will disable this behavior.

Default value is 1.
@end table

@section json
JSON based format.

Each section is printed using JSON notation.

The description of the accepted options follows.

@table @option

@item compact, c
If set to 1 enable compact output, that is each section will be
printed on a single line. Default value is 0.
@end table

For more information about JSON, see @url{http://www.json.org/}.

@section xml
XML based format.

The XML output is described in the XML schema description file
@file{ffprobe.xsd} installed in the FFmpeg datadir.

An updated version of the schema can be retrieved at the url
@url{http://www.ffmpeg.org/schema/ffprobe.xsd}, which redirects to the
latest schema committed into the FFmpeg development source code tree.

Note that the output issued will be compliant to the
@file{ffprobe.xsd} schema only when no special global output options
(@option{unit}, @option{prefix}, @option{byte_binary_prefix},
@option{sexagesimal} etc.) are specified.

The description of the accepted options follows.

@table @option

@item fully_qualified, q
If set to 1 specify if the output should be fully qualified. Default
value is 0.
This is required for generating an XML file which can be validated
through an XSD file.

@item xsd_strict, x
If set to 1 perform more checks for ensuring that the output is XSD
compliant. Default value is 0.
This option automatically sets @option{fully_qualified} to 1.
@end table

For more information about the XML format, see
@url{https://www.w3.org/XML/}.
@c man end WRITERS

@chapter Timecode
@c man begin TIMECODE

@command{ffprobe} supports Timecode extraction:

@itemize

@item
MPEG1/2 timecode is extracted from the GOP, and is available in the video
stream details (@option{-show_streams}, see @var{timecode}).

@item
MOV timecode is extracted from tmcd track, so is available in the tmcd
stream metadata (@option{-show_streams}, see @var{TAG:timecode}).

@item
DV, GXF and AVI timecodes are available in format metadata
(@option{-show_format}, see @var{TAG:timecode}).

@end itemize
@c man end TIMECODE

@include config.texi
@ifset config-all
@set config-readonly
@ifset config-avutil
@include utils.texi
@end ifset
@ifset config-avcodec
@include codecs.texi
@include bitstream_filters.texi
@end ifset
@ifset config-avformat
@include formats.texi
@include protocols.texi
@end ifset
@ifset config-avdevice
@include devices.texi
@end ifset
@ifset config-swresample
@include resampler.texi
@end ifset
@ifset config-swscale
@include scaler.texi
@end ifset
@ifset config-avfilter
@include filters.texi
@end ifset
@include general_contents.texi
@end ifset

@chapter See Also

@ifhtml
@ifset config-all
@url{ffprobe.html,ffprobe},
@end ifset
@ifset config-not-all
@url{ffprobe-all.html,ffprobe-all},
@end ifset
@url{ffmpeg.html,ffmpeg}, @url{ffplay.html,ffplay},
@url{ffmpeg-utils.html,ffmpeg-utils},
@url{ffmpeg-scaler.html,ffmpeg-scaler},
@url{ffmpeg-resampler.html,ffmpeg-resampler},
@url{ffmpeg-codecs.html,ffmpeg-codecs},
@url{ffmpeg-bitstream-filters.html,ffmpeg-bitstream-filters},
@url{ffmpeg-formats.html,ffmpeg-formats},
@url{ffmpeg-devices.html,ffmpeg-devices},
@url{ffmpeg-protocols.html,ffmpeg-protocols},
@url{ffmpeg-filters.html,ffmpeg-filters}
@end ifhtml

@ifnothtml
@ifset config-all
ffprobe(1),
@end ifset
@ifset config-not-all
ffprobe-all(1),
@end ifset
ffmpeg(1), ffplay(1),
ffmpeg-utils(1), ffmpeg-scaler(1), ffmpeg-resampler(1),
ffmpeg-codecs(1), ffmpeg-bitstream-filters(1), ffmpeg-formats(1),
ffmpeg-devices(1), ffmpeg-protocols(1), ffmpeg-filters(1)
@end ifnothtml

@include authors.texi

@ignore

@setfilename ffprobe
@settitle ffprobe media prober

@end ignore

@bye
