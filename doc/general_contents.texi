@chapter External libraries

FFmpeg can be hooked up with a number of external libraries to add support
for more formats. None of them are used by default, their use has to be
explicitly requested by passing the appropriate flags to
@command{./configure}.

@section Alliance for Open Media (AOM)

FFmpeg can make use of the AOM library for AV1 decoding and encoding.

Go to @url{http://aomedia.org/} and follow the instructions for
installing the library. Then pass @code{--enable-libaom} to configure to
enable it.

@section AMD AMF/VCE

FFmpeg can use the AMD Advanced Media Framework library
for accelerated H.264 and HEVC(only windows) encoding on hardware with Video Coding Engine (VCE).

To enable support you must obtain the AMF framework header files(version 1.4.9+) from
@url{https://github.com/GPUOpen-LibrariesAndSDKs/AMF.git}.

Create an @code{AMF/} directory in the system include path.
Copy the contents of @code{AMF/amf/public/include/} into that directory.
Then configure FFmpeg with @code{--enable-amf}.

Initialization of amf encoder occurs in this order:
1) trying to initialize through dx11(only windows)
2) trying to initialize through dx9(only windows)
3) trying to initialize through vulkan

To use h.264(AMD VCE) encoder on linux amdgru-pro version 19.20+ and amf-amdgpu-pro
package(amdgru-pro contains, but does not install automatically) are required.

This driver can be installed using amdgpu-pro-install script in official amd driver archive.

@section AviSynth

FFmpeg can read AviSynth scripts as input. To enable support, pass
@code{--enable-avisynth} to configure after installing the headers
provided by @url{https://github.com/AviSynth/AviSynthPlus, AviSynth+}.
AviSynth+ can be configured to install only the headers by either
passing @code{-DHEADERS_ONLY:bool=on} to the normal CMake-based build
system, or by using the supplied @code{GNUmakefile}.

For Windows, supported AviSynth variants are
@url{http://avisynth.nl, AviSynth 2.6 RC1 or higher} for 32-bit builds and
@url{http://avisynth.nl/index.php/AviSynth+, AviSynth+ r1718 or higher} for 32-bit and 64-bit builds.

For Linux, macOS, and BSD, the only supported AviSynth variant is
@url{https://github.com/AviSynth/AviSynthPlus, AviSynth+}, starting with version 3.5.

@float NOTE
In 2016, AviSynth+ added support for building with GCC. However, due to
the eccentricities of Windows' calling conventions, 32-bit GCC builds
of AviSynth+ are not compatible with typical 32-bit builds of FFmpeg.

By default, FFmpeg assumes compatibility with 32-bit MSVC builds of
AviSynth+ since that is the most widely-used and entrenched build
configuration.  Users can override this and enable support for 32-bit
GCC builds of AviSynth+ by passing @code{-DAVSC_WIN32_GCC32} to
@code{--extra-cflags} when configuring FFmpeg.

64-bit builds of FFmpeg are not affected, and can use either MSVC or
GCC builds of AviSynth+ without any special flags.
@end float

@float NOTE
AviSynth(+) is loaded dynamically.  Distributors can build FFmpeg
with @code{--enable-avisynth}, and the binaries will work regardless
of the end user having AviSynth installed.  If/when an end user
would like to use AviSynth scripts, then they can install AviSynth(+)
and FFmpeg will be able to find and use it to open scripts.
@end float

@section Chromaprint

FFmpeg can make use of the Chromaprint library for generating audio fingerprints.
Pass @code{--enable-chromaprint} to configure to
enable it. See @url{https://acoustid.org/chromaprint}.

@section codec2

FFmpeg can make use of the codec2 library for codec2 decoding and encoding.
There is currently no native decoder, so libcodec2 must be used for decoding.

Go to @url{http://freedv.org/}, download "Codec 2 source archive".
Build and install using CMake. Debian users can install the libcodec2-dev package instead.
Once libcodec2 is installed you can pass @code{--enable-libcodec2} to configure to enable it.

The easiest way to use codec2 is with .c2 files, since they contain the mode information required for decoding.
To encode such a file, use a .c2 file extension and give the libcodec2 encoder the -mode option:
@code{ffmpeg -i input.wav -mode 700C output.c2}.
Playback is as simple as @code{ffplay output.c2}.
For a list of supported modes, run @code{ffmpeg -h encoder=libcodec2}.
Raw codec2 files are also supported.
To make sense of them the mode in use needs to be specified as a format option:
@code{ffmpeg -f codec2raw -mode 1300 -i input.raw output.wav}.

@section dav1d

FFmpeg can make use of the dav1d library for AV1 video decoding.

Go to @url{https://code.videolan.org/videolan/dav1d} and follow the instructions for
installing the library. Then pass @code{--enable-libdav1d} to configure to enable it.

@section davs2

FFmpeg can make use of the davs2 library for AVS2-P2/IEEE1857.4 video decoding.

Go to @url{https://github.com/pkuvcl/davs2} and follow the instructions for
installing the library. Then pass @code{--enable-libdavs2} to configure to
enable it.

@float NOTE
libdavs2 is under the GNU Public License Version 2 or later
(see @url{http://www.gnu.org/licenses/old-licenses/gpl-2.0.html} for
details), you must upgrade FFmpeg's license to GPL in order to use it.
@end float

@section uavs3d

FFmpeg can make use of the uavs3d library for AVS3-P2/IEEE1857.10 video decoding.

Go to @url{https://github.com/uavs3/uavs3d} and follow the instructions for
installing the library. Then pass @code{--enable-libuavs3d} to configure to
enable it.

@section Game Music Emu

FFmpeg can make use of the Game Music Emu library to read audio from supported video game
music file formats. Pass @code{--enable-libgme} to configure to
enable it. See @url{https://bitbucket.org/mpyne/game-music-emu/overview}.

@section Intel QuickSync Video

FFmpeg can use Intel QuickSync Video (QSV) for accelerated decoding and encoding
of multiple codecs. To use QSV, FFmpeg must be linked against the @code{libmfx}
dispatcher, which loads the actual decoding libraries.

The dispatcher is open source and can be downloaded from
@url{https://github.com/lu-zero/mfx_dispatch.git}. FFmpeg needs to be configured
with the @code{--enable-libmfx} option and @code{pkg-config} needs to be able to
locate the dispatcher's @code{.pc} files.

@section Kvazaar

FFmpeg can make use of the Kvazaar library for HEVC encoding.

Go to @url{https://github.com/ultravideo/kvazaar} and follow the
instructions for installing the library. Then pass
@code{--enable-libkvazaar} to configure to enable it.

@section LAME

FFmpeg can make use of the LAME library for MP3 encoding.

Go to @url{http://lame.sourceforge.net/} and follow the
instructions for installing the library.
Then pass @code{--enable-libmp3lame} to configure to enable it.

@section LCEVCdec

FFmpeg can make use of the liblcevc_dec library for LCEVC enhacement layer
decoding on supported bitstreams.

Go to @url{https://github.com/v-novaltd/LCEVCdec} and follow the instructions
for installing the library. Then pass @code{--enable-liblcevc-dec} to configure to
enable it.

@float NOTE
LCEVCdec is under the BSD-3-Clause-Clear License.
@end float

@section libilbc

iLBC is a narrowband speech codec that has been made freely available
by Google as part of the WebRTC project. libilbc is a packaging friendly
copy of the iLBC codec. FFmpeg can make use of the libilbc library for
iLBC decoding and encoding.

Go to @url{https://github.com/TimothyGu/libilbc} and follow the instructions for
installing the library. Then pass @code{--enable-libilbc} to configure to
enable it.

@section libjxl

JPEG XL is an image format intended to fully replace legacy JPEG for an extended
period of life. See @url{https://jpegxl.info/} for more information, and see
@url{https://github.com/libjxl/libjxl} for the library source. You can pass
@code{--enable-libjxl} to configure in order enable the libjxl wrapper.

@section libvpx

FFmpeg can make use of the libvpx library for VP8/VP9 decoding and encoding.

Go to @url{http://www.webmproject.org/} and follow the instructions for
installing the library. Then pass @code{--enable-libvpx} to configure to
enable it.

@section ModPlug

FFmpeg can make use of this library, originating in Modplug-XMMS, to read from MOD-like music files.
See @url{https://github.com/Konstanty/libmodplug}. Pass @code{--enable-libmodplug} to configure to
enable it.

@section OpenCORE, VisualOn, and Fraunhofer libraries

Spun off Google Android sources, OpenCore, VisualOn and Fraunhofer
libraries provide encoders for a number of audio codecs.

@float NOTE
OpenCORE and VisualOn libraries are under the Apache License 2.0
(see @url{http://www.apache.org/licenses/LICENSE-2.0} for details), which is
incompatible to the LGPL version 2.1 and GPL version 2. You have to
upgrade FFmpeg's license to LGPL version 3 (or if you have enabled
GPL components, GPL version 3) by passing @code{--enable-version3} to configure in
order to use it.

The license of the Fraunhofer AAC library is incompatible with the GPL.
Therefore, for GPL builds, you have to pass @code{--enable-nonfree} to
configure in order to use it. To the best of our knowledge, it is
compatible with the LGPL.
@end float

@subsection OpenCORE AMR

FFmpeg can make use of the OpenCORE libraries for AMR-NB
decoding/encoding and AMR-WB decoding.

Go to @url{http://sourceforge.net/projects/opencore-amr/} and follow the
instructions for installing the libraries.
Then pass @code{--enable-libopencore-amrnb} and/or
@code{--enable-libopencore-amrwb} to configure to enable them.

@subsection VisualOn AMR-WB encoder library

FFmpeg can make use of the VisualOn AMR-WBenc library for AMR-WB encoding.

Go to @url{http://sourceforge.net/projects/opencore-amr/} and follow the
instructions for installing the library.
Then pass @code{--enable-libvo-amrwbenc} to configure to enable it.

@subsection Fraunhofer AAC library

FFmpeg can make use of the Fraunhofer AAC library for AAC decoding & encoding.

Go to @url{http://sourceforge.net/projects/opencore-amr/} and follow the
instructions for installing the library.
Then pass @code{--enable-libfdk-aac} to configure to enable it.

@subsection LC3 library

FFmpeg can make use of the Google LC3 library for LC3 decoding & encoding.

Go to @url{https://github.com/google/liblc3/} and follow the instructions for
installing the library.
Then pass @code{--enable-liblc3} to configure to enable it.

@section OpenH264

FFmpeg can make use of the OpenH264 library for H.264 decoding and encoding.

Go to @url{http://www.openh264.org/} and follow the instructions for
installing the library. Then pass @code{--enable-libopenh264} to configure to
enable it.

For decoding, this library is much more limited than the built-in decoder
in libavcodec; currently, this library lacks support for decoding B-frames
and some other main/high profile features. (It currently only supports
constrained baseline profile and CABAC.) Using it is mostly useful for
testing and for taking advantage of Cisco's patent portfolio license
(@url{http://www.openh264.org/BINARY_LICENSE.txt}).

@section OpenJPEG

FFmpeg can use the OpenJPEG libraries for decoding/encoding J2K videos.  Go to
@url{http://www.openjpeg.org/} to get the libraries and follow the installation
instructions.  To enable using OpenJPEG in FFmpeg, pass @code{--enable-libopenjpeg} to
@file{./configure}.

@section rav1e

FFmpeg can make use of rav1e (Rust AV1 Encoder) via its C bindings to encode videos.
Go to @url{https://github.com/xiph/rav1e/} and follow the instructions to build
the C library. To enable using rav1e in FFmpeg, pass @code{--enable-librav1e}
to @file{./configure}.

@section SVT-AV1

FFmpeg can make use of the Scalable Video Technology for AV1 library for AV1 encoding.

Go to @url{https://gitlab.com/AOMediaCodec/SVT-AV1/} and follow the instructions
for installing the library. Then pass @code{--enable-libsvtav1} to configure to
enable it.

@section TwoLAME

FFmpeg can make use of the TwoLAME library for MP2 encoding.

Go to @url{http://www.twolame.org/} and follow the
instructions for installing the library.
Then pass @code{--enable-libtwolame} to configure to enable it.

@section VapourSynth

FFmpeg can read VapourSynth scripts as input. To enable support, pass
@code{--enable-vapoursynth} to configure. Vapoursynth is detected via
@code{pkg-config}. Versions 42 or greater supported.
See @url{http://www.vapoursynth.com/}.

Due to security concerns, Vapoursynth scripts will not
be autodetected so the input format has to be forced. For ff* CLI tools,
add @code{-f vapoursynth} before the input @code{-i yourscript.vpy}.

@section x264

FFmpeg can make use of the x264 library for H.264 encoding.

Go to @url{http://www.videolan.org/developers/x264.html} and follow the
instructions for installing the library. Then pass @code{--enable-libx264} to
configure to enable it.

@float NOTE
x264 is under the GNU Public License Version 2 or later
(see @url{http://www.gnu.org/licenses/old-licenses/gpl-2.0.html} for
details), you must upgrade FFmpeg's license to GPL in order to use it.
@end float

@section x265

FFmpeg can make use of the x265 library for HEVC encoding.

Go to @url{http://x265.org/developers.html} and follow the instructions
for installing the library. Then pass @code{--enable-libx265} to configure
to enable it.

@float NOTE
x265 is under the GNU Public License Version 2 or later
(see @url{http://www.gnu.org/licenses/old-licenses/gpl-2.0.html} for
details), you must upgrade FFmpeg's license to GPL in order to use it.
@end float

@section xavs

FFmpeg can make use of the xavs library for AVS encoding.

Go to @url{http://xavs.sf.net/} and follow the instructions for
installing the library. Then pass @code{--enable-libxavs} to configure to
enable it.

@section xavs2

FFmpeg can make use of the xavs2 library for AVS2-P2/IEEE1857.4 video encoding.

Go to @url{https://github.com/pkuvcl/xavs2} and follow the instructions for
installing the library. Then pass @code{--enable-libxavs2} to configure to
enable it.

@float NOTE
libxavs2 is under the GNU Public License Version 2 or later
(see @url{http://www.gnu.org/licenses/old-licenses/gpl-2.0.html} for
details), you must upgrade FFmpeg's license to GPL in order to use it.
@end float

@section eXtra-fast Essential Video Encoder (XEVE)

FFmpeg can make use of the XEVE library for EVC video encoding.

Go to @url{https://github.com/mpeg5/xeve} and follow the instructions for
installing the XEVE library. Then pass @code{--enable-libxeve} to configure to
enable it.

@section eXtra-fast Essential Video Decoder (XEVD)

FFmpeg can make use of the XEVD library for EVC video decoding.

Go to @url{https://github.com/mpeg5/xevd} and follow the instructions for
installing the XEVD library. Then pass @code{--enable-libxevd} to configure to
enable it.

@section ZVBI

ZVBI is a VBI decoding library which can be used by FFmpeg to decode DVB
teletext pages and DVB teletext subtitles.

Go to @url{http://sourceforge.net/projects/zapping/} and follow the instructions for
installing the library. Then pass @code{--enable-libzvbi} to configure to
enable it.

@chapter Supported File Formats, Codecs or Features

You can use the @code{-formats} and @code{-codecs} options to have an exhaustive list.

@section File Formats

FFmpeg supports the following file formats through the @code{libavformat}
library:

@multitable @columnfractions .4 .1 .1 .4
@item Name @tab Encoding @tab Decoding @tab Comments
@item 3dostr                    @tab   @tab X
@item 4xm                       @tab   @tab X
    @tab 4X Technologies format, used in some games.
@item 8088flex TMV              @tab   @tab X
@item AAX                       @tab   @tab X
    @tab Audible Enhanced Audio format, used in audiobooks.
@item AA                        @tab   @tab X
    @tab Audible Format 2, 3, and 4, used in audiobooks.
@item ACT Voice                 @tab   @tab X
    @tab contains G.729 audio
@item Adobe Filmstrip           @tab X @tab X
@item Audio IFF (AIFF)          @tab X @tab X
@item American Laser Games MM   @tab   @tab X
    @tab Multimedia format used in games like Mad Dog McCree.
@item 3GPP AMR                  @tab X @tab X
@item Amazing Studio Packed Animation File  @tab   @tab X
    @tab Multimedia format used in game Heart Of Darkness.
@item Apple HTTP Live Streaming @tab   @tab X
@item Artworx Data Format       @tab   @tab X
@item Interplay ACM             @tab   @tab X
    @tab Audio only format used in some Interplay games.
@item ADP                       @tab   @tab X
    @tab Audio format used on the Nintendo Gamecube.
@item AFC                       @tab   @tab X
    @tab Audio format used on the Nintendo Gamecube.
@item ADS/SS2                   @tab   @tab X
    @tab Audio format used on the PS2.
@item APNG                      @tab X @tab X
@item ASF                       @tab X @tab X
    @tab Advanced / Active Streaming Format.
@item AST                       @tab X @tab X
    @tab Audio format used on the Nintendo Wii.
@item AVI                       @tab X @tab X
@item AviSynth                  @tab   @tab X
@item AVR                       @tab   @tab X
    @tab Audio format used on Mac.
@item AVS                       @tab   @tab X
    @tab Multimedia format used by the Creature Shock game.
@item Beam Software SIFF        @tab   @tab X
    @tab Audio and video format used in some games by Beam Software.
@item Bethesda Softworks VID    @tab   @tab X
    @tab Used in some games from Bethesda Softworks.
@item Binary text               @tab   @tab X
@item Bink                      @tab   @tab X
    @tab Multimedia format used by many games.
@item Bink Audio                @tab   @tab X
    @tab Audio only multimedia format used by some games.
@item Bitmap Brothers JV        @tab   @tab X
    @tab Used in Z and Z95 games.
@item BRP                       @tab   @tab X
    @tab Argonaut Games format.
@item Brute Force & Ignorance   @tab   @tab X
    @tab Used in the game Flash Traffic: City of Angels.
@item BFSTM                     @tab   @tab X
    @tab Audio format used on the Nintendo WiiU (based on BRSTM).
@item BRSTM                     @tab   @tab X
    @tab Audio format used on the Nintendo Wii.
@item BW64                      @tab   @tab X
    @tab Broadcast Wave 64bit.
@item BWF                       @tab X @tab X
@item codec2 (raw)              @tab X @tab X
    @tab Must be given -mode format option to decode correctly.
@item codec2 (.c2 files)        @tab X @tab X
    @tab Contains header with version and mode info, simplifying playback.
@item CRI ADX                   @tab X @tab X
    @tab Audio-only format used in console video games.
@item CRI AIX                   @tab   @tab X
@item CRI HCA                   @tab   @tab X
    @tab Audio-only format used in console video games.
@item Discworld II BMV          @tab   @tab X
@item Interplay C93             @tab   @tab X
    @tab Used in the game Cyberia from Interplay.
@item Delphine Software International CIN @tab   @tab X
    @tab Multimedia format used by Delphine Software games.
@item Digital Speech Standard (DSS) @tab   @tab X
@item CD+G                      @tab   @tab X
    @tab Video format used by CD+G karaoke disks
@item Phantom Cine              @tab   @tab X
@item Commodore CDXL            @tab   @tab X
    @tab Amiga CD video format
@item Core Audio Format         @tab X @tab X
    @tab Apple Core Audio Format
@item CRC testing format        @tab X @tab
@item Creative Voice            @tab X @tab X
    @tab Created for the Sound Blaster Pro.
@item CRYO APC                  @tab   @tab X
    @tab Audio format used in some games by CRYO Interactive Entertainment.
@item D-Cinema audio            @tab X @tab X
@item Deluxe Paint Animation    @tab   @tab X
@item DCSTR                     @tab   @tab X
@item DFA                       @tab   @tab X
    @tab This format is used in Chronomaster game
@item DirectDraw Surface        @tab   @tab X
@item DSD Stream File (DSF)     @tab   @tab X
@item DV video                  @tab X @tab X
@item DXA                       @tab   @tab X
    @tab This format is used in the non-Windows version of the Feeble Files
         game and different game cutscenes repacked for use with ScummVM.
@item Electronic Arts cdata  @tab    @tab X
@item Electronic Arts Multimedia  @tab    @tab X
    @tab Used in various EA games; files have extensions like WVE and UV2.
@item Ensoniq Paris Audio File  @tab   @tab X
@item FFM (FFserver live feed)  @tab X @tab X
@item Flash (SWF)               @tab X @tab X
@item Flash 9 (AVM2)            @tab X @tab X
    @tab Only embedded audio is decoded.
@item FLI/FLC/FLX animation     @tab   @tab X
    @tab .fli/.flc files
@item Flash Video (FLV)         @tab X @tab X
    @tab Macromedia Flash video files
@item framecrc testing format   @tab X @tab
@item FunCom ISS                @tab   @tab X
    @tab Audio format used in various games from FunCom like The Longest Journey.
@item G.723.1                   @tab X @tab X
@item G.726                     @tab   @tab X @tab Both left- and right-justified.
@item G.729 BIT                 @tab X @tab X
@item G.729 raw                 @tab   @tab X
@item GENH                      @tab   @tab X
    @tab Audio format for various games.
@item GIF Animation             @tab X @tab X
@item GXF                       @tab X @tab X
    @tab General eXchange Format SMPTE 360M, used by Thomson Grass Valley
         playout servers.
@item HNM @tab   @tab X
    @tab Only version 4 supported, used in some games from Cryo Interactive
@item iCEDraw File              @tab   @tab X
@item ICO                       @tab X @tab X
    @tab Microsoft Windows ICO
@item id Quake II CIN video     @tab   @tab X
@item id RoQ                    @tab X @tab X
    @tab Used in Quake III, Jedi Knight 2 and other computer games.
@item IEC61937 encapsulation @tab X @tab X
@item IFF                       @tab   @tab X
    @tab Interchange File Format
@item IFV                       @tab   @tab X
    @tab A format used by some old CCTV DVRs.
@item iLBC                      @tab X @tab X
@item Interplay MVE             @tab   @tab X
    @tab Format used in various Interplay computer games.
@item Iterated Systems ClearVideo @tab     @tab  X
    @tab I-frames only
@item IV8                       @tab   @tab X
    @tab A format generated by IndigoVision 8000 video server.
@item IVF (On2)                 @tab X @tab X
    @tab A format used by libvpx
@item Internet Video Recording  @tab   @tab X
@item IRCAM                     @tab X @tab X
@item LAF                       @tab   @tab X
    @tab Limitless Audio Format
@item LATM                      @tab X @tab X
@item LMLM4                     @tab   @tab X
    @tab Used by Linux Media Labs MPEG-4 PCI boards
@item LOAS                      @tab   @tab X
    @tab contains LATM multiplexed AAC audio
@item LRC                       @tab X @tab X
@item LVF                       @tab   @tab X
@item LXF                       @tab   @tab X
    @tab VR native stream format, used by Leitch/Harris' video servers.
@item Magic Lantern Video (MLV) @tab   @tab X
@item Matroska                  @tab X @tab X
@item Matroska audio            @tab X @tab
@item FFmpeg metadata           @tab X @tab X
    @tab Metadata in text format.
@item MAXIS XA                  @tab   @tab X
    @tab Used in Sim City 3000; file extension .xa.
@item MCA                       @tab   @tab X
    @tab Used in some games from Capcom; file extension .mca.
@item MD Studio                 @tab   @tab X
@item Metal Gear Solid: The Twin Snakes @tab @tab X
@item Megalux Frame             @tab   @tab X
    @tab Used by Megalux Ultimate Paint
@item MobiClip MODS             @tab   @tab X
@item MobiClip MOFLEX           @tab   @tab X
@item Mobotix .mxg              @tab   @tab X
@item Monkey's Audio            @tab   @tab X
@item Motion Pixels MVI         @tab   @tab X
@item MOV/QuickTime/MP4         @tab X @tab X
    @tab 3GP, 3GP2, PSP, iPod variants supported
@item MP2                       @tab X @tab X
@item MP3                       @tab X @tab X
@item MPEG-1 System             @tab X @tab X
    @tab muxed audio and video, VCD format supported
@item MPEG-PS (program stream)  @tab X @tab X
    @tab also known as @code{VOB} file, SVCD and DVD format supported
@item MPEG-TS (transport stream) @tab X @tab X
    @tab also known as DVB Transport Stream
@item MPEG-4                    @tab X @tab X
    @tab MPEG-4 is a variant of QuickTime.
@item MSF                       @tab   @tab X
    @tab Audio format used on the PS3.
@item Mirillis FIC video        @tab   @tab X
    @tab No cursor rendering.
@item MIDI Sample Dump Standard @tab   @tab X
@item MIME multipart JPEG       @tab X @tab
@item MSN TCP webcam            @tab   @tab X
    @tab Used by MSN Messenger webcam streams.
@item MTV                       @tab   @tab X
@item Musepack                  @tab   @tab X
@item Musepack SV8              @tab   @tab X
@item Material eXchange Format (MXF) @tab X @tab X
    @tab SMPTE 377M, used by D-Cinema, broadcast industry.
@item Material eXchange Format (MXF), D-10 Mapping @tab X @tab X
    @tab SMPTE 386M, D-10/IMX Mapping.
@item NC camera feed            @tab   @tab X
    @tab NC (AVIP NC4600) camera streams
@item NIST SPeech HEader REsources @tab   @tab X
@item Computerized Speech Lab NSP @tab   @tab X
@item NTT TwinVQ (VQF)          @tab   @tab X
    @tab Nippon Telegraph and Telephone Corporation TwinVQ.
@item Nullsoft Streaming Video  @tab   @tab X
@item NuppelVideo               @tab   @tab X
@item NUT                       @tab X @tab X
    @tab NUT Open Container Format
@item Ogg                       @tab X @tab X
@item Playstation Portable PMP  @tab   @tab X
@item Portable Voice Format     @tab   @tab X
@item RK Audio (RKA)            @tab   @tab X
@item TechnoTrend PVA           @tab   @tab X
    @tab Used by TechnoTrend DVB PCI boards.
@item QCP                       @tab   @tab X
@item raw ADTS (AAC)            @tab X @tab X
@item raw AC-3                  @tab X @tab X
@item raw AMR-NB                @tab   @tab X
@item raw AMR-WB                @tab   @tab X
@item raw APAC                  @tab   @tab X
@item raw aptX                  @tab X @tab X
@item raw aptX HD               @tab X @tab X
@item raw Bonk                  @tab   @tab X
@item raw Chinese AVS video     @tab X @tab X
@item raw DFPWM                 @tab X @tab X
@item raw Dirac                 @tab X @tab X
@item raw DNxHD                 @tab X @tab X
@item raw DTS                   @tab X @tab X
@item raw DTS-HD                @tab   @tab X
@item raw E-AC-3                @tab X @tab X
@item raw EVC                   @tab X @tab X
@item raw FLAC                  @tab X @tab X
@item raw GSM                   @tab   @tab X
@item raw H.261                 @tab X @tab X
@item raw H.263                 @tab X @tab X
@item raw H.264                 @tab X @tab X
@item raw HEVC                  @tab X @tab X
@item raw Ingenient MJPEG       @tab   @tab X
@item raw MJPEG                 @tab X @tab X
@item raw MLP                   @tab   @tab X
@item raw MPEG                  @tab   @tab X
@item raw MPEG-1                @tab   @tab X
@item raw MPEG-2                @tab   @tab X
@item raw MPEG-4                @tab X @tab X
@item raw NULL                  @tab X @tab
@item raw video                 @tab X @tab X
@item raw id RoQ                @tab X @tab
@item raw OBU                   @tab X @tab X
@item raw OSQ                   @tab   @tab X
@item raw SBC                   @tab X @tab X
@item raw Shorten               @tab   @tab X
@item raw TAK                   @tab   @tab X
@item raw TrueHD                @tab X @tab X
@item raw VC-1                  @tab X @tab X
@item raw PCM A-law             @tab X @tab X
@item raw PCM mu-law            @tab X @tab X
@item raw PCM Archimedes VIDC   @tab X @tab X
@item raw PCM signed 8 bit      @tab X @tab X
@item raw PCM signed 16 bit big-endian  @tab X @tab X
@item raw PCM signed 16 bit little-endian  @tab X @tab X
@item raw PCM signed 24 bit big-endian  @tab X @tab X
@item raw PCM signed 24 bit little-endian  @tab X @tab X
@item raw PCM signed 32 bit big-endian  @tab X @tab X
@item raw PCM signed 32 bit little-endian  @tab X @tab X
@item raw PCM signed 64 bit big-endian  @tab X @tab X
@item raw PCM signed 64 bit little-endian  @tab X @tab X
@item raw PCM unsigned 8 bit    @tab X @tab X
@item raw PCM unsigned 16 bit big-endian  @tab X @tab X
@item raw PCM unsigned 16 bit little-endian  @tab X @tab X
@item raw PCM unsigned 24 bit big-endian  @tab X @tab X
@item raw PCM unsigned 24 bit little-endian  @tab X @tab X
@item raw PCM unsigned 32 bit big-endian  @tab X @tab X
@item raw PCM unsigned 32 bit little-endian  @tab X @tab X
@item raw PCM 16.8 floating point little-endian @tab   @tab X
@item raw PCM 24.0 floating point little-endian @tab   @tab X
@item raw PCM floating-point 32 bit big-endian  @tab X @tab X
@item raw PCM floating-point 32 bit little-endian  @tab X @tab X
@item raw PCM floating-point 64 bit big-endian  @tab X @tab X
@item raw PCM floating-point 64 bit little-endian  @tab X @tab X
@item RDT                       @tab   @tab X
@item REDCODE R3D               @tab   @tab X
    @tab File format used by RED Digital cameras, contains JPEG 2000 frames and PCM audio.
@item RealMedia                 @tab X @tab X
@item Redirector                @tab   @tab X
@item RedSpark                  @tab   @tab X
@item Renderware TeXture Dictionary @tab   @tab X
@item Resolume DXV              @tab X @tab X
    @tab Encoding is only supported for the DXT1 (Normal Quality, No Alpha) texture format.
@item RF64                      @tab   @tab X
@item RL2                       @tab   @tab X
    @tab Audio and video format used in some games by Entertainment Software Partners.
@item RPL/ARMovie               @tab   @tab X
@item Lego Mindstorms RSO       @tab X @tab X
@item RSD                       @tab   @tab X
@item RTMP                      @tab X @tab X
    @tab Output is performed by publishing stream to RTMP server
@item RTP                       @tab X @tab X
@item RTSP                      @tab X @tab X
@item Sample Dump eXchange      @tab   @tab X
@item SAP                       @tab X @tab X
@item SBG                       @tab   @tab X
@item SDNS                      @tab   @tab X
@item SDP                       @tab   @tab X
@item SER                       @tab   @tab X
@item Digital Pictures SGA      @tab   @tab X
@item Sega FILM/CPK             @tab X @tab X
    @tab Used in many Sega Saturn console games.
@item Silicon Graphics Movie    @tab   @tab X
@item Sierra SOL                @tab   @tab X
    @tab .sol files used in Sierra Online games.
@item Sierra VMD                @tab   @tab X
    @tab Used in Sierra CD-ROM games.
@item Smacker                   @tab   @tab X
    @tab Multimedia format used by many games.
@item SMJPEG                    @tab X @tab X
    @tab Used in certain Loki game ports.
@item SMPTE 337M encapsulation  @tab   @tab X
@item Smush                     @tab   @tab X
    @tab Multimedia format used in some LucasArts games.
@item Sony OpenMG (OMA)         @tab X @tab X
    @tab Audio format used in Sony Sonic Stage and Sony Vegas.
@item Sony PlayStation STR      @tab   @tab X
@item Sony Wave64 (W64)         @tab X @tab X
@item SoX native format         @tab X @tab X
@item SUN AU format             @tab X @tab X
@item SUP raw PGS subtitles     @tab X @tab X
@item SVAG                      @tab   @tab X
    @tab Audio format used in Konami PS2 games.
@item TDSC                      @tab   @tab X
@item Text files                @tab   @tab X
@item THP                       @tab   @tab X
    @tab Used on the Nintendo GameCube.
@item Tiertex Limited SEQ       @tab   @tab X
    @tab Tiertex .seq files used in the DOS CD-ROM version of the game Flashback.
@item True Audio                @tab X @tab X
@item VAG                       @tab   @tab X
    @tab Audio format used in many Sony PS2 games.
@item VC-1 test bitstream       @tab X @tab X
@item Vidvox Hap                @tab X @tab X
@item Vivo                      @tab   @tab X
@item VPK                       @tab   @tab X
    @tab Audio format used in Sony PS games.
@item Marble WADY               @tab   @tab X
@item WAV                       @tab X @tab X
@item Waveform Archiver         @tab   @tab X
@item WavPack                   @tab X @tab X
@item WebM                      @tab X @tab X
@item Windows Televison (WTV)   @tab X @tab X
@item Wing Commander III movie  @tab   @tab X
    @tab Multimedia format used in Origin's Wing Commander III computer game.
@item Westwood Studios audio    @tab X @tab X
    @tab Multimedia format used in Westwood Studios games.
@item Westwood Studios VQA      @tab   @tab X
    @tab Multimedia format used in Westwood Studios games.
@item Wideband Single-bit Data (WSD) @tab   @tab X
@item WVE                       @tab   @tab X
@item Konami XMD                @tab   @tab X
@item XMV                       @tab   @tab X
    @tab Microsoft video container used in Xbox games.
@item XVAG                      @tab   @tab X
    @tab Audio format used on the PS3.
@item xWMA                      @tab   @tab X
    @tab Microsoft audio container used by XAudio 2.
@item eXtended BINary text (XBIN) @tab @tab X
@item YUV4MPEG pipe             @tab X @tab X
@item Psygnosis YOP             @tab   @tab X
@end multitable

@code{X} means that the feature in that column (encoding / decoding) is supported.

@section Image Formats

FFmpeg can read and write images for each frame of a video sequence. The
following image formats are supported:

@multitable @columnfractions .4 .1 .1 .4
@item Name @tab Encoding @tab Decoding @tab Comments
@item .Y.U.V       @tab X @tab X
    @tab one raw file per component
@item Alias PIX    @tab X @tab X
    @tab Alias/Wavefront PIX image format
@item animated GIF @tab X @tab X
@item APNG         @tab X @tab X
    @tab Animated Portable Network Graphics
@item BMP          @tab X @tab X
    @tab Microsoft BMP image
@item BRender PIX  @tab   @tab X
    @tab Argonaut BRender 3D engine image format.
@item CRI          @tab   @tab X
    @tab Cintel RAW
@item DPX          @tab X @tab X
    @tab Digital Picture Exchange
@item EXR          @tab   @tab X
    @tab OpenEXR
@item FITS         @tab X @tab X
    @tab Flexible Image Transport System
@item HDR          @tab X @tab X
    @tab Radiance HDR RGBE Image format
@item IMG          @tab   @tab X
    @tab GEM Raster image
@item JPEG         @tab X @tab X
    @tab Progressive JPEG is not supported.
@item JPEG 2000    @tab X @tab X
@item JPEG-LS      @tab X @tab X
@item LJPEG        @tab X @tab
    @tab Lossless JPEG
@item Media 100    @tab   @tab X
@item MSP          @tab   @tab X
    @tab Microsoft Paint image
@item PAM          @tab X @tab X
    @tab PAM is a PNM extension with alpha support.
@item PBM          @tab X @tab X
    @tab Portable BitMap image
@item PCD          @tab   @tab X
    @tab PhotoCD
@item PCX          @tab X @tab X
    @tab PC Paintbrush
@item PFM          @tab X @tab X
    @tab Portable FloatMap image
@item PGM          @tab X @tab X
    @tab Portable GrayMap image
@item PGMYUV       @tab X @tab X
    @tab PGM with U and V components in YUV 4:2:0
@item PGX          @tab   @tab X
    @tab PGX file decoder
@item PHM          @tab X @tab X
    @tab Portable HalfFloatMap image
@item PIC          @tab @tab X
    @tab Pictor/PC Paint
@item PNG          @tab X @tab X
    @tab Portable Network Graphics image
@item PPM          @tab X @tab X
    @tab Portable PixelMap image
@item PSD          @tab   @tab X
    @tab Photoshop
@item PTX          @tab   @tab X
    @tab V.Flash PTX format
@item QOI          @tab X @tab X
    @tab Quite OK Image format
@item SGI          @tab X @tab X
    @tab SGI RGB image format
@item Sun Rasterfile  @tab X @tab X
    @tab Sun RAS image format
@item TIFF         @tab X @tab X
    @tab YUV, JPEG and some extension is not supported yet.
@item Truevision Targa  @tab X @tab X
    @tab Targa (.TGA) image format
@item VBN  @tab X @tab X
    @tab Vizrt Binary Image format
@item WBMP         @tab X @tab X
    @tab Wireless Application Protocol Bitmap image format
@item WebP         @tab E @tab X
    @tab WebP image format, encoding supported through external library libwebp
@item XBM  @tab X @tab X
    @tab X BitMap image format
@item XFace @tab X @tab X
    @tab X-Face image format
@item XPM  @tab   @tab X
    @tab X PixMap image format
@item XWD  @tab X @tab X
    @tab X Window Dump image format
@end multitable

@code{X} means that the feature in that column (encoding / decoding) is supported.

@code{E} means that support is provided through an external library.

@section Video Codecs

@multitable @columnfractions .4 .1 .1 .4
@item Name @tab Encoding @tab Decoding @tab Comments
@item 4X Movie               @tab     @tab  X
    @tab Used in certain computer games.
@item 8088flex TMV           @tab     @tab  X
@item A64 multicolor         @tab  X  @tab
    @tab Creates video suitable to be played on a commodore 64 (multicolor mode).
@item Amazing Studio PAF Video @tab     @tab  X
@item American Laser Games MM  @tab    @tab X
    @tab Used in games like Mad Dog McCree.
@item Amuse Graphics Movie   @tab     @tab  X
@item AMV Video              @tab  X  @tab  X
    @tab Used in Chinese MP3 players.
@item ANSI/ASCII art         @tab     @tab  X
@item Apple Intermediate Codec @tab     @tab  X
@item Apple MJPEG-B          @tab     @tab  X
@item Apple Pixlet           @tab     @tab  X
@item Apple ProRes           @tab  X  @tab  X
    @tab fourcc: apch,apcn,apcs,apco,ap4h,ap4x
@item Apple QuickDraw        @tab     @tab  X
    @tab fourcc: qdrw
@item Argonaut Video         @tab     @tab  X
    @tab Used in some Argonaut games.
@item Asus v1                @tab  X  @tab  X
    @tab fourcc: ASV1
@item Asus v2                @tab  X  @tab  X
    @tab fourcc: ASV2
@item ATI VCR1               @tab     @tab  X
    @tab fourcc: VCR1
@item ATI VCR2               @tab     @tab  X
    @tab fourcc: VCR2
@item Auravision Aura        @tab     @tab  X
@item Auravision Aura 2      @tab     @tab  X
@item Autodesk Animator Flic video  @tab     @tab  X
@item Autodesk RLE           @tab     @tab  X
    @tab fourcc: AASC
@item AV1                    @tab  E  @tab  E
    @tab Supported through external libraries libaom, libdav1d, librav1e and libsvtav1
@item Avid 1:1 10-bit RGB Packer  @tab  X  @tab  X
    @tab fourcc: AVrp
@item AVS (Audio Video Standard) video  @tab     @tab  X
    @tab Video encoding used by the Creature Shock game.
@item AVS2-P2/IEEE1857.4     @tab  E  @tab  E
    @tab Supported through external libraries libxavs2 and libdavs2
@item AVS3-P2/IEEE1857.10    @tab     @tab  E
    @tab Supported through external library libuavs3d
@item AYUV                   @tab  X  @tab  X
    @tab Microsoft uncompressed packed 4:4:4:4
@item Beam Software VB       @tab     @tab  X
@item Bethesda VID video     @tab     @tab  X
    @tab Used in some games from Bethesda Softworks.
@item Bink Video             @tab     @tab  X
@item BitJazz SheerVideo     @tab     @tab  X
@item Bitmap Brothers JV video  @tab   @tab X
@item y41p Brooktree uncompressed 4:1:1 12-bit     @tab  X  @tab  X
@item Brooktree ProSumer Video  @tab     @tab  X
    @tab fourcc: BT20
@item Brute Force & Ignorance   @tab   @tab X
    @tab Used in the game Flash Traffic: City of Angels.
@item C93 video              @tab     @tab  X
    @tab Codec used in Cyberia game.
@item CamStudio              @tab     @tab  X
    @tab fourcc: CSCD
@item CD+G                   @tab     @tab  X
    @tab Video codec for CD+G karaoke disks
@item CDXL                   @tab     @tab  X
    @tab Amiga CD video codec
@item Chinese AVS video      @tab  E  @tab  X
    @tab AVS1-P2, JiZhun profile, encoding through external library libxavs
@item Delphine Software International CIN video  @tab     @tab  X
    @tab Codec used in Delphine Software International games.
@item Discworld II BMV Video @tab     @tab  X
@item CineForm HD            @tab  X  @tab  X
@item Canopus HQ             @tab     @tab  X
@item Canopus HQA            @tab     @tab  X
@item Canopus HQX            @tab     @tab  X
@item Canopus Lossless Codec @tab     @tab  X
@item CDToons                @tab     @tab  X
    @tab Codec used in various Broderbund games.
@item Cinepak                @tab     @tab  X
@item Cirrus Logic AccuPak   @tab  X  @tab  X
    @tab fourcc: CLJR
@item CPiA Video Format      @tab     @tab  X
@item Creative YUV (CYUV)    @tab     @tab  X
@item DFA                    @tab     @tab  X
    @tab Codec used in Chronomaster game.
@item Dirac                  @tab  E  @tab  X
    @tab supported though the native vc2 (Dirac Pro) encoder
@item Deluxe Paint Animation @tab     @tab  X
@item DNxHD                  @tab   X @tab  X
    @tab aka SMPTE VC3
@item Duck TrueMotion 1.0   @tab     @tab  X
    @tab fourcc: DUCK
@item Duck TrueMotion 2.0    @tab     @tab  X
    @tab fourcc: TM20
@item Duck TrueMotion 2.0 RT @tab     @tab  X
    @tab fourcc: TR20
@item DV (Digital Video)     @tab  X  @tab  X
@item Dxtory capture format  @tab     @tab  X
@item Feeble Files/ScummVM DXA  @tab     @tab  X
    @tab Codec originally used in Feeble Files game.
@item Electronic Arts CMV video  @tab     @tab  X
    @tab Used in NHL 95 game.
@item Electronic Arts Madcow video  @tab     @tab  X
@item Electronic Arts TGV video  @tab     @tab  X
@item Electronic Arts TGQ video  @tab     @tab  X
@item Electronic Arts TQI video  @tab     @tab  X
@item Escape 124             @tab     @tab  X
@item Escape 130             @tab     @tab  X
@item EVC / MPEG-5 Part 1    @tab  E  @tab  E
    @tab encoding and decoding supported through external libraries libxeve and libxevd
@item FFmpeg video codec #1  @tab  X  @tab  X
    @tab lossless codec (fourcc: FFV1)
@item Flash Screen Video v1  @tab  X  @tab  X
    @tab fourcc: FSV1
@item Flash Screen Video v2  @tab  X  @tab  X
@item Flash Video (FLV)      @tab  X  @tab  X
    @tab Sorenson H.263 used in Flash
@item FM Screen Capture Codec  @tab     @tab  X
@item Forward Uncompressed   @tab     @tab  X
@item Fraps                  @tab     @tab  X
@item Go2Meeting             @tab     @tab  X
    @tab fourcc: G2M2, G2M3
@item Go2Webinar             @tab     @tab  X
    @tab fourcc: G2M4
@item Gremlin Digital Video  @tab     @tab  X
@item H.261                  @tab  X  @tab  X
@item H.263 / H.263-1996     @tab  X  @tab  X
@item H.263+ / H.263-1998 / H.263 version 2  @tab  X  @tab  X
@item H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10  @tab  E  @tab  X
    @tab encoding supported through external library libx264 and OpenH264
@item HEVC                   @tab  X  @tab  X
    @tab encoding supported through external library libx265 and libkvazaar
@item HNM version 4          @tab     @tab  X
@item HuffYUV                @tab  X  @tab  X
@item HuffYUV FFmpeg variant @tab  X  @tab  X
@item IBM Ultimotion         @tab     @tab  X
    @tab fourcc: ULTI
@item id Cinematic video     @tab     @tab  X
    @tab Used in Quake II.
@item id RoQ video           @tab  X  @tab  X
    @tab Used in Quake III, Jedi Knight 2, other computer games.
@item IFF ILBM               @tab     @tab  X
    @tab IFF interleaved bitmap
@item IFF ByteRun1           @tab     @tab  X
    @tab IFF run length encoded bitmap
@item Infinity IMM4          @tab     @tab  X
@item Intel H.263            @tab     @tab  X
@item Intel Indeo 2          @tab     @tab  X
@item Intel Indeo 3          @tab     @tab  X
@item Intel Indeo 4          @tab     @tab  X
@item Intel Indeo 5          @tab     @tab  X
@item Interplay C93          @tab     @tab  X
    @tab Used in the game Cyberia from Interplay.
@item Interplay MVE video    @tab     @tab  X
    @tab Used in Interplay .MVE files.
@item J2K @tab  X  @tab  X
@item Karl Morton's video codec  @tab     @tab  X
    @tab Codec used in Worms games.
@item Kega Game Video (KGV1) @tab      @tab  X
    @tab Kega emulator screen capture codec.
@item Lagarith               @tab     @tab  X
@item LCEVC / MPEG-5 LCEVC / MPEG-5 Part 2 @tab     @tab  E
    @tab decoding supported through external library liblcevc-dec
@item LCL (LossLess Codec Library) MSZH  @tab     @tab  X
@item LCL (LossLess Codec Library) ZLIB  @tab  E  @tab  E
@item LEAD MCMP              @tab     @tab  X
@item LOCO                   @tab     @tab  X
@item LucasArts SANM/Smush   @tab     @tab  X
    @tab Used in LucasArts games / SMUSH animations.
@item lossless MJPEG         @tab  X  @tab  X
@item MagicYUV Video         @tab  X  @tab  X
@item Mandsoft Screen Capture Codec  @tab     @tab  X
@item Microsoft ATC Screen   @tab     @tab  X
    @tab Also known as Microsoft Screen 3.
@item Microsoft Expression Encoder Screen  @tab     @tab  X
    @tab Also known as Microsoft Titanium Screen 2.
@item Microsoft RLE          @tab  X  @tab  X
@item Microsoft Screen 1     @tab     @tab  X
    @tab Also known as Windows Media Video V7 Screen.
@item Microsoft Screen 2     @tab     @tab  X
    @tab Also known as Windows Media Video V9 Screen.
@item Microsoft Video 1      @tab     @tab  X
@item Mimic                  @tab     @tab  X
    @tab Used in MSN Messenger Webcam streams.
@item Miro VideoXL           @tab     @tab  X
    @tab fourcc: VIXL
@item MJPEG (Motion JPEG)    @tab  X  @tab  X
@item Mobotix MxPEG video    @tab     @tab  X
@item Motion Pixels video    @tab     @tab  X
@item MPEG-1 video           @tab  X  @tab  X
@item MPEG-2 video           @tab  X  @tab  X
@item MPEG-4 part 2          @tab  X  @tab  X
    @tab libxvidcore can be used alternatively for encoding.
@item MPEG-4 part 2 Microsoft variant version 1  @tab     @tab  X
@item MPEG-4 part 2 Microsoft variant version 2  @tab  X  @tab  X
@item MPEG-4 part 2 Microsoft variant version 3  @tab  X  @tab  X
@item Newtek SpeedHQ               @tab  X  @tab  X
@item Nintendo Gamecube THP video  @tab     @tab  X
@item NotchLC                @tab     @tab  X
@item NuppelVideo/RTjpeg     @tab     @tab  X
    @tab Video encoding used in NuppelVideo files.
@item On2 VP3                @tab     @tab  X
    @tab still experimental
@item On2 VP4                @tab     @tab  X
    @tab fourcc: VP40
@item On2 VP5                @tab     @tab  X
    @tab fourcc: VP50
@item On2 VP6                @tab     @tab  X
    @tab fourcc: VP60,VP61,VP62
@item On2 VP7                @tab     @tab  X
    @tab fourcc: VP70,VP71
@item VP8                    @tab  E  @tab  X
    @tab fourcc: VP80, encoding supported through external library libvpx
@item VP9                    @tab  E  @tab  X
    @tab encoding supported through external library libvpx
@item Pinnacle TARGA CineWave YUV16 @tab     @tab  X
    @tab fourcc: Y216
@item Q-team QPEG            @tab     @tab  X
    @tab fourccs: QPEG, Q1.0, Q1.1
@item QuickTime 8BPS video   @tab     @tab  X
@item QuickTime Animation (RLE) video  @tab  X  @tab  X
    @tab fourcc: 'rle '
@item QuickTime Graphics (SMC)  @tab  X  @tab  X
    @tab fourcc: 'smc '
@item QuickTime video (RPZA) @tab  X  @tab  X
    @tab fourcc: rpza
@item R10K AJA Kona 10-bit RGB Codec     @tab  X  @tab  X
@item R210 Quicktime Uncompressed RGB 10-bit     @tab  X  @tab  X
@item Raw Video              @tab  X  @tab  X
@item RealVideo 1.0          @tab  X  @tab  X
@item RealVideo 2.0          @tab  X  @tab  X
@item RealVideo 3.0          @tab     @tab  X
    @tab still far from ideal
@item RealVideo 4.0          @tab     @tab  X
@item Renderware TXD (TeXture Dictionary)  @tab     @tab  X
    @tab Texture dictionaries used by the Renderware Engine.
@item RivaTuner Video        @tab     @tab  X
    @tab fourcc: 'RTV1'
@item RL2 video              @tab     @tab  X
    @tab used in some games by Entertainment Software Partners
@item ScreenPressor          @tab     @tab  X
@item Screenpresso           @tab     @tab  X
@item Screen Recorder Gold Codec  @tab     @tab  X
@item Sierra VMD video       @tab     @tab  X
    @tab Used in Sierra VMD files.
@item Silicon Graphics Motion Video Compressor 1 (MVC1)  @tab     @tab  X
@item Silicon Graphics Motion Video Compressor 2 (MVC2)  @tab     @tab  X
@item Silicon Graphics RLE 8-bit video  @tab     @tab  X
@item Smacker video          @tab     @tab  X
    @tab Video encoding used in Smacker.
@item SMPTE VC-1             @tab     @tab  X
@item Snow                   @tab  X  @tab  X
    @tab experimental wavelet codec (fourcc: SNOW)
@item Sony PlayStation MDEC (Motion DECoder)  @tab     @tab  X
@item Sorenson Vector Quantizer 1  @tab  X  @tab  X
    @tab fourcc: SVQ1
@item Sorenson Vector Quantizer 3  @tab     @tab  X
    @tab fourcc: SVQ3
@item Sunplus JPEG (SP5X)    @tab     @tab  X
    @tab fourcc: SP5X
@item TechSmith Screen Capture Codec  @tab     @tab  X
    @tab fourcc: TSCC
@item TechSmith Screen Capture Codec 2  @tab     @tab  X
    @tab fourcc: TSC2
@item Theora                 @tab  E  @tab  X
    @tab encoding supported through external library libtheora
@item Tiertex Limited SEQ video  @tab     @tab  X
    @tab Codec used in DOS CD-ROM FlashBack game.
@item Ut Video               @tab  X  @tab  X
@item v210 QuickTime uncompressed 4:2:2 10-bit     @tab  X  @tab  X
@item v308 QuickTime uncompressed 4:4:4            @tab  X  @tab  X
@item v408 QuickTime uncompressed 4:4:4:4          @tab  X  @tab  X
@item v410 QuickTime uncompressed 4:4:4 10-bit     @tab  X  @tab  X
@item VBLE Lossless Codec    @tab     @tab  X
@item vMix Video             @tab     @tab  X
    @tab fourcc: 'VMX1'
@item VMware Screen Codec / VMware Video  @tab     @tab  X
    @tab Codec used in videos captured by VMware.
@item Westwood Studios VQA (Vector Quantized Animation) video  @tab     @tab  X
@item Windows Media Image    @tab     @tab  X
@item Windows Media Video 7  @tab  X  @tab  X
@item Windows Media Video 8  @tab  X  @tab  X
@item Windows Media Video 9  @tab     @tab  X
    @tab not completely working
@item Wing Commander III / Xan  @tab     @tab  X
    @tab Used in Wing Commander III .MVE files.
@item Wing Commander IV / Xan  @tab     @tab  X
    @tab Used in Wing Commander IV.
@item Winnov WNV1            @tab     @tab  X
@item WMV7                   @tab  X  @tab  X
@item YAMAHA SMAF            @tab  X  @tab  X
@item Psygnosis YOP Video    @tab     @tab  X
@item yuv4                   @tab  X  @tab  X
    @tab libquicktime uncompressed packed 4:2:0
@item ZeroCodec Lossless Video @tab     @tab  X
@item ZLIB                   @tab  X  @tab  X
    @tab part of LCL, encoder experimental
@item Zip Motion Blocks Video  @tab   X @tab  X
    @tab Encoder works only in PAL8.
@end multitable

@code{X} means that the feature in that column (encoding / decoding) is supported.

@code{E} means that support is provided through an external library.

@section Audio Codecs

@multitable @columnfractions .4 .1 .1 .4
@item Name @tab Encoding @tab Decoding @tab Comments
@item 8SVX exponential       @tab     @tab  X
@item 8SVX fibonacci         @tab     @tab  X
@item AAC                    @tab EX  @tab  X
    @tab encoding supported through internal encoder and external library libfdk-aac
@item AAC+                   @tab  E  @tab  IX
    @tab encoding supported through external library libfdk-aac
@item AC-3                   @tab IX  @tab  IX
@item ACELP.KELVIN           @tab     @tab  X
@item ADPCM 4X Movie         @tab     @tab  X
@item ADPCM Yamaha AICA      @tab     @tab  X
@item ADPCM AmuseGraphics Movie @tab    @tab  X
@item ADPCM Argonaut Games   @tab X   @tab  X
@item ADPCM CDROM XA         @tab     @tab  X
@item ADPCM Creative Technology @tab     @tab  X
    @tab 16 -> 4, 8 -> 4, 8 -> 3, 8 -> 2
@item ADPCM Electronic Arts  @tab     @tab  X
    @tab Used in various EA titles.
@item ADPCM Electronic Arts Maxis CDROM XS  @tab     @tab  X
    @tab Used in Sim City 3000.
@item ADPCM Electronic Arts R1  @tab     @tab  X
@item ADPCM Electronic Arts R2  @tab     @tab  X
@item ADPCM Electronic Arts R3  @tab     @tab  X
@item ADPCM Electronic Arts XAS @tab     @tab  X
@item ADPCM G.722            @tab  X  @tab  X
@item ADPCM G.726            @tab  X  @tab  X
@item ADPCM IMA Acorn Replay @tab     @tab  X
@item ADPCM IMA AMV          @tab  X  @tab  X
    @tab Used in AMV files
@item ADPCM IMA Cunning Developments  @tab     @tab  X
@item ADPCM IMA Electronic Arts EACS  @tab     @tab  X
@item ADPCM IMA Electronic Arts SEAD  @tab     @tab  X
@item ADPCM IMA Funcom       @tab     @tab  X
@item ADPCM IMA High Voltage Software ALP      @tab  X  @tab  X
@item ADPCM IMA Mobiclip MOFLEX  @tab     @tab  X
@item ADPCM IMA QuickTime    @tab  X  @tab  X
@item ADPCM IMA Simon & Schuster Interactive   @tab  X  @tab  X
@item ADPCM IMA Ubisoft APM  @tab  X  @tab  X
@item ADPCM IMA Loki SDL MJPEG  @tab     @tab  X
@item ADPCM IMA WAV          @tab  X  @tab  X
@item ADPCM IMA Westwood     @tab     @tab  X
@item ADPCM ISS IMA          @tab     @tab  X
    @tab Used in FunCom games.
@item ADPCM IMA Dialogic     @tab     @tab  X
@item ADPCM IMA Duck DK3     @tab     @tab  X
    @tab Used in some Sega Saturn console games.
@item ADPCM IMA Duck DK4     @tab     @tab  X
    @tab Used in some Sega Saturn console games.
@item ADPCM IMA Radical      @tab     @tab  X
@item ADPCM Microsoft        @tab  X  @tab  X
@item ADPCM MS IMA           @tab  X  @tab  X
@item ADPCM Nintendo Gamecube AFC  @tab     @tab  X
@item ADPCM Nintendo Gamecube DTK  @tab     @tab  X
@item ADPCM Nintendo THP  @tab     @tab  X
@item ADPCM Playstation      @tab     @tab  X
@item ADPCM QT IMA           @tab  X  @tab  X
@item ADPCM SEGA CRI ADX     @tab  X  @tab  X
    @tab Used in Sega Dreamcast games.
@item ADPCM Shockwave Flash  @tab  X  @tab  X
@item ADPCM Sound Blaster Pro 2-bit  @tab     @tab  X
@item ADPCM Sound Blaster Pro 2.6-bit  @tab     @tab  X
@item ADPCM Sound Blaster Pro 4-bit  @tab     @tab  X
@item ADPCM VIMA             @tab     @tab  X
    @tab Used in LucasArts SMUSH animations.
@item ADPCM Konami XMD       @tab     @tab  X
@item ADPCM Westwood Studios IMA      @tab  X @tab  X
    @tab Used in Westwood Studios games like Command and Conquer.
@item ADPCM Yamaha           @tab  X  @tab  X
@item ADPCM Zork             @tab     @tab  X
@item AMR-NB                 @tab  E  @tab  X
    @tab encoding supported through external library libopencore-amrnb
@item AMR-WB                 @tab  E  @tab  X
    @tab encoding supported through external library libvo-amrwbenc
@item Amazing Studio PAF Audio @tab     @tab  X
@item Apple lossless audio   @tab  X  @tab  X
    @tab QuickTime fourcc 'alac'
@item aptX                   @tab  X  @tab  X
    @tab Used in Bluetooth A2DP
@item aptX HD                @tab  X  @tab  X
    @tab Used in Bluetooth A2DP
@item ATRAC1                 @tab     @tab  X
@item ATRAC3                 @tab     @tab  X
@item ATRAC3+                @tab     @tab  X
@item ATRAC9                 @tab     @tab  X
@item Bink Audio             @tab     @tab  X
    @tab Used in Bink and Smacker files in many games.
@item Bonk audio             @tab     @tab  X
@item CELT                   @tab     @tab  E
    @tab decoding supported through external library libcelt
@item codec2                 @tab  E  @tab  E
    @tab en/decoding supported through external library libcodec2
@item CRI HCA                @tab     @tab X
@item Delphine Software International CIN audio  @tab     @tab  X
    @tab Codec used in Delphine Software International games.
@item DFPWM                  @tab  X  @tab  X
@item Digital Speech Standard - Standard Play mode (DSS SP) @tab     @tab  X
@item Discworld II BMV Audio @tab     @tab  X
@item COOK                   @tab     @tab  X
    @tab All versions except 5.1 are supported.
@item DCA (DTS Coherent Acoustics)  @tab  X  @tab  X
    @tab supported extensions: XCh, XXCH, X96, XBR, XLL, LBR (partially)
@item Dolby E  @tab     @tab  X
@item DPCM Cuberoot-Delta-Exact @tab  @tab  X
    @tab Used in few games.
@item DPCM Gremlin           @tab     @tab  X
@item DPCM id RoQ            @tab  X  @tab  X
    @tab Used in Quake III, Jedi Knight 2 and other computer games.
@item DPCM Marble WADY       @tab     @tab  X
@item DPCM Interplay         @tab     @tab  X
    @tab Used in various Interplay computer games.
@item DPCM Squareroot-Delta-Exact  @tab  @tab  X
    @tab Used in various games.
@item DPCM Sierra Online     @tab     @tab  X
    @tab Used in Sierra Online game audio files.
@item DPCM Sol               @tab     @tab  X
@item DPCM Xan               @tab     @tab  X
    @tab Used in Origin's Wing Commander IV AVI files.
@item DPCM Xilam DERF        @tab     @tab  X
@item DSD (Direct Stream Digital), least significant bit first  @tab  @tab  X
@item DSD (Direct Stream Digital), most significant bit first   @tab  @tab  X
@item DSD (Direct Stream Digital), least significant bit first, planar  @tab  @tab  X
@item DSD (Direct Stream Digital), most significant bit first, planar   @tab  @tab  X
@item DSP Group TrueSpeech   @tab     @tab  X
@item DST (Direct Stream Transfer) @tab  @tab  X
@item DV audio               @tab     @tab  X
@item Enhanced AC-3          @tab  X  @tab  X
@item EVRC (Enhanced Variable Rate Codec) @tab     @tab  X
@item FLAC (Free Lossless Audio Codec)  @tab  X  @tab  IX
@item FTR Voice              @tab     @tab  X
@item G.723.1                @tab X   @tab  X
@item G.729                  @tab     @tab  X
@item GSM                    @tab  E  @tab  X
    @tab encoding supported through external library libgsm
@item GSM Microsoft variant  @tab  E  @tab  X
    @tab encoding supported through external library libgsm
@item IAC (Indeo Audio Coder)  @tab     @tab  X
@item iLBC (Internet Low Bitrate Codec) @tab  E  @tab  EX
    @tab encoding and decoding supported through external library libilbc
@item IMC (Intel Music Coder)  @tab     @tab  X
@item Interplay ACM            @tab     @tab  X
@item LC3                    @tab E  @tab  E
    @tab supported through external library liblc3
@item MACE (Macintosh Audio Compression/Expansion) 6:1  @tab     @tab  X
@item Marian's A-pac audio     @tab     @tab  X
@item MI-SC4 (Micronas SC-4 Audio)  @tab     @tab  X
@item MLP (Meridian Lossless Packing)  @tab  X  @tab  X
    @tab Used in DVD-Audio discs.
@item Monkey's Audio         @tab     @tab  X
@item MP1 (MPEG audio layer 1)  @tab     @tab IX
@item MP2 (MPEG audio layer 2)  @tab IX  @tab IX
    @tab encoding supported also through external library TwoLAME
@item MP3 (MPEG audio layer 3)  @tab  E  @tab IX
    @tab encoding supported through external library LAME, ADU MP3 and MP3onMP4 also supported
@item MPEG-4 Audio Lossless Coding (ALS)  @tab     @tab  X
@item MobiClip FastAudio     @tab     @tab  X
@item Musepack SV7           @tab     @tab  X
@item Musepack SV8           @tab     @tab  X
@item Nellymoser Asao        @tab  X  @tab  X
@item On2 AVC (Audio for Video Codec) @tab     @tab  X
@item Opus                   @tab  E  @tab  X
    @tab encoding supported through external library libopus
@item OSQ (Original Sound Quality)  @tab     @tab  X
@item PCM A-law              @tab  X  @tab  X
@item PCM mu-law             @tab  X  @tab  X
@item PCM Archimedes VIDC    @tab  X  @tab  X
@item PCM signed 8-bit planar  @tab  X  @tab  X
@item PCM signed 16-bit big-endian planar  @tab  X  @tab  X
@item PCM signed 16-bit little-endian planar  @tab  X  @tab  X
@item PCM signed 24-bit little-endian planar  @tab  X  @tab  X
@item PCM signed 32-bit little-endian planar  @tab  X  @tab  X
@item PCM 32-bit floating point big-endian  @tab  X  @tab  X
@item PCM 32-bit floating point little-endian  @tab  X  @tab  X
@item PCM 64-bit floating point big-endian  @tab  X  @tab  X
@item PCM 64-bit floating point little-endian  @tab  X  @tab  X
@item PCM D-Cinema audio signed 24-bit   @tab  X  @tab  X
@item PCM signed 8-bit       @tab  X  @tab  X
@item PCM signed 16-bit big-endian  @tab  X  @tab  X
@item PCM signed 16-bit little-endian  @tab  X  @tab  X
@item PCM signed 24-bit big-endian  @tab  X  @tab  X
@item PCM signed 24-bit little-endian  @tab  X  @tab  X
@item PCM signed 32-bit big-endian  @tab  X  @tab  X
@item PCM signed 32-bit little-endian  @tab  X  @tab  X
@item PCM signed 16/20/24-bit big-endian in MPEG-TS  @tab     @tab  X
@item PCM unsigned 8-bit     @tab  X  @tab  X
@item PCM unsigned 16-bit big-endian  @tab  X  @tab  X
@item PCM unsigned 16-bit little-endian  @tab  X  @tab  X
@item PCM unsigned 24-bit big-endian  @tab  X  @tab  X
@item PCM unsigned 24-bit little-endian  @tab  X  @tab  X
@item PCM unsigned 32-bit big-endian  @tab  X  @tab  X
@item PCM unsigned 32-bit little-endian  @tab  X  @tab  X
@item PCM SGA                @tab     @tab  X
@item QCELP / PureVoice      @tab     @tab  X
@item QDesign Music Codec 1  @tab     @tab  X
@item QDesign Music Codec 2  @tab     @tab  X
    @tab There are still some distortions.
@item RealAudio 1.0 (14.4K)  @tab  X  @tab  X
    @tab Real 14400 bit/s codec
@item RealAudio 2.0 (28.8K)  @tab     @tab  X
    @tab Real 28800 bit/s codec
@item RealAudio 3.0 (dnet)   @tab IX  @tab  X
    @tab Real low bitrate AC-3 codec
@item RealAudio Lossless     @tab     @tab  X
@item RealAudio SIPR / ACELP.NET @tab     @tab  X
@item RK Audio (RKA)         @tab     @tab  X
@item SBC (low-complexity subband codec) @tab  X  @tab  X
    @tab Used in Bluetooth A2DP
@item Shorten                @tab     @tab  X
@item Sierra VMD audio       @tab     @tab  X
    @tab Used in Sierra VMD files.
@item Smacker audio          @tab     @tab  X
@item SMPTE 302M AES3 audio  @tab  X  @tab  X
@item Sonic                  @tab  X  @tab  X
    @tab experimental codec
@item Sonic lossless         @tab  X  @tab  X
    @tab experimental codec
@item Speex                  @tab  E  @tab  EX
    @tab supported through external library libspeex
@item TAK (Tom's lossless Audio Kompressor)  @tab     @tab  X
@item True Audio (TTA)       @tab  X  @tab  X
@item TrueHD                 @tab  X  @tab  X
    @tab Used in HD-DVD and Blu-Ray discs.
@item TwinVQ (VQF flavor)    @tab     @tab  X
@item VIMA                   @tab     @tab  X
    @tab Used in LucasArts SMUSH animations.
@item ViewQuest VQC          @tab     @tab  X
@item Vorbis                 @tab  E  @tab  X
    @tab A native but very primitive encoder exists.
@item Voxware MetaSound      @tab     @tab  X
@item Waveform Archiver      @tab     @tab  X
@item WavPack                @tab  X  @tab  X
@item Westwood Audio (SND1)  @tab     @tab  X
@item Windows Media Audio 1  @tab  X  @tab  X
@item Windows Media Audio 2  @tab  X  @tab  X
@item Windows Media Audio Lossless @tab  @tab  X
@item Windows Media Audio Pro @tab    @tab  X
@item Windows Media Audio Voice @tab  @tab  X
@item Xbox Media Audio 1     @tab     @tab  X
@item Xbox Media Audio 2     @tab     @tab  X
@end multitable

@code{X} means that the feature in that column (encoding / decoding) is supported.

@code{E} means that support is provided through an external library.

@code{I} means that an integer-only version is available, too (ensures high
performance on systems without hardware floating point support).

@section Subtitle Formats

@multitable @columnfractions .4 .1 .1 .1 .1
@item Name @tab Muxing @tab Demuxing @tab Encoding @tab Decoding
@item 3GPP Timed Text  @tab   @tab   @tab X @tab X
@item AQTitle          @tab   @tab X @tab   @tab X
@item DVB              @tab X @tab X @tab X @tab X
@item DVB teletext     @tab   @tab X @tab   @tab E
@item DVD              @tab X @tab X @tab X @tab X
@item JACOsub          @tab X @tab X @tab   @tab X
@item MicroDVD         @tab X @tab X @tab   @tab X
@item MPL2             @tab   @tab X @tab   @tab X
@item MPsub (MPlayer)  @tab   @tab X @tab   @tab X
@item PGS              @tab   @tab   @tab   @tab X
@item PJS (Phoenix)    @tab   @tab X @tab   @tab X
@item RealText         @tab   @tab X @tab   @tab X
@item SAMI             @tab   @tab X @tab   @tab X
@item Spruce format (STL) @tab   @tab X @tab   @tab X
@item SSA/ASS          @tab X @tab X @tab X @tab X
@item SubRip (SRT)     @tab X @tab X @tab X @tab X
@item SubViewer v1     @tab   @tab X @tab   @tab X
@item SubViewer        @tab   @tab X @tab   @tab X
@item TED Talks captions @tab @tab X @tab   @tab X
@item TTML             @tab X @tab   @tab X @tab
@item VobSub (IDX+SUB) @tab   @tab X @tab   @tab X
@item VPlayer          @tab   @tab X @tab   @tab X
@item WebVTT           @tab X @tab X @tab X @tab X
@item XSUB             @tab   @tab   @tab X @tab X
@end multitable

@code{X} means that the feature is supported.

@code{E} means that support is provided through an external library.

@section Network Protocols

@multitable @columnfractions .4 .1
@item Name         @tab Support
@item AMQP         @tab E
@item file         @tab X
@item FTP          @tab X
@item Gopher       @tab X
@item Gophers      @tab X
@item HLS          @tab X
@item HTTP         @tab X
@item HTTPS        @tab X
@item Icecast      @tab X
@item MMSH         @tab X
@item MMST         @tab X
@item pipe         @tab X
@item Pro-MPEG FEC @tab X
@item RTMP         @tab X
@item RTMPE        @tab X
@item RTMPS        @tab X
@item RTMPT        @tab X
@item RTMPTE       @tab X
@item RTMPTS       @tab X
@item RTP          @tab X
@item SAMBA        @tab E
@item SCTP         @tab X
@item SFTP         @tab E
@item TCP          @tab X
@item TLS          @tab X
@item UDP          @tab X
@item ZMQ          @tab E
@end multitable

@code{X} means that the protocol is supported.

@code{E} means that support is provided through an external library.


@section Input/Output Devices

@multitable @columnfractions .4 .1 .1
@item Name              @tab Input  @tab Output
@item ALSA              @tab X      @tab X
@item BKTR              @tab X      @tab
@item caca              @tab        @tab X
@item DV1394            @tab X      @tab
@item Lavfi virtual device @tab X   @tab
@item Linux framebuffer @tab X      @tab X
@item JACK              @tab X      @tab
@item LIBCDIO           @tab X
@item LIBDC1394         @tab X      @tab
@item OpenAL            @tab X
@item OpenGL            @tab        @tab X
@item OSS               @tab X      @tab X
@item PulseAudio        @tab X      @tab X
@item SDL               @tab        @tab X
@item Video4Linux2      @tab X      @tab X
@item VfW capture       @tab X      @tab
@item X11 grabbing      @tab X      @tab
@item Win32 grabbing    @tab X      @tab
@end multitable

@code{X} means that input/output is supported.

@section Timecode

@multitable @columnfractions .4 .1 .1
@item Codec/format      @tab Read   @tab Write
@item AVI               @tab X      @tab X
@item DV                @tab X      @tab X
@item GXF               @tab X      @tab X
@item MOV               @tab X      @tab X
@item MPEG1/2           @tab X      @tab X
@item MXF               @tab X      @tab X
@end multitable
