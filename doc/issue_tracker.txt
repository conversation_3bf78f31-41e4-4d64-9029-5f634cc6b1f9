FFmpeg's bug/feature request tracker manual
=================================================

Overview:
---------

FFmpeg uses Trac for tracking issues, new issues and changes to
existing issues can be done through a web interface.

Issues can be different kinds of things we want to keep track of
but that do not belong into the source tree itself. This includes
bug reports, feature requests and license violations. We
might add more items to this list in the future, so feel free to
propose a new `type of issue' on the ffmpeg-devel mailing list if
you feel it is worth tracking.

It is possible to subscribe to individual issues by adding yourself to the
Cc list or to subscribe to the ffmpeg-trac mailing list which receives
a mail for every change to every issue.
(the above does all work already after light testing)

The subscription URL for the ffmpeg-trac list is:
https://lists.ffmpeg.org/mailman/listinfo/ffmpeg-trac
The URL of the webinterface of the tracker is:
https://trac.ffmpeg.org

Type:
-----
art
    Artwork such as photos, music, banners, and logos.

bug / defect
    An error, flaw, mistake, failure, or fault in FFmpeg or libav* that
    prevents it from behaving as intended.

feature request / enhancement
    Request of support for encoding or decoding of a new codec, container
    or variant.
    Request of support for more, less or plain different output or behavior
    where the current implementation cannot be considered wrong.

license violation
    Ticket to keep track of (L)GPL violations of ffmpeg by others.

sponsoring request
    Developer requests for hardware, software, specifications, money,
    refunds, etc.

task
    A task/reminder such as setting up a FATE client, adding filters to
    Trac, etc.

Priority:
---------
critical
    Bugs about data loss and security issues.
    No feature request can be critical.

important
    Bugs which make FFmpeg unusable for a significant number of users.
    Examples here might be completely broken MPEG-4 decoding or a build issue
    on Linux.
    While broken 4xm decoding or a broken OS/2 build would not be important,
    the separation to normal is somewhat fuzzy.
    For feature requests this priority would be used for things many people
    want.
    Regressions also should be marked as important, regressions are bugs that
    don't exist in a past revision or another branch.

normal
   Default setting. Use this if the bug does not match the other
   priorities or if you are unsure of what priority to choose.

minor
    Bugs about things like spelling errors, "mp2" instead of
    "mp3" being shown and such.
    Feature requests about things few people want or which do not make a big
    difference.

wish
    Something that is desirable to have but that there is no urgency at
    all to implement, e.g. something completely cosmetic like a website
    restyle or a personalized doxy template or the FFmpeg logo.
    This priority is not valid for bugs.


Status:
-------
new
    initial state

open
    intermediate states

closed
    final state


Analyzed flag:
--------------
Bugs which have been analyzed and where it is understood what causes them
and which exact chain of events triggers them. This analysis should be
available as a message in the bug report.
Note, do not change the status to analyzed without also providing a clear
and understandable analysis.
This state implicates that the bug either has been reproduced or that
reproduction is not needed as the bug is already understood.


Type/Status:
----------
*/new
    Initial state of new bugs and feature requests submitted by
    users.

*/open
    Issues which have been briefly looked at and which did not look outright
    invalid.
    This implicates that no real more detailed state applies yet. Conversely,
    the more detailed states below implicate that the issue has been briefly
    looked at.

*/closed/duplicate
    Bugs or feature requests which are duplicates.
    Note, if you mark something as duplicate, do not forget setting the
    superseder so bug reports are properly linked.

*/closed/invalid
    Bugs caused by user errors, random ineligible or otherwise nonsense stuff.

*/closed/needs_more_info
    Issues for which some information has been requested by the developers,
    but which has not been provided by anyone within reasonable time.


bug/closed/fixed
    Bugs which have to the best of our knowledge been fixed.

bug/closed/wontfix
    Bugs which we will not fix. Possible reasons include legality, high
    complexity for the sake of supporting obscure corner cases, speed loss
    for similarly esoteric purposes, et cetera.
    This also means that we would reject a patch.
    If we are just too lazy to fix a bug then the correct state is open
    and unassigned. Closed means that the case is closed which is not
    the case if we are just waiting for a patch.

bug/closed/works_for_me
    Bugs for which sufficient information was provided to reproduce but
    reproduction failed - that is the code seems to work correctly to the
    best of our knowledge.

feature_request/closed/fixed
    Feature requests which have been implemented.

feature_request/closed/wontfix
    Feature requests which will not be implemented. The reasons here could
    be legal, philosophical or others.

Note2, if you provide the requested info do not forget to remove the
needs_more_info resolution.

Component:
----------

avcodec
    issues in libavcodec/*

avdevice
    issues in libavdevice/*

avfilter
    issues in libavfilter/*

avformat
    issues in libavformat/*

avutil
    issues in libavutil/*

build system
    issues in or related to configure/Makefile

documentation
    issues in or related to doc/*

ffmpeg
    issues in or related to ffmpeg.c

ffplay
    issues in or related to ffplay.c

ffprobe
    issues in or related to ffprobe.c

postproc
    issues in libpostproc/*

swresample
    issues in libswresample/*

swscale
    issues in libswscale/*

trac
    issues related to our issue tracker

undetermined
    default component; choose this if unsure

website
    issues related to the website

wiki
    issues related to the wiki
