
IRT <PERSON><PERSON><PERSON> (Cola). Build Sep 28 2007 15:38:52.

MXF::SDK version 4.1.1.174

File Name
---------

 "Avid-00005.mxf"


KLV Stream Information
----------------------

0 | 0x0
 Key: 06.0e.2b.***********.01.0d.***********.02.04.00 -> Partition Pack -> Header Partition -> closed and complete
 Length: 120 | 0x78


140 | 0x8c
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 352 | 0x160
 Total Length: 372 | 0x174


512 | 0x200
 Key: 06.0e.2b.***********.01.0d.***********.05.01.00 -> Primer Pack
 Length: 1610 | 0x64a


2142 | 0x85e
 Key: 06.0e.2b.***********.01.0d.***********.01.2f.00 -> Structural Metadata -> Preface
 Length: 162 | 0xa2


2324 | 0x914
 Key: 06.0e.2b.***********.01.0d.***********.01.30.00 -> Structural Metadata -> Identification
 Length: 108 | 0x6c


2452 | 0x994
 Key: 06.0e.2b.***********.01.0d.***********.01.18.00 -> Structural Metadata -> Content Storage
 Length: 92 | 0x5c


2564 | 0xa04
 Key: 06.0e.2b.***********.01.0d.***********.01.23.00 -> Structural Metadata -> Essence Container Data
 Length: 72 | 0x48


2656 | 0xa60
 Key: 06.0e.2b.***********.01.0d.***********.01.36.00 -> Structural Metadata -> Material Package
 Length: 156 | 0x9c


2832 | 0xb10
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


2932 | 0xb74
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


3032 | 0xbd8
 Key: 06.0e.2b.***********.01.0d.***********.01.14.00 -> Structural Metadata -> Timecode Component
 Length: 75 | 0x4b


3127 | 0xc37
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


3227 | 0xc9b
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


3327 | 0xcff
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


3455 | 0xd7f
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


3555 | 0xde3
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


3655 | 0xe47
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


3783 | 0xec7
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


3883 | 0xf2b
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


3983 | 0xf8f
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


4111 | 0x100f
 Key: 06.0e.2b.***********.01.0d.***********.01.37.00 -> Structural Metadata -> Source Package
 Length: 176 | 0xb0


4307 | 0x10d3
 Key: 06.0e.2b.***********.01.0d.***********.01.44.00 -> Structural Metadata -> Multiple Descriptor
 Length: 112 | 0x70


4439 | 0x1157
 Key: 06.0e.2b.***********.01.0d.***********.01.28.00 -> Structural Metadata -> CDCI Essence Descriptor
 Length: 331 | 0x14b


4790 | 0x12b6
 Key: 06.0e.2b.***********.01.0d.***********.01.47.00 -> Structural Metadata -> AES3 Audio Essence Descriptor
 Length: 161 | 0xa1


4971 | 0x136b
 Key: 06.0e.2b.***********.01.0d.***********.01.47.00 -> Structural Metadata -> AES3 Audio Essence Descriptor
 Length: 161 | 0xa1


5152 | 0x1420
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


5252 | 0x1484
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


5352 | 0x14e8
 Key: 06.0e.2b.***********.01.0d.***********.01.14.00 -> Structural Metadata -> Timecode Component
 Length: 75 | 0x4b


5447 | 0x1547
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


5547 | 0x15ab
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


5647 | 0x160f
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


5775 | 0x168f
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


5875 | 0x16f3
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


5975 | 0x1757
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


6103 | 0x17d7
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


6203 | 0x183b
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


6303 | 0x189f
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


6431 | 0x191f
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 205 | 0xcd
 Total Length: 225 | 0xe1


6656 | 0x1a00
 Key: 06.0e.2b.***********.01.0d.***********.10.01.00 -> Index Table Segment
 Length: 133 | 0x85


6809 | 0x1a99
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 339 | 0x153
 Total Length: 359 | 0x167


7168 | 0x1c00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


7245 | 0x1c4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


7680 | 0x1e00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


151700 | 0x25094
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


152064 | 0x25200
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


155924 | 0x26114
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


156160 | 0x26200
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


160020 | 0x27114
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


160256 | 0x27200
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


160333 | 0x2724d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


160768 | 0x27400
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


304788 | 0x4a694
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


305152 | 0x4a800
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


309012 | 0x4b714
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


309248 | 0x4b800
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


313108 | 0x4c714
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


313344 | 0x4c800
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


313421 | 0x4c84d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


313856 | 0x4ca00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


457876 | 0x6fc94
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


458240 | 0x6fe00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


462100 | 0x70d14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


462336 | 0x70e00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


466196 | 0x71d14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


466432 | 0x71e00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


466509 | 0x71e4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


466944 | 0x72000
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


610964 | 0x95294
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


611328 | 0x95400
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


615188 | 0x96314
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


615424 | 0x96400
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


619284 | 0x97314
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


619520 | 0x97400
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


619597 | 0x9744d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


620032 | 0x97600
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


764052 | 0xba894
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


764416 | 0xbaa00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


768276 | 0xbb914
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


768512 | 0xbba00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


772372 | 0xbc914
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


772608 | 0xbca00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


772685 | 0xbca4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


773120 | 0xbcc00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


917140 | 0xdfe94
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


917504 | 0xe0000
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


921364 | 0xe0f14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


921600 | 0xe1000
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


925460 | 0xe1f14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


925696 | 0xe2000
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


925773 | 0xe204d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


926208 | 0xe2200
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


1070228 | 0x105494
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


1070592 | 0x105600
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


1074452 | 0x106514
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1074688 | 0x106600
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


1078548 | 0x107514
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1078784 | 0x107600
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


1078861 | 0x10764d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


1079296 | 0x107800
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


1223316 | 0x12aa94
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


1223680 | 0x12ac00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


1227540 | 0x12bb14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1227776 | 0x12bc00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


1231636 | 0x12cb14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1231872 | 0x12cc00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


1231949 | 0x12cc4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


1232384 | 0x12ce00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


1376404 | 0x150094
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


1376768 | 0x150200
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


1380628 | 0x151114
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1380864 | 0x151200
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


1384724 | 0x152114
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1384960 | 0x152200
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


1385037 | 0x15224d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


1385472 | 0x152400
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


1529492 | 0x175694
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


1529856 | 0x175800
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


1533716 | 0x176714
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1533952 | 0x176800
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


1537812 | 0x177714
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1538048 | 0x177800
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


1538125 | 0x17784d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


1538560 | 0x177a00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


1682580 | 0x19ac94
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


1682944 | 0x19ae00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


1686804 | 0x19bd14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1687040 | 0x19be00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


1690900 | 0x19cd14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1691136 | 0x19ce00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


1691213 | 0x19ce4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


1691648 | 0x19d000
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


1835668 | 0x1c0294
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


1836032 | 0x1c0400
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


1839892 | 0x1c1314
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1840128 | 0x1c1400
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


1843988 | 0x1c2314
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1844224 | 0x1c2400
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


1844301 | 0x1c244d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


1844736 | 0x1c2600
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


1988756 | 0x1e5894
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


1989120 | 0x1e5a00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


1992980 | 0x1e6914
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1993216 | 0x1e6a00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


1997076 | 0x1e7914
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


1997312 | 0x1e7a00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


1997389 | 0x1e7a4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


1997824 | 0x1e7c00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


2141844 | 0x20ae94
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


2142208 | 0x20b000
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


2146068 | 0x20bf14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2146304 | 0x20c000
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


2150164 | 0x20cf14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2150400 | 0x20d000
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


2150477 | 0x20d04d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


2150912 | 0x20d200
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


2294932 | 0x230494
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


2295296 | 0x230600
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


2299156 | 0x231514
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2299392 | 0x231600
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


2303252 | 0x232514
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2303488 | 0x232600
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


2303565 | 0x23264d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


2304000 | 0x232800
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


2448020 | 0x255a94
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


2448384 | 0x255c00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


2452244 | 0x256b14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2452480 | 0x256c00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


2456340 | 0x257b14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2456576 | 0x257c00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


2456653 | 0x257c4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


2457088 | 0x257e00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


2601108 | 0x27b094
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


2601472 | 0x27b200
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


2605332 | 0x27c114
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2605568 | 0x27c200
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


2609428 | 0x27d114
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2609664 | 0x27d200
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


2609741 | 0x27d24d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


2610176 | 0x27d400
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


2754196 | 0x2a0694
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


2754560 | 0x2a0800
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


2758420 | 0x2a1714
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2758656 | 0x2a1800
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


2762516 | 0x2a2714
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2762752 | 0x2a2800
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


2762829 | 0x2a284d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


2763264 | 0x2a2a00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


2907284 | 0x2c5c94
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


2907648 | 0x2c5e00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


2911508 | 0x2c6d14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2911744 | 0x2c6e00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


2915604 | 0x2c7d14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


2915840 | 0x2c7e00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


2915917 | 0x2c7e4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


2916352 | 0x2c8000
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


3060372 | 0x2eb294
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


3060736 | 0x2eb400
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


3064596 | 0x2ec314
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3064832 | 0x2ec400
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


3068692 | 0x2ed314
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3068928 | 0x2ed400
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


3069005 | 0x2ed44d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


3069440 | 0x2ed600
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


3213460 | 0x310894
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


3213824 | 0x310a00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


3217684 | 0x311914
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3217920 | 0x311a00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


3221780 | 0x312914
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3222016 | 0x312a00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


3222093 | 0x312a4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


3222528 | 0x312c00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


3366548 | 0x335e94
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


3366912 | 0x336000
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


3370772 | 0x336f14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3371008 | 0x337000
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


3374868 | 0x337f14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3375104 | 0x338000
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


3375181 | 0x33804d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


3375616 | 0x338200
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


3519636 | 0x35b494
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


3520000 | 0x35b600
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


3523860 | 0x35c514
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3524096 | 0x35c600
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


3527956 | 0x35d514
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3528192 | 0x35d600
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


3528269 | 0x35d64d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


3528704 | 0x35d800
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


3672724 | 0x380a94
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


3673088 | 0x380c00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


3676948 | 0x381b14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3677184 | 0x381c00
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


3681044 | 0x382b14
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3681280 | 0x382c00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


3681357 | 0x382c4d
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 415 | 0x19f
 Total Length: 435 | 0x1b3


3681792 | 0x382e00
 Key: 06.0e.2b.34.***********.0d.***********.01.01.00 -> MXF Generic Container Version 1 DV-DIF Frame Wrapped Compound Essence
 Length: 144000 | 0x23280
 TrackNumber = 402718976 | 0x18010100 (itemIdentifier=18h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


3825812 | 0x3a6094
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 344 | 0x158
 Total Length: 364 | 0x16c


3826176 | 0x3a6200
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.00 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230592 | 0x16020300 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=00h)


3830036 | 0x3a7114
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3830272 | 0x3a7200
 Key: 06.0e.2b.34.***********.0d.01.03.01.16.02.03.01 -> MXF Generic Container Version 1 SMPTE 382M AES Frame-wrapped Sound Essence
 Length: 3840 | 0xf00
 TrackNumber = 369230593 | 0x16020301 (itemIdentifier=16h, NrOfElements=02h, ElementType=03h, ElementNr=01h)


3834132 | 0x3a8114
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 216 | 0xd8
 Total Length: 236 | 0xec


3834368 | 0x3a8200
 Key: 06.0e.2b.***********.01.0d.***********.04.04.00 -> Partition Pack -> Footer Partition -> closed and complete
 Length: 104 | 0x68


3834492 | 0x3a827c
 Key: 06.0e.2b.34.01.01.***********.***********.00.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 368 | 0x170
 Total Length: 388 | 0x184



Partition, Header Metadata, Index Table Segment and RIP Information
-------------------------------------------------------------------

0 | 0x0
Partition Pack (Header Partition -> closed and complete)
   |--SimpleElement ........<Major Version> UInt16 1
   |--SimpleElement ........<Minor Version> UInt16 2
   |--SimpleElement ........<KAG Size> UInt32 512
   |--SimpleElement ........<ThisPartition> UInt64 0
   |--SimpleElement ........<PreviousPartition> UInt64 0
   |--SimpleElement ........<FooterPartition> UInt64 3834368
   |--SimpleElement ........<HeaderByteCount> UInt64 6144
   |--SimpleElement ........<IndexByteCount> UInt64 512
   |--SimpleElement ........<IndexSID> UInt32 1
   |--SimpleElement ........<BodyOffset> UInt64 0
   |--SimpleElement ........<BodySID> UInt32 2
   |--SimpleElement ........<Operational Pattern> UniversalLabel 06.0e.2b.***********.01.0d.***********.01.09.00->MXF OP1a SingleItem SinglePackage->MXF OP1a SingleItem SinglePackage MultiTrack Stream Internal->internal essence->stream file->multi-track
   |--BatchElement..........<EssenceContainers> Batch 
   |                             UniversalLabel 06.0e.2b.***********.01.0d.***********.02.02.01->MXF-GC IEC DV 625x50I 25Mbps->MXF-GC Frame-wrapped IEC-DV 625x50I 25Mbps
   |                             UniversalLabel 06.0e.2b.***********.01.0d.***********.06.03.00->MXF-GC AES-BWF Audio->MXF-GC Frame-wrapped AES3 audio data

2142 | 0x85e
Preface
   |-- SimpleElement ........<Instance UID> UUID fb.42.63.3b.36.b9.14.46.91.c0.4d.e0.3a.82.27.5a
   |-- SimpleElement ........<Last Modified Date> Timestamp 2006/06/22 16:37:36,000
   |-- SimpleElement ........<Version> VersionType major 1 minor 2
   |-- ArrayReferenceElement.<Identifications> Array 
   |                             UUID a7.41.d0.c7.24.4a.bc.4a.bd.36.33.23.d0.4f.89.54
   |-- ReferenceElement .....<Content Storage> UUID 4c.19.f6.31.39.1f.2c.48.8d.1b.0b.ad.9b.de.41.46
   |-- SimpleElement ........<Operational Pattern> UniversalLabel 06.0e.2b.***********.01.0d.***********.01.09.00->MXF OP1a SingleItem SinglePackage->MXF OP1a SingleItem SinglePackage MultiTrack Stream Internal->internal essence->stream file->multi-track
   |-- BatchElement .........<EssenceContainers> Batch 
   |                             UniversalLabel 06.0e.2b.***********.01.0d.***********.02.02.01->MXF-GC IEC DV 625x50I 25Mbps->MXF-GC Frame-wrapped IEC-DV 625x50I 25Mbps
   |                             UniversalLabel 06.0e.2b.***********.01.0d.***********.06.03.00->MXF-GC AES-BWF Audio->MXF-GC Frame-wrapped AES3 audio data
   |-- BatchElement .........<DM Schemes> Batch 

2452 | 0x994
Content Storage
   |-- SimpleElement ........<Instance UID> UUID 4c.19.f6.31.39.1f.2c.48.8d.1b.0b.ad.9b.de.41.46
   |-- BatchReferenceElement.<Packages> Batch 
   |                             UUID c3.ce.c6.bc.e7.aa.73.4c.9a.bd.b2.37.f3.fb.96.37
   |                             UUID f2.e2.fc.e9.87.22.f1.4f.94.7f.08.de.1e.f7.a8.da
   |-- BatchReferenceElement.<EssenceContainerData> Batch 
   |                             UUID 7d.31.f3.e3.42.84.5a.4d.ab.7a.2b.7e.bd.fb.2d.83

2324 | 0x914
Identification
   |-- SimpleElement ........<Instance UID> UUID a7.41.d0.c7.24.4a.bc.4a.bd.36.33.23.d0.4f.89.54
   |-- SimpleElement ........<This Generation UID> UUID c1.d7.a0.ee.89.d6.75.4d.bd.52.cd.f4.2b.53.de.9f
   |-- SimpleElement ........<Company Name> UTF16String "AVID"
   |-- SimpleElement ........<Product Name> UTF16String "TRMG"
   |-- SimpleElement ........<Version String> UTF16String "2.97"
   |-- SimpleElement ........<Product UID> UUID ***********.***********.***********.***********
   |-- SimpleElement ........<Modification Date> Timestamp 2006/06/22 16:37:36,000

2656 | 0xa60
Material Package
   |-- SimpleElement ........<Instance UID> UUID c3.ce.c6.bc.e7.aa.73.4c.9a.bd.b2.37.f3.fb.96.37
   |-- SimpleElement ........<Package UID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.08.dc.39.00.97.29.05.80.00.00.08.00.46.b1.e8.9c
   |-- SimpleElement ........<Package Creation Date> Timestamp 2006/06/22 16:37:36,000
   |-- SimpleElement ........<Package Modified Date> Timestamp 2006/06/22 16:37:36,000
   |-- ArrayReferenceElement.<Tracks> Array 
   |                             UUID 11.f9.42.f0.d3.81.b1.4b.84.e6.bf.***********.dc
   |                             UUID e8.51.b8.f4.e6.0e.98.41.a9.b1.ad.d6.ad.f9.38.db
   |                             UUID 48.fa.01.74.98.77.dc.43.8c.c5.fb.27.d7.77.ae.17
   |                             UUID c3.84.3a.6a.84.2a.98.46.ad.a9.f4.3a.db.32.f2.3e

4111 | 0x100f
Source Package
   |-- SimpleElement ........<Instance UID> UUID f2.e2.fc.e9.87.22.f1.4f.94.7f.08.de.1e.f7.a8.da
   |-- SimpleElement ........<Package UID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.f2.e2.fc.e9.87.22.f1.4f.94.7f.08.de.1e.f7.a8.da
   |-- SimpleElement ........<Package Creation Date> Timestamp 2006/06/22 16:37:36,000
   |-- SimpleElement ........<Package Modified Date> Timestamp 2006/06/22 16:37:36,000
   |-- ArrayReferenceElement.<Tracks> Array 
   |                             UUID 0e.64.cd.c0.***********.af.60.73.6f.72.72.c9.fa
   |                             UUID bf.52.27.65.e6.f4.83.4d.b7.c6.58.a6.39.0f.79.fd
   |                             UUID a1.1d.94.ee.b0.4e.53.43.a2.d1.a9.e6.f8.3a.73.1b
   |                             UUID f9.2b.70.a4.7f.45.23.4d.94.5e.ee.09.57.2b.f0.27
   |-- ReferenceElement .....<Descriptor> UUID 79.64.0c.b5.d3.a8.5f.49.b5.a6.fd.a5.8e.ee.9a.51

2564 | 0xa04
Essence Container Data
   |-- SimpleElement ........<Instance UID> UUID 7d.31.f3.e3.42.84.5a.4d.ab.7a.2b.7e.bd.fb.2d.83
   |-- SimpleElement ........<Linked Package UID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.f2.e2.fc.e9.87.22.f1.4f.94.7f.08.de.1e.f7.a8.da
   |-- SimpleElement ........<IndexSID> UInt32 1
   |-- SimpleElement ........<BodySID> UInt32 2

2832 | 0xb10
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID 11.f9.42.f0.d3.81.b1.4b.84.e6.bf.***********.dc
   |-- SimpleElement ........<Track ID> UInt32 1
   |-- SimpleElement ........<Track Number> UInt32 0
   |-- ReferenceElement .....<Sequence> UUID 00.4e.1f.68.48.7b.1c.44.80.3b.68.06.8b.06.b9.cc
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

3127 | 0xc37
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID e8.51.b8.f4.e6.0e.98.41.a9.b1.ad.d6.ad.f9.38.db
   |-- SimpleElement ........<Track ID> UInt32 2
   |-- SimpleElement ........<Track Number> UInt32 402718976
   |-- ReferenceElement .....<Sequence> UUID 95.50.26.8d.c0.dc.bf.47.b2.a6.05.d4.f2.de.3a.78
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

3455 | 0xd7f
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID 48.fa.01.74.98.77.dc.43.8c.c5.fb.27.d7.77.ae.17
   |-- SimpleElement ........<Track ID> UInt32 3
   |-- SimpleElement ........<Track Number> UInt32 369230592
   |-- ReferenceElement .....<Sequence> UUID 60.d3.35.cc.***********.a6.d1.dc.71.1c.2c.67.df
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

3783 | 0xec7
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID c3.84.3a.6a.84.2a.98.46.ad.a9.f4.3a.db.32.f2.3e
   |-- SimpleElement ........<Track ID> UInt32 4
   |-- SimpleElement ........<Track Number> UInt32 369230593
   |-- ReferenceElement .....<Sequence> UUID cf.88.4b.bf.db.82.1c.4c.88.6d.f6.9b.02.b5.f3.ab
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

2932 | 0xb74
Sequence
   |-- SimpleElement ........<Instance UID> UUID 00.4e.1f.68.48.7b.1c.44.80.3b.68.06.8b.06.b9.cc
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Metadata Track Kinds->SMPTE-12M Timecode Track Inactive User Bits
   |-- SimpleElement ........<Duration> Length 25
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID da.d4.7f.90.d1.1b.df.4c.b4.26.67.2f.65.f2.01.3b

3032 | 0xbd8
Timecode Component
   |-- SimpleElement ........<Instance UID> UUID da.d4.7f.90.d1.1b.df.4c.b4.26.67.2f.65.f2.01.3b
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Metadata Track Kinds->SMPTE-12M Timecode Track Inactive User Bits
   |-- SimpleElement ........<Duration> Length 25
   |-- SimpleElement ........<Start Timecode> Position 90000->01:00:00:00
   |-- SimpleElement ........<Rounded Timecode Base> UInt16 25
   |-- SimpleElement ........<Drop Frame> Boolean 0

3227 | 0xc9b
Sequence
   |-- SimpleElement ........<Instance UID> UUID 95.50.26.8d.c0.dc.bf.47.b2.a6.05.d4.f2.de.3a.78
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Picture Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID 46.7f.84.8b.b2.16.36.4d.9b.6d.ec.6d.ec.44.f5.1a

3327 | 0xcff
SourceClip
   |-- SimpleElement ........<Instance UID> UUID 46.7f.84.8b.b2.16.36.4d.9b.6d.ec.6d.ec.44.f5.1a
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Picture Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- SimpleElement ........<SourcePackageID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.f2.e2.fc.e9.87.22.f1.4f.94.7f.08.de.1e.f7.a8.da
   |-- SimpleElement ........<SourceTrackID> UInt32 2
   |-- SimpleElement ........<Start Position> Position 0

3555 | 0xde3
Sequence
   |-- SimpleElement ........<Instance UID> UUID 60.d3.35.cc.***********.a6.d1.dc.71.1c.2c.67.df
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID 53.10.f7.b0.c5.b5.f6.4f.82.d8.f1.f2.a2.cc.f7.a5

3655 | 0xe47
SourceClip
   |-- SimpleElement ........<Instance UID> UUID 53.10.f7.b0.c5.b5.f6.4f.82.d8.f1.f2.a2.cc.f7.a5
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- SimpleElement ........<SourcePackageID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.f2.e2.fc.e9.87.22.f1.4f.94.7f.08.de.1e.f7.a8.da
   |-- SimpleElement ........<SourceTrackID> UInt32 3
   |-- SimpleElement ........<Start Position> Position 0

3883 | 0xf2b
Sequence
   |-- SimpleElement ........<Instance UID> UUID cf.88.4b.bf.db.82.1c.4c.88.6d.f6.9b.02.b5.f3.ab
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID 2c.a4.4b.36.96.5f.19.44.a5.6e.6c.78.be.60.47.7a

3983 | 0xf8f
SourceClip
   |-- SimpleElement ........<Instance UID> UUID 2c.a4.4b.36.96.5f.19.44.a5.6e.6c.78.be.60.47.7a
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- SimpleElement ........<SourcePackageID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.f2.e2.fc.e9.87.22.f1.4f.94.7f.08.de.1e.f7.a8.da
   |-- SimpleElement ........<SourceTrackID> UInt32 4
   |-- SimpleElement ........<Start Position> Position 0

4307 | 0x10d3
Multiple Descriptor
   |-- SimpleElement ........<Instance UID> UUID 79.64.0c.b5.d3.a8.5f.49.b5.a6.fd.a5.8e.ee.9a.51
   |-- SimpleElement ........<SampleRate> Rational 25/1
   |-- SimpleElement ........<Essence Container> UniversalLabel 06.0e.2b.***********.01.0d.***********.02.02.01->MXF-GC IEC DV 625x50I 25Mbps->MXF-GC Frame-wrapped IEC-DV 625x50I 25Mbps
   |-- ArrayReferenceElement.<Sub Descriptor UIDs> Array 
   |                             UUID 73.ea.e5.c9.77.eb.a9.4f.a8.d7.f1.98.08.5e.f9.2b
   |                             UUID 29.c4.72.49.8f.7f.2f.48.bb.d9.6f.63.e3.cc.f1.fa
   |                             UUID ee.7d.1e.62.6c.a5.9f.4e.bf.df.fa.05.87.ec.c9.f8

5152 | 0x1420
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID 0e.64.cd.c0.***********.af.60.73.6f.72.72.c9.fa
   |-- SimpleElement ........<Track ID> UInt32 1
   |-- SimpleElement ........<Track Number> UInt32 0
   |-- ReferenceElement .....<Sequence> UUID 53.e3.62.0e.5c.44.b2.4c.a8.84.ef.62.0a.9b.1b.c1
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

5447 | 0x1547
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID bf.52.27.65.e6.f4.83.4d.b7.c6.58.a6.39.0f.79.fd
   |-- SimpleElement ........<Track ID> UInt32 2
   |-- SimpleElement ........<Track Number> UInt32 402718976
   |-- ReferenceElement .....<Sequence> UUID b5.d9.1c.9a.72.2b.cf.4a.b8.f4.88.b1.c4.5a.4e.6d
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

5775 | 0x168f
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID a1.1d.94.ee.b0.4e.53.43.a2.d1.a9.e6.f8.3a.73.1b
   |-- SimpleElement ........<Track ID> UInt32 3
   |-- SimpleElement ........<Track Number> UInt32 369230592
   |-- ReferenceElement .....<Sequence> UUID 7a.51.2d.af.0f.78.db.42.9b.30.4f.a4.a0.e9.50.c4
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

6103 | 0x17d7
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID f9.2b.70.a4.7f.45.23.4d.94.5e.ee.09.57.2b.f0.27
   |-- SimpleElement ........<Track ID> UInt32 4
   |-- SimpleElement ........<Track Number> UInt32 369230593
   |-- ReferenceElement .....<Sequence> UUID 7b.96.dd.78.5c.56.32.47.9f.f2.33.4d.6f.ca.6b.fa
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

4439 | 0x1157
CDCI Essence Descriptor
   |-- SimpleElement ........<Instance UID> UUID 73.ea.e5.c9.77.eb.a9.4f.a8.d7.f1.98.08.5e.f9.2b
   |-- SimpleElement ........<Linked Track ID> UInt32 2
   |-- SimpleElement ........<SampleRate> Rational 25/1
   |-- SimpleElement ........<Essence Container> UniversalLabel 06.0e.2b.***********.01.0d.***********.02.02.01->MXF-GC IEC DV 625x50I 25Mbps->MXF-GC Frame-wrapped IEC-DV 625x50I 25Mbps
   |-- SimpleElement ........<Signal Standard> SignalStandard 1
   |-- SimpleElement ........<Frame Layout> UInt8 1
   |-- SimpleElement ........<Stored Width> UInt32 720
   |-- SimpleElement ........<Stored Height> UInt32 288
   |-- SimpleElement ........<StoredF2Offset> Int32 0
   |-- SimpleElement ........<SampledWidth> UInt32 720
   |-- SimpleElement ........<Sampled Height> UInt32 288
   |-- SimpleElement ........<SampledXOffset> Int32 0
   |-- SimpleElement ........<SampledYOffset> Int32 0
   |-- SimpleElement ........<DisplayHeight> UInt32 288
   |-- SimpleElement ........<DisplayWidth> UInt32 720
   |-- SimpleElement ........<DisplayXOffset> Int32 0
   |-- SimpleElement ........<DisplayYOffset> Int32 0
   |-- SimpleElement ........<DisplayF2Offset> Int32 0
   |-- SimpleElement ........<Aspect Ratio> Rational 4/3
   |-- ArrayElement  ........<Video Line Map> Array 
   |                             Int32 23
   |                             Int32 335
   |-- SimpleElement ........<Capture Gamma> UniversalLabel 06.0e.2b.***********.01.04.01.01.01.***********->Transfer Characteristic->ITU-R BT470 Transfer Characteristic
   |-- SimpleElement ........<Image Alignment Offset> UInt32 0
   |-- SimpleElement ........<Image Start Offset> UInt32 0
   |-- SimpleElement ........<Image End Offset> UInt32 0
   |-- SimpleElement ........<FieldDominance> UInt8 1
   |-- SimpleElement ........<Picture Essence Coding> UniversalLabel 06.0e.2b.***********.01.0d.***********.02.02.01->MXF-GC IEC DV 625x50I 25Mbps->MXF-GC Frame-wrapped IEC-DV 625x50I 25Mbps
   |-- SimpleElement ........<Component Depth> UInt32 8
   |-- SimpleElement ........<Horizontal Subsampling> UInt32 2
   |-- SimpleElement ........<Vertical Subsampling> UInt32 1
   |-- SimpleElement ........<Color Siting> UInt8 4
   |-- SimpleElement ........<ReversedByteOrder> Boolean 0
   |-- SimpleElement ........<PaddingBits> Int16 0
   |-- SimpleElement ........<Black Ref Level> UInt32 16
   |-- SimpleElement ........<White Ref level> UInt32 235
   |-- SimpleElement ........<Color Range> UInt32 225

4790 | 0x12b6
AES3 Audio Essence Descriptor
   |-- SimpleElement ........<Instance UID> UUID 29.c4.72.49.8f.7f.2f.48.bb.d9.6f.63.e3.cc.f1.fa
   |-- SimpleElement ........<Linked Track ID> UInt32 3
   |-- SimpleElement ........<SampleRate> Rational 25/1
   |-- SimpleElement ........<Essence Container> UniversalLabel 06.0e.2b.***********.01.0d.***********.06.03.00->MXF-GC AES-BWF Audio->MXF-GC Frame-wrapped AES3 audio data
   |-- SimpleElement ........<Audio sampling rate> Rational 48000/1
   |-- SimpleElement ........<Locked/Unlocked> Boolean 1
   |-- SimpleElement ........<Audio Ref Level> Int8 0
   |-- SimpleElement ........<ChannelCount> UInt32 1
   |-- SimpleElement ........<Quantization bits> UInt32 16
   |-- SimpleElement ........<BlockAlign> UInt16 2
   |-- SimpleElement ........<AvgBps> UInt32 96000
   |-- ArrayElement  ........<ChannelStatus-Mode> Array 
   |                             UInt8 1
   |-- ArrayElement  ........<FixedChannelStatusData> Array 
   |                             DataStream 850000000000000000000000000000000000000000000000

4971 | 0x136b
AES3 Audio Essence Descriptor
   |-- SimpleElement ........<Instance UID> UUID ee.7d.1e.62.6c.a5.9f.4e.bf.df.fa.05.87.ec.c9.f8
   |-- SimpleElement ........<Linked Track ID> UInt32 4
   |-- SimpleElement ........<SampleRate> Rational 25/1
   |-- SimpleElement ........<Essence Container> UniversalLabel 06.0e.2b.***********.01.0d.***********.06.03.00->MXF-GC AES-BWF Audio->MXF-GC Frame-wrapped AES3 audio data
   |-- SimpleElement ........<Audio sampling rate> Rational 48000/1
   |-- SimpleElement ........<Locked/Unlocked> Boolean 1
   |-- SimpleElement ........<Audio Ref Level> Int8 0
   |-- SimpleElement ........<ChannelCount> UInt32 1
   |-- SimpleElement ........<Quantization bits> UInt32 16
   |-- SimpleElement ........<BlockAlign> UInt16 2
   |-- SimpleElement ........<AvgBps> UInt32 96000
   |-- ArrayElement  ........<ChannelStatus-Mode> Array 
   |                             UInt8 1
   |-- ArrayElement  ........<FixedChannelStatusData> Array 
   |                             DataStream 850000000000000000000000000000000000000000000000

5252 | 0x1484
Sequence
   |-- SimpleElement ........<Instance UID> UUID 53.e3.62.0e.5c.44.b2.4c.a8.84.ef.62.0a.9b.1b.c1
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Metadata Track Kinds->SMPTE-12M Timecode Track Inactive User Bits
   |-- SimpleElement ........<Duration> Length 25
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID b5.ec.c8.9d.9b.22.54.4c.84.41.41.f3.3d.a5.13.b8

5352 | 0x14e8
Timecode Component
   |-- SimpleElement ........<Instance UID> UUID b5.ec.c8.9d.9b.22.54.4c.84.41.41.f3.3d.a5.13.b8
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Metadata Track Kinds->SMPTE-12M Timecode Track Inactive User Bits
   |-- SimpleElement ........<Duration> Length 25
   |-- SimpleElement ........<Start Timecode> Position 90000->01:00:00:00
   |-- SimpleElement ........<Rounded Timecode Base> UInt16 25
   |-- SimpleElement ........<Drop Frame> Boolean 0

5547 | 0x15ab
Sequence
   |-- SimpleElement ........<Instance UID> UUID b5.d9.1c.9a.72.2b.cf.4a.b8.f4.88.b1.c4.5a.4e.6d
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Picture Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID a9.23.6f.07.bb.18.82.48.a4.50.ac.b9.ab.6a.20.ce

5647 | 0x160f
SourceClip
   |-- SimpleElement ........<Instance UID> UUID a9.23.6f.07.bb.18.82.48.a4.50.ac.b9.ab.6a.20.ce
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Picture Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- SimpleElement ........<SourcePackageID> UMID32 ***********.***********.***********.***********.***********.***********.***********.***********
   |-- SimpleElement ........<SourceTrackID> UInt32 0
   |-- SimpleElement ........<Start Position> Position 0

5875 | 0x16f3
Sequence
   |-- SimpleElement ........<Instance UID> UUID 7a.51.2d.af.0f.78.db.42.9b.30.4f.a4.a0.e9.50.c4
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID f0.a7.60.58.48.cc.ad.46.97.89.7a.***********.d6

5975 | 0x1757
SourceClip
   |-- SimpleElement ........<Instance UID> UUID f0.a7.60.58.48.cc.ad.46.97.89.7a.***********.d6
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- SimpleElement ........<SourcePackageID> UMID32 ***********.***********.***********.***********.***********.***********.***********.***********
   |-- SimpleElement ........<SourceTrackID> UInt32 0
   |-- SimpleElement ........<Start Position> Position 0

6203 | 0x183b
Sequence
   |-- SimpleElement ........<Instance UID> UUID 7b.96.dd.78.5c.56.32.47.9f.f2.33.4d.6f.ca.6b.fa
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID b0.eb.5e.9c.f4.c8.9a.4b.b4.e7.58.48.2a.ec.d4.7f

6303 | 0x189f
SourceClip
   |-- SimpleElement ........<Instance UID> UUID b0.eb.5e.9c.f4.c8.9a.4b.b4.e7.58.48.2a.ec.d4.7f
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length 25
   |-- SimpleElement ........<SourcePackageID> UMID32 ***********.***********.***********.***********.***********.***********.***********.***********
   |-- SimpleElement ........<SourceTrackID> UInt32 0
   |-- SimpleElement ........<Start Position> Position 0


6656 | 0x1a00
Index Table Segment
   |-- <Instance UID> UUID 3e.86.7d.de.67.8d.13.45.96.cd.36.10.e3.04.cb.90
   |-- <Index Edit Rate> Rational 25/1
   |-- <Index Start Position> Position 0
   |-- <Index Duration> Length 0
   |-- <EditUnitByteCount> UInt32 153088
   |-- <IndexSID> UInt32 1
   |-- <BodySID> UInt32 2
   |-- <Slice Count> UInt8 0
   |-- <Delta Entry Array> Array of DeltaEntry 
   |       <NDE> UInt32 4
   |       <Length> UInt32 6
   |-- <IndexEntryArray> Array of IndexEntry 
   |       <NIE> UInt32 0
   |       <Length> UInt32 0


3834368 | 0x3a8200
Partition Pack (Footer Partition -> closed and complete)
   |--SimpleElement ........<Major Version> UInt16 1
   |--SimpleElement ........<Minor Version> UInt16 2
   |--SimpleElement ........<KAG Size> UInt32 512
   |--SimpleElement ........<ThisPartition> UInt64 3834368
   |--SimpleElement ........<PreviousPartition> UInt64 0
   |--SimpleElement ........<FooterPartition> UInt64 3834368
   |--SimpleElement ........<HeaderByteCount> UInt64 0
   |--SimpleElement ........<IndexByteCount> UInt64 0
   |--SimpleElement ........<IndexSID> UInt32 0
   |--SimpleElement ........<BodyOffset> UInt64 0
   |--SimpleElement ........<BodySID> UInt32 0
   |--SimpleElement ........<Operational Pattern> UniversalLabel 06.0e.2b.***********.01.0d.***********.01.09.00->MXF OP1a SingleItem SinglePackage->MXF OP1a SingleItem SinglePackage MultiTrack Stream Internal->internal essence->stream file->multi-track
   |--BatchElement..........<EssenceContainers> Batch 
   |                             UniversalLabel 06.0e.2b.***********.01.0d.***********.02.02.01->MXF-GC IEC DV 625x50I 25Mbps->MXF-GC Frame-wrapped IEC-DV 625x50I 25Mbps





Information computed during analysis
------------------------------------

Random Index Pack
       Partition |   Offset   | BodySID
   |-------------+------------+---------
   |--         1            0      2
   |--         2      3834368      0


Analysis Results
----------------

KLV errors/warnings/infos: 0



Non-KLV data elements: 0



MXF errors/warnings/infos: 58

       Offset | Code |   Description
--------------+------+-------------------------------------------
            0   <USER>   <GROUP>: The header partition contains Essence Container data
            0   1354   Info: The file does not contain a Random Index pack (RIP).
            0   1356   Info: The header partition contains one or more index table segment(s).
          140   1198   Warning: Fill Item Key with a wrong RP210 version number of 0x01. The correct version number is 0x02. Note: This is reported only once per MXF file.
          512   1167   Error: Re-definition of Universal Label for static MXF local tag 3f.08 from UL 06.0e.2b.***********.***********.***********.00 (dictionary) to UL 06.0e.2b.***********.***********.***********.00 in Primer Pack.
         2142   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         2324   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         2324   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         2324   1292   Info: The value of this UUID is the ISO/IEC 11578-1:1996 nil UUID (i.e. all octets equal 0x00).
         2452   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         2564   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         2656   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         2832   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         2932   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3032   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3127   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3127   1058   Warning: TrackNumber of Tracks inside Material Packages should be set to 0.
         3227   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3327   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3455   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3455   1058   Warning: TrackNumber of Tracks inside Material Packages should be set to 0.
         3555   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3655   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3783   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3783   1058   Warning: TrackNumber of Tracks inside Material Packages should be set to 0.
         3883   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         3983   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         4111   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         4111   1366   Info: This SourcePackage is linked to the essence container with BodySID 2.
         4111   1367   Info: The essence container described by this SourcePackage is indexed by an index table with IndexSID 1.
         4111   1055   Info: This SourcePackage is a Top Level Source Package.
         4307   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         4307   1251   Error: The correct value for property Essence Container is 06.0e.2b.***********.03.0d.***********.7f.01.00 and not 06.0e.2b.***********.01.0d.***********.02.02.01 as encoded in the file.
         4439   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         4439   1251   Error: The correct value for property Picture Essence Coding is 06.0e.2b.***********.01.04.01.02.02.02.01.02.00 and not 06.0e.2b.***********.01.0d.***********.02.02.01 as encoded in the file.
         4439   1251   Error: The correct value for property Vertical Subsampling is 2 and not 1 as encoded in the file.
         4439   1251   Error: The correct value for property Color Siting is 255 and not 4 as encoded in the file.
         4790   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         4790   1260   Error: The correct value for property SampleRate is 48000/1 and not 25/1 as encoded in the file.
         4971   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         4971   1260   Error: The correct value for property SampleRate is 48000/1 and not 25/1 as encoded in the file.
         5152   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         5252   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         5352   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         5447   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         5547   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         5647   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         5647   1261   Info: All bytes of this basic UMID equal 0x00.
         5775   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         5875   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         5975   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         5975   1261   Info: All bytes of this basic UMID equal 0x00.
         6103   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         6203   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         6303   1292   Info: RFC 4122-defined UUID version 4. This is a UUID version that is not enabled by ISO/IEC 11578-1:1996, which normatively defined UUIDs for SMPTE 377M and SMPTE 330M.
         6303   1261   Info: All bytes of this basic UMID equal 0x00.
      3834368   1254   Error: The correct number of elements for property "EssenceContainers" is 2 and not 1 as encoded in the file.
      3834368   1258   Error: Missing element 06.0e.2b.***********.01.0d.***********.06.03.00 in batch "EssenceContainers" when comparing against the Essence Container Labels found in the Descriptors of Top Level Source Packages of this file.


XML Schema instance written successfully to file "xml-instance.xml".
