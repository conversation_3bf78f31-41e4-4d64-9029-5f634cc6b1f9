
IRT <PERSON><PERSON><PERSON> (Cola). Build Sep 28 2007 15:38:52.

MXF::SDK version 4.1.1.174

File Name
---------

 "Sony-00001.mxf"


KLV Stream Information
----------------------

0 | 0x0
 Key: 06.0e.2b.***********.01.0d.***********.02.02.00 -> Partition Pack -> Header Partition -> closed and incomplete
 Length: 104 | 0x68


124 | 0x7c
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 368 | 0x170
 Total Length: 388 | 0x184


512 | 0x200
 Key: 06.0e.2b.***********.01.0d.***********.05.01.00 -> Primer Pack
 Length: 1484 | 0x5cc


2016 | 0x7e0
 Key: 06.0e.2b.***********.01.0d.***********.01.2f.00 -> Structural Metadata -> Preface
 Length: 146 | 0x92


2182 | 0x886
 Key: 06.0e.2b.***********.01.0d.***********.01.30.00 -> Structural Metadata -> Identification
 Length: 108 | 0x6c


2310 | 0x906
 Key: 06.0e.2b.***********.01.0d.***********.01.18.00 -> Structural Metadata -> Content Storage
 Length: 92 | 0x5c


2422 | 0x976
 Key: 06.0e.2b.***********.01.0d.***********.01.23.00 -> Structural Metadata -> Essence Container Data
 Length: 72 | 0x48


2514 | 0x9d2
 Key: 06.0e.2b.***********.01.0d.***********.01.36.00 -> Structural Metadata -> Material Package
 Length: 140 | 0x8c


2674 | 0xa72
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


2774 | 0xad6
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


2874 | 0xb3a
 Key: 06.0e.2b.***********.01.0d.***********.01.14.00 -> Structural Metadata -> Timecode Component
 Length: 75 | 0x4b


2969 | 0xb99
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


3069 | 0xbfd
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


3169 | 0xc61
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


3297 | 0xce1
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


3397 | 0xd45
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


3497 | 0xda9
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


3625 | 0xe29
 Key: 06.0e.2b.***********.01.0d.***********.01.37.00 -> Structural Metadata -> Source Package
 Length: 160 | 0xa0


3805 | 0xedd
 Key: 06.0e.2b.***********.01.0d.***********.01.44.00 -> Structural Metadata -> Multiple Descriptor
 Length: 96 | 0x60


3921 | 0xf51
 Key: 06.0e.2b.***********.01.0d.***********.01.28.00 -> Structural Metadata -> CDCI Essence Descriptor
 Length: 331 | 0x14b


4272 | 0x10b0
 Key: 06.0e.2b.***********.01.0d.***********.01.42.00 -> Structural Metadata -> Sound Essence Descriptor
 Length: 98 | 0x62


4390 | 0x1126
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


4490 | 0x118a
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


4590 | 0x11ee
 Key: 06.0e.2b.***********.01.0d.***********.01.14.00 -> Structural Metadata -> Timecode Component
 Length: 75 | 0x4b


4685 | 0x124d
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


4785 | 0x12b1
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


4885 | 0x1315
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


5013 | 0x1395
 Key: 06.0e.2b.***********.01.0d.***********.01.3b.00 -> Structural Metadata -> Timeline Track
 Length: 80 | 0x50


5113 | 0x13f9
 Key: 06.0e.2b.***********.01.0d.***********.01.0f.00 -> Structural Metadata -> Sequence
 Length: 80 | 0x50


5213 | 0x145d
 Key: 06.0e.2b.***********.01.0d.***********.01.11.00 -> Structural Metadata -> SourceClip
 Length: 108 | 0x6c


5341 | 0x14dd
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 271 | 0x10f
 Total Length: 291 | 0x123


5632 | 0x1600
 Key: 06.0e.2b.***********.01.0d.***********.10.01.00 -> Index Table Segment
 Length: 80 | 0x50


5732 | 0x1664
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 392 | 0x188
 Total Length: 412 | 0x19c


6144 | 0x1800
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


6221 | 0x184d
 Key: 06.0e.2b.***********.01.0d.***********.01.02.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 0 | 0x0
 TrackNumber = 67174912 | 0x04010200 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=02h, MetadataElementUse=00h)


6241 | 0x1861
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 395 | 0x18b
 Total Length: 415 | 0x19f


6656 | 0x1a00
 Key: 06.0e.2b.34.***********.0d.01.03.01.05.01.01.00 -> MXF Generic Container Version 1 MPEG-2 4:2:2 P@ML Picture Essence
 Length: 237628 | 0x3a03c
 TrackNumber = 83951872 | 0x05010100 (itemIdentifier=05h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


244304 | 0x3ba50
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 12700 | 0x319c
 Total Length: 12720 | 0x31b0


257024 | 0x3ec00
 Key: 06.0e.2b.34.***********.0d.01.03.01.06.01.10.00 -> MXF Generic Container Version 1 SMPTE 331M 8-Channel AES3 Sound Essence
 Length: 61444 | 0xf004
 TrackNumber = 100732928 | 0x06011000 (itemIdentifier=06h, NrOfElements=01h, ElementType=10h, ElementNr=00h)


318488 | 0x4dc18
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 468 | 0x1d4
 Total Length: 488 | 0x1e8


318976 | 0x4de00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


319053 | 0x4de4d
 Key: 06.0e.2b.***********.01.0d.***********.01.02.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 0 | 0x0
 TrackNumber = 67174912 | 0x04010200 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=02h, MetadataElementUse=00h)


319073 | 0x4de61
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 395 | 0x18b
 Total Length: 415 | 0x19f


319488 | 0x4e000
 Key: 06.0e.2b.34.***********.0d.01.03.01.05.01.01.00 -> MXF Generic Container Version 1 MPEG-2 4:2:2 P@ML Picture Essence
 Length: 238066 | 0x3a1f2
 TrackNumber = 83951872 | 0x05010100 (itemIdentifier=05h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


557574 | 0x88206
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 12262 | 0x2fe6
 Total Length: 12282 | 0x2ffa


569856 | 0x8b200
 Key: 06.0e.2b.34.***********.0d.01.03.01.06.01.10.00 -> MXF Generic Container Version 1 SMPTE 331M 8-Channel AES3 Sound Essence
 Length: 61444 | 0xf004
 TrackNumber = 100732928 | 0x06011000 (itemIdentifier=06h, NrOfElements=01h, ElementType=10h, ElementNr=00h)


631320 | 0x9a218
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 468 | 0x1d4
 Total Length: 488 | 0x1e8


631808 | 0x9a400
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


631885 | 0x9a44d
 Key: 06.0e.2b.***********.01.0d.***********.01.02.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 0 | 0x0
 TrackNumber = 67174912 | 0x04010200 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=02h, MetadataElementUse=00h)


631905 | 0x9a461
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 395 | 0x18b
 Total Length: 415 | 0x19f


632320 | 0x9a600
 Key: 06.0e.2b.34.***********.0d.01.03.01.05.01.01.00 -> MXF Generic Container Version 1 MPEG-2 4:2:2 P@ML Picture Essence
 Length: 237723 | 0x3a09b
 TrackNumber = 83951872 | 0x05010100 (itemIdentifier=05h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


870063 | 0xd46af
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 12605 | 0x313d
 Total Length: 12625 | 0x3151


882688 | 0xd7800
 Key: 06.0e.2b.34.***********.0d.01.03.01.06.01.10.00 -> MXF Generic Container Version 1 SMPTE 331M 8-Channel AES3 Sound Essence
 Length: 61444 | 0xf004
 TrackNumber = 100732928 | 0x06011000 (itemIdentifier=06h, NrOfElements=01h, ElementType=10h, ElementNr=00h)


944152 | 0xe6818
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 468 | 0x1d4
 Total Length: 488 | 0x1e8


944640 | 0xe6a00
 Key: 06.0e.2b.***********.01.0d.***********.01.01.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 57 | 0x39
 TrackNumber = 67174656 | 0x04010100 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=01h, MetadataElementUse=00h)


944717 | 0xe6a4d
 Key: 06.0e.2b.***********.01.0d.***********.01.02.00 ->  MXF Generic Container Version 1 CP-Compatible System Item
 Length: 0 | 0x0
 TrackNumber = 67174912 | 0x04010200 (itemIdentifier=04h, SchemeIdentifier=01h, MetadataOrControlElementIdentifier=02h, MetadataElementUse=00h)


944737 | 0xe6a61
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 395 | 0x18b
 Total Length: 415 | 0x19f


945152 | 0xe6c00
 Key: 06.0e.2b.34.***********.0d.01.03.01.05.01.01.00 -> MXF Generic Container Version 1 MPEG-2 4:2:2 P@ML Picture Essence
 Length: 238290 | 0x3a2d2
 TrackNumber = 83951872 | 0x05010100 (itemIdentifier=05h, NrOfElements=01h, ElementType=01h, ElementNr=00h)


1183462 | 0x120ee6
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 12038 | 0x2f06
 Total Length: 12058 | 0x2f1a


1195520 | 0x123e00
 Key: 06.0e.2b.34.***********.0d.01.03.01.06.01.10.00 -> MXF Generic Container Version 1 SMPTE 331M 8-Channel AES3 Sound Essence
 Length: 61444 | 0xf004
 TrackNumber = 100732928 | 0x06011000 (itemIdentifier=06h, NrOfElements=01h, ElementType=10h, ElementNr=00h)


1256984 | 0x132e18
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 468 | 0x1d4
 Total Length: 488 | 0x1e8


1257472 | 0x133000
 Key: 06.0e.2b.***********.01.0d.***********.04.04.00 -> Partition Pack -> Footer Partition -> closed and complete
 Length: 104 | 0x68


1257596 | 0x13307c
 Key: 06.0e.2b.***********.***********.***********.00 -> Fill Item (wrong RP210 version number 0x01)
 Length: 368 | 0x170
 Total Length: 388 | 0x184



Partition, Header Metadata, Index Table Segment and RIP Information
-------------------------------------------------------------------

0 | 0x0
Partition Pack (Header Partition -> closed and incomplete)
   |--SimpleElement ........<Major Version> UInt16 1
   |--SimpleElement ........<Minor Version> UInt16 2
   |--SimpleElement ........<KAG Size> UInt32 512
   |--SimpleElement ........<ThisPartition> UInt64 0
   |--SimpleElement ........<PreviousPartition> UInt64 0
   |--SimpleElement ........<FooterPartition> UInt64 0
   |--SimpleElement ........<HeaderByteCount> UInt64 5120
   |--SimpleElement ........<IndexByteCount> UInt64 512
   |--SimpleElement ........<IndexSID> UInt32 1
   |--SimpleElement ........<BodyOffset> UInt64 0
   |--SimpleElement ........<BodySID> UInt32 2
   |--SimpleElement ........<Operational Pattern> UniversalLabel 06.0e.2b.***********.01.0d.***********.01.09.00->MXF OP1a SingleItem SinglePackage->MXF OP1a SingleItem SinglePackage MultiTrack Stream Internal->internal essence->stream file->multi-track
   |--BatchElement..........<EssenceContainers> Batch 
   |                             UniversalLabel 06.0e.2b.***********.01.0d.***********.01.01.01->MXF-GC SMPTE D-10 625x50I 50Mbps->MXF-GC Frame-wrapped SMPTE D-10 625x50I 50Mbps DefinedTemplate

2016 | 0x7e0
Preface
   |-- SimpleElement ........<Instance UID> UUID 26.87.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Last Modified Date> Timestamp 2004/07/05 21:39:00,000
   |-- SimpleElement ........<Version> VersionType major 1 minor 2
   |-- ArrayReferenceElement.<Identifications> Array 
   |                             UUID 00.86.b2.***********.***********.00.46.a5.40.11
   |-- ReferenceElement .....<Content Storage> UUID 06.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Operational Pattern> UniversalLabel 06.0e.2b.***********.01.0d.***********.01.09.00->MXF OP1a SingleItem SinglePackage->MXF OP1a SingleItem SinglePackage MultiTrack Stream Internal->internal essence->stream file->multi-track
   |-- BatchElement .........<EssenceContainers> Batch 
   |                             UniversalLabel 06.0e.2b.***********.01.0d.***********.01.01.01->MXF-GC SMPTE D-10 625x50I 50Mbps->MXF-GC Frame-wrapped SMPTE D-10 625x50I 50Mbps DefinedTemplate
   |-- BatchElement .........<DM Schemes> Batch 

2310 | 0x906
Content Storage
   |-- SimpleElement ........<Instance UID> UUID 06.86.b2.***********.***********.00.46.a5.40.11
   |-- BatchReferenceElement.<Packages> Batch 
   |                             UUID 0c.86.b2.***********.***********.00.46.a5.40.11
   |                             UUID 12.86.b2.***********.***********.00.46.a5.40.11
   |-- BatchReferenceElement.<EssenceContainerData> Batch 
   |                             UUID 18.86.b2.***********.***********.00.46.a5.40.11

2182 | 0x886
Identification
   |-- SimpleElement ........<Instance UID> UUID 00.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<This Generation UID> UUID b4.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Company Name> UTF16String "SONY"
   |-- SimpleElement ........<Product Name> UTF16String "eVTR"
   |-- SimpleElement ........<Version String> UTF16String "1.00"
   |-- SimpleElement ........<Product UID> UUID 06.0e.2b.***********.06.0e.06.01.20.01.01.01.00
   |-- SimpleElement ........<Modification Date> Timestamp 2004/07/05 21:39:00,000

2514 | 0x9d2
Material Package
   |-- SimpleElement ........<Instance UID> UUID 0c.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Package UID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.a8.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Package Creation Date> Timestamp 2004/07/05 21:39:00,000
   |-- SimpleElement ........<Package Modified Date> Timestamp 2004/07/05 21:39:00,000
   |-- ArrayReferenceElement.<Tracks> Array 
   |                             UUID 1e.86.b2.***********.***********.00.46.a5.40.11
   |                             UUID 24.86.b2.***********.***********.00.46.a5.40.11
   |                             UUID 2a.86.b2.***********.***********.00.46.a5.40.11

3625 | 0xe29
Source Package
   |-- SimpleElement ........<Instance UID> UUID 12.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Package UID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.ae.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Package Creation Date> Timestamp 2004/07/05 21:39:00,000
   |-- SimpleElement ........<Package Modified Date> Timestamp 2004/07/05 21:39:00,000
   |-- ArrayReferenceElement.<Tracks> Array 
   |                             UUID 54.86.b2.***********.***********.00.46.a5.40.11
   |                             UUID 5a.86.b2.***********.***********.00.46.a5.40.11
   |                             UUID 60.86.b2.***********.***********.00.46.a5.40.11
   |-- ReferenceElement .....<Descriptor> UUID 8a.86.b2.***********.***********.00.46.a5.40.11

2422 | 0x976
Essence Container Data
   |-- SimpleElement ........<Instance UID> UUID 18.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Linked Package UID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.ae.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<IndexSID> UInt32 1
   |-- SimpleElement ........<BodySID> UInt32 2

2674 | 0xa72
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID 1e.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Track ID> UInt32 1
   |-- SimpleElement ........<Track Number> UInt32 0
   |-- ReferenceElement .....<Sequence> UUID 30.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

2969 | 0xb99
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID 24.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Track ID> UInt32 2
   |-- SimpleElement ........<Track Number> UInt32 83951872
   |-- ReferenceElement .....<Sequence> UUID 3c.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

3297 | 0xce1
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID 2a.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Track ID> UInt32 3
   |-- SimpleElement ........<Track Number> UInt32 100732928
   |-- ReferenceElement .....<Sequence> UUID 48.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

2774 | 0xad6
Sequence
   |-- SimpleElement ........<Instance UID> UUID 30.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Metadata Track Kinds->SMPTE-12M Timecode Track Inactive User Bits
   |-- SimpleElement ........<Duration> Length -1
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID 36.86.b2.***********.***********.00.46.a5.40.11

2874 | 0xb3a
Timecode Component
   |-- SimpleElement ........<Instance UID> UUID 36.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Metadata Track Kinds->SMPTE-12M Timecode Track Inactive User Bits
   |-- SimpleElement ........<Duration> Length -1
   |-- SimpleElement ........<Start Timecode> Position 648250->07:12:10:00
   |-- SimpleElement ........<Rounded Timecode Base> UInt16 25
   |-- SimpleElement ........<Drop Frame> Boolean 0

3069 | 0xbfd
Sequence
   |-- SimpleElement ........<Instance UID> UUID 3c.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Picture Essence Track
   |-- SimpleElement ........<Duration> Length -1
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID 42.86.b2.***********.***********.00.46.a5.40.11

3169 | 0xc61
SourceClip
   |-- SimpleElement ........<Instance UID> UUID 42.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Picture Essence Track
   |-- SimpleElement ........<Duration> Length -1
   |-- SimpleElement ........<SourcePackageID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.ae.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<SourceTrackID> UInt32 2
   |-- SimpleElement ........<Start Position> Position 0

3397 | 0xd45
Sequence
   |-- SimpleElement ........<Instance UID> UUID 48.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length -1
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID 4e.86.b2.***********.***********.00.46.a5.40.11

3497 | 0xda9
SourceClip
   |-- SimpleElement ........<Instance UID> UUID 4e.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length -1
   |-- SimpleElement ........<SourcePackageID> UMID32 06.0a.2b.***********.05.01.01.0d.***********.00.ae.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<SourceTrackID> UInt32 3
   |-- SimpleElement ........<Start Position> Position 0

3805 | 0xedd
Multiple Descriptor
   |-- SimpleElement ........<Instance UID> UUID 8a.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<SampleRate> Rational 25/1
   |-- SimpleElement ........<Essence Container> UniversalLabel 06.0e.2b.***********.01.0d.***********.01.01.01->MXF-GC SMPTE D-10 625x50I 50Mbps->MXF-GC Frame-wrapped SMPTE D-10 625x50I 50Mbps DefinedTemplate
   |-- ArrayReferenceElement.<Sub Descriptor UIDs> Array 
   |                             UUID 90.86.b2.***********.***********.00.46.a5.40.11
   |                             UUID 96.86.b2.***********.***********.00.46.a5.40.11

4390 | 0x1126
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID 54.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Track ID> UInt32 1
   |-- SimpleElement ........<Track Number> UInt32 0
   |-- ReferenceElement .....<Sequence> UUID 66.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

4685 | 0x124d
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID 5a.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Track ID> UInt32 2
   |-- SimpleElement ........<Track Number> UInt32 83951872
   |-- ReferenceElement .....<Sequence> UUID 72.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

5013 | 0x1395
Timeline Track
   |-- SimpleElement ........<Instance UID> UUID 60.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Track ID> UInt32 3
   |-- SimpleElement ........<Track Number> UInt32 100732928
   |-- ReferenceElement .....<Sequence> UUID 7e.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Edit Rate> Rational 25/1
   |-- SimpleElement ........<Origin> Position 0

3921 | 0xf51
CDCI Essence Descriptor
   |-- SimpleElement ........<Instance UID> UUID 90.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Linked Track ID> UInt32 2
   |-- SimpleElement ........<SampleRate> Rational 25/1
   |-- SimpleElement ........<Essence Container> UniversalLabel 06.0e.2b.***********.01.0d.***********.01.01.01->MXF-GC SMPTE D-10 625x50I 50Mbps->MXF-GC Frame-wrapped SMPTE D-10 625x50I 50Mbps DefinedTemplate
   |-- SimpleElement ........<Signal Standard> SignalStandard 1
   |-- SimpleElement ........<Frame Layout> UInt8 1
   |-- SimpleElement ........<Stored Width> UInt32 720
   |-- SimpleElement ........<Stored Height> UInt32 304
   |-- SimpleElement ........<StoredF2Offset> Int32 0
   |-- SimpleElement ........<SampledWidth> UInt32 720
   |-- SimpleElement ........<Sampled Height> UInt32 304
   |-- SimpleElement ........<SampledXOffset> Int32 0
   |-- SimpleElement ........<SampledYOffset> Int32 0
   |-- SimpleElement ........<DisplayHeight> UInt32 288
   |-- SimpleElement ........<DisplayWidth> UInt32 720
   |-- SimpleElement ........<DisplayXOffset> Int32 0
   |-- SimpleElement ........<DisplayYOffset> Int32 16
   |-- SimpleElement ........<DisplayF2Offset> Int32 0
   |-- SimpleElement ........<Aspect Ratio> Rational 4/3
   |-- ArrayElement  ........<Video Line Map> Array 
   |                             Int32 7
   |                             Int32 320
   |-- SimpleElement ........<Capture Gamma> UniversalLabel 06.0e.2b.***********.01.04.01.01.01.***********->Transfer Characteristic->ITU-R BT470 Transfer Characteristic
   |-- SimpleElement ........<Image Alignment Offset> UInt32 0
   |-- SimpleElement ........<Image Start Offset> UInt32 0
   |-- SimpleElement ........<Image End Offset> UInt32 0
   |-- SimpleElement ........<FieldDominance> UInt8 1
   |-- SimpleElement ........<Picture Essence Coding> UniversalLabel 06.0e.2b.***********.01.04.01.02.02.***********->SMPTE D-10 MPEG-2 422P-ML->SMPTE D-10 50Mbps 625x50I
   |-- SimpleElement ........<Component Depth> UInt32 8
   |-- SimpleElement ........<Horizontal Subsampling> UInt32 2
   |-- SimpleElement ........<Vertical Subsampling> UInt32 1
   |-- SimpleElement ........<Color Siting> UInt8 4
   |-- SimpleElement ........<ReversedByteOrder> Boolean 0
   |-- SimpleElement ........<PaddingBits> Int16 0
   |-- SimpleElement ........<Black Ref Level> UInt32 16
   |-- SimpleElement ........<White Ref level> UInt32 235
   |-- SimpleElement ........<Color Range> UInt32 225

4272 | 0x10b0
Sound Essence Descriptor
   |-- SimpleElement ........<Instance UID> UUID 96.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Linked Track ID> UInt32 3
   |-- SimpleElement ........<SampleRate> Rational 25/1
   |-- SimpleElement ........<Essence Container> UniversalLabel 06.0e.2b.***********.01.0d.***********.01.01.01->MXF-GC SMPTE D-10 625x50I 50Mbps->MXF-GC Frame-wrapped SMPTE D-10 625x50I 50Mbps DefinedTemplate
   |-- SimpleElement ........<Audio sampling rate> Rational 48000/1
   |-- SimpleElement ........<Locked/Unlocked> Boolean 1
   |-- SimpleElement ........<Audio Ref Level> Int8 0
   |-- SimpleElement ........<ChannelCount> UInt32 8
   |-- SimpleElement ........<Quantization bits> UInt32 16

4490 | 0x118a
Sequence
   |-- SimpleElement ........<Instance UID> UUID 66.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Metadata Track Kinds->SMPTE-12M Timecode Track Inactive User Bits
   |-- SimpleElement ........<Duration> Length -1
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID 6c.86.b2.***********.***********.00.46.a5.40.11

4590 | 0x11ee
Timecode Component
   |-- SimpleElement ........<Instance UID> UUID 6c.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Metadata Track Kinds->SMPTE-12M Timecode Track Inactive User Bits
   |-- SimpleElement ........<Duration> Length -1
   |-- SimpleElement ........<Start Timecode> Position 648250->07:12:10:00
   |-- SimpleElement ........<Rounded Timecode Base> UInt16 25
   |-- SimpleElement ........<Drop Frame> Boolean 0

4785 | 0x12b1
Sequence
   |-- SimpleElement ........<Instance UID> UUID 72.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Picture Essence Track
   |-- SimpleElement ........<Duration> Length -1
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID 78.86.b2.***********.***********.00.46.a5.40.11

4885 | 0x1315
SourceClip
   |-- SimpleElement ........<Instance UID> UUID 78.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Picture Essence Track
   |-- SimpleElement ........<Duration> Length -1
   |-- SimpleElement ........<SourcePackageID> UMID32 ***********.***********.***********.***********.***********.***********.***********.***********
   |-- SimpleElement ........<SourceTrackID> UInt32 0
   |-- SimpleElement ........<Start Position> Position 0

5113 | 0x13f9
Sequence
   |-- SimpleElement ........<Instance UID> UUID 7e.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length -1
   |-- ArrayReferenceElement.<Structural Components> Array 
   |                             UUID 84.86.b2.***********.***********.00.46.a5.40.11

5213 | 0x145d
SourceClip
   |-- SimpleElement ........<Instance UID> UUID 84.86.b2.***********.***********.00.46.a5.40.11
   |-- SimpleElement ........<Data Definition> UniversalLabel 06.0e.2b.***********.***********.***********.00->Essence Track Kinds->Sound Essence Track
   |-- SimpleElement ........<Duration> Length -1
   |-- SimpleElement ........<SourcePackageID> UMID32 ***********.***********.***********.***********.***********.***********.***********.***********
   |-- SimpleElement ........<SourceTrackID> UInt32 0
   |-- SimpleElement ........<Start Position> Position 0


5632 | 0x1600
Index Table Segment
   |-- <Instance UID> UUID ba.86.b2.***********.***********.00.46.a5.40.11
   |-- <Index Edit Rate> Rational 25/1
   |-- <Index Start Position> Position 0
   |-- <Index Duration> Length 0
   |-- <EditUnitByteCount> UInt32 312832
   |-- <IndexSID> UInt32 1
   |-- <BodySID> UInt32 2


1257472 | 0x133000
Partition Pack (Footer Partition -> closed and complete)
   |--SimpleElement ........<Major Version> UInt16 1
   |--SimpleElement ........<Minor Version> UInt16 2
   |--SimpleElement ........<KAG Size> UInt32 512
   |--SimpleElement ........<ThisPartition> UInt64 1257472
   |--SimpleElement ........<PreviousPartition> UInt64 0
   |--SimpleElement ........<FooterPartition> UInt64 1257472
   |--SimpleElement ........<HeaderByteCount> UInt64 0
   |--SimpleElement ........<IndexByteCount> UInt64 0
   |--SimpleElement ........<IndexSID> UInt32 0
   |--SimpleElement ........<BodyOffset> UInt64 0
   |--SimpleElement ........<BodySID> UInt32 0
   |--SimpleElement ........<Operational Pattern> UniversalLabel 06.0e.2b.***********.01.0d.***********.01.09.00->MXF OP1a SingleItem SinglePackage->MXF OP1a SingleItem SinglePackage MultiTrack Stream Internal->internal essence->stream file->multi-track
   |--BatchElement..........<EssenceContainers> Batch 
   |                             UniversalLabel 06.0e.2b.***********.01.0d.***********.01.01.01->MXF-GC SMPTE D-10 625x50I 50Mbps->MXF-GC Frame-wrapped SMPTE D-10 625x50I 50Mbps DefinedTemplate





Information computed during analysis
------------------------------------

Random Index Pack
       Partition |   Offset   | BodySID
   |-------------+------------+---------
   |--         1            0      2
   |--         2      1257472      0


Analysis Results
----------------

KLV errors/warnings/infos: 0



Non-KLV data elements: 0



MXF errors/warnings/infos: 42

       Offset | Code |   Description
--------------+------+-------------------------------------------
            0   <USER>   <GROUP>: The header partition contains Essence Container data
            0   1354   Info: The file does not contain a Random Index pack (RIP).
            0   1362   Info: The file is "incomplete", i.e. it does not contain a "complete" header metadata instance.
            0   1356   Info: The header partition contains one or more index table segment(s).
          124   1198   Warning: Fill Item Key with a wrong RP210 version number of 0x01. The correct version number is 0x02. Note: This is reported only once per MXF file.
         2016   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (26.87.b2.***********.***********.00.46.a5.40.11)
         2182   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (00.86.b2.***********.***********.00.46.a5.40.11)
         2182   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (b4.86.b2.***********.***********.00.46.a5.40.11)
         2182   1293   Error: Unknown UUID version Field value 0x00. The value of the property with type UUID may not be a valid UUID. (06.0e.2b.***********.06.0e.06.01.20.01.01.01.00)
         2310   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (06.86.b2.***********.***********.00.46.a5.40.11)
         2422   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (18.86.b2.***********.***********.00.46.a5.40.11)
         2514   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (0c.86.b2.***********.***********.00.46.a5.40.11)
         2674   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (1e.86.b2.***********.***********.00.46.a5.40.11)
         2774   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (30.86.b2.***********.***********.00.46.a5.40.11)
         2874   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (36.86.b2.***********.***********.00.46.a5.40.11)
         2969   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (24.86.b2.***********.***********.00.46.a5.40.11)
         2969   1058   Warning: TrackNumber of Tracks inside Material Packages should be set to 0.
         3069   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (3c.86.b2.***********.***********.00.46.a5.40.11)
         3169   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (42.86.b2.***********.***********.00.46.a5.40.11)
         3297   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (2a.86.b2.***********.***********.00.46.a5.40.11)
         3297   1058   Warning: TrackNumber of Tracks inside Material Packages should be set to 0.
         3397   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (48.86.b2.***********.***********.00.46.a5.40.11)
         3497   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (4e.86.b2.***********.***********.00.46.a5.40.11)
         3625   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (12.86.b2.***********.***********.00.46.a5.40.11)
         3625   1366   Info: This SourcePackage is linked to the essence container with BodySID 2.
         3625   1367   Info: The essence container described by this SourcePackage is indexed by an index table with IndexSID 1.
         3625   1055   Info: This SourcePackage is a Top Level Source Package.
         3805   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (8a.86.b2.***********.***********.00.46.a5.40.11)
         3921   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (90.86.b2.***********.***********.00.46.a5.40.11)
         4272   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (96.86.b2.***********.***********.00.46.a5.40.11)
         4272   1250   Info: The correct value for property Quantization bits is 24 and not 16 as encoded in the file. Note: Best effort properties may not need to hold their correct value in an incomplete partition.
         4390   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (54.86.b2.***********.***********.00.46.a5.40.11)
         4490   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (66.86.b2.***********.***********.00.46.a5.40.11)
         4590   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (6c.86.b2.***********.***********.00.46.a5.40.11)
         4685   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (5a.86.b2.***********.***********.00.46.a5.40.11)
         4785   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (72.86.b2.***********.***********.00.46.a5.40.11)
         4885   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (78.86.b2.***********.***********.00.46.a5.40.11)
         4885   1261   Info: All bytes of this basic UMID equal 0x00.
         5013   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (60.86.b2.***********.***********.00.46.a5.40.11)
         5113   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (7e.86.b2.***********.***********.00.46.a5.40.11)
         5213   1293   Error: Unknown UUID version Field value 0x08. The value of the property with type UUID may not be a valid UUID. (84.86.b2.***********.***********.00.46.a5.40.11)
         5213   1261   Info: All bytes of this basic UMID equal 0x00.


XML Schema instance written successfully to file "xml-instance.xml".
