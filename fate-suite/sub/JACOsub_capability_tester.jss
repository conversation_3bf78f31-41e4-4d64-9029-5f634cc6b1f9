#
# JACOsub sample for FFmpeg based on demo.txt
#

#TIMERES 100

0:00:00.00 0:00:04.00 VM JACOsub\n\nThis script demonstrates \
                         some of the capa\
                         bilities of JACOsub.

0:00:04.00 0:00:06.50 VT Text may be positioned at the top,
0:00:05.00 0:00:07.10 vm middle,
0:00:06.00 0:00:07.70 VB or bottom of the screen.

0:00:08.00 0:00:11.00 vm {this is a comment} (And, you just saw, {another \
                         comment} timing ranges for different lines of \
                         text.

#S 0.12


0:00:11.00 0:00:13.50 JL Within margin constraints\n\
                         that you set, text may be\nleft justified,
0:00:13.50 0:00:14.75 JC {the JC is redundant - it's the default}\
                         center\njustified,
0:00:14.75 0:00:16.00 JR and also\nright justified.

0:00:22.30 0:00:27.80 VL3 Text may appear in different styles\n(Normal, \
                          \BBold\N, \IItalic\N)

@0007600 @0008100 vm \n\nAt that time, you may press any key to return to the Editor.
@0007600 @0008100 VA OK, this script will be finished when the screen goes blank.
