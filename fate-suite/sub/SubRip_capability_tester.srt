﻿1
00:00:00,000 --> 00:00:00,000
Don't show this text it may be used to insert hidden data

2
00:00:01,500 --> 00:00:04,500
SubRip subtitles capability tester 1.3o by ale5000
<b><i>Use VLC 1.1 or higher as reference for most things and MPC Home Cinema for others</i></b>
<font color="#0000FF">This text should be blue</font>
<font color="red">This text should be red</font>
<font color="#000000">This text should be black</font>
<font face="Webdings">If you see this with the normal font, the player don't (fully) support font face</font>

3
00:00:04,500 --> 00:00:04,500
Hidden

4
00:00:04,501 --> 00:00:07,500
<font size="8">This text should be small</font>
This text should be normal
<font size="35">This text should be big</font>

5
00:00:07,501 --> 00:00:11,500
This should be an E with an accent: È
日本語
<font size=30><b><i><u>This text should be bold, italics and underline</u></i></b></font>
<font size=9 color="00ff00">This text should be small and green</font>
<font color=#ff0000 size=9>This text should be small and red</font>
<font color=brown size=24>This text should be big and brown</font>

6
00:00:11,501 --> 00:00:14,500
<b>This line should be bold</b>
<i>This line should be italics</i>
<u>This line should be underline</u>
<s>This line should be strikethrough</s>
<u>Both lines
should be underline</u>

7
00:00:14,501 --> 00:00:17,500
>
It would be a good thing to
<invalid_tag>hide invalid html tags that are closed and show the text in them</invalid_tag>
<invalid_tag_unclosed>but show un-closed invalid html tags
Show not opened tags</invalid_tag_not_opened>
<

8
00:00:17,501 --> 00:00:20,500
and also
<invalid_tag par=5>hide invalid html tags with parameters that are closed and show the text in them</invalid_tag>
<invalid_tag_uc par=5>but show un-closed invalid html tags
<u>This text should be showed underlined without problems also: 2<3,5>1,4<6</u>
This shouldn't be underlined

9
00:00:20,501 --> 00:00:21,500
This text should be in the normal position...

10
00:00:21,501 --> 00:00:22,500  X1:000 X2:000 Y1:050 Y2:100
This text should NOT be in the normal position

11
00:00:22,501 --> 00:00:24,500
Implementation is the same of the ASS tag
{\an8}This text should be at the
top and horizontally centered

12
00:00:22,501 --> 00:00:24,500
{\an5}This text should be at the
middle and horizontally centered

13
00:00:22,501 --> 00:00:24,500
{\an2}This text should be at the
bottom and horizontally centered

14
00:00:24,501 --> 00:00:26,500
This text should be at the
top and horizontally at the left{\an7}

15
00:00:24,501 --> 00:00:26,500
{\an4}This text should be at the
middle and horiz{\an6}ontally at the left
(The second position must be ignored)

16
00:00:24,501 --> 00:00:26,500
{\an1}This text should be at the
bottom and horizontally at the left

17
00:00:26,501 --> 00:00:28,500
{\an9}This text should be at the
top and horizontally at the right

18
00:00:26,501 --> 00:00:28,500
{\an6}This text should be at the
middle and horizontally at the right

19
00:00:26,501 --> 00:00:28,500
{\an3}This text should be at the
bottom and horizontally at the right

20
00:00:28,501 --> 00:00:31,500
<font color="#00FF00" size="6">This could be the <font size="35">m<font color="#000000">o</font>st</font> difficult thing to implement</font>

21
00:00:31,501 --> 00:00:50,500
First text

22
00:00:33,500 --> 00:00:35,500
Second, it shouldn't overlap first

23
00:00:35,501 --> 00:00:37,500
Third, it should replace second

24
00:00:36,501 --> 00:00:50,500
Fourth, it shouldn't overlap first and third

25
00:00:40,501 --> 00:00:45,500
Fifth, it should replace third

26
00:00:45,501 --> 00:00:50,500
Sixth, it shouldn't be
showed overlapped

27
00:00:50,501 --> 00:00:52,500
TEXT 1 (bottom)

28
00:00:50,501 --> 00:00:52,500
text 2

29
00:00:52,501 --> 00:00:54,500
Hide these tags: {\some_letters_or_numbers_or_chars}
also hide these tags: {Y:some_letters_or_numbers_or_chars}
but show this: {normal text}

30
00:00:54,501 --> 00:01:00,500
{\an8}
\ N is a forced line break
\ h is a hard space
Normal spaces at the start and at the end of the line are trimmed while hard spaces are not trimmed.
The\hline\hwill\hnever\hbreak\hautomatically\hright\hbefore\hor\hafter\ha\hhard\hspace.\h:-D

31
00:00:54,501 --> 00:00:56,500
{\an1}
\h\h\h\h\hA (05 hard spaces followed by a letter)
     A (Normal  spaces followed by a letter)
A (No hard spaces followed by a letter)

32
00:00:56,501 --> 00:00:58,500
\h\h\h\h\hA (05 hard spaces followed by a letter)
     A (Normal  spaces followed by a letter)
A (No hard spaces followed by a letter)
Show this: \TEST and this: \-)

33
00:00:58,501 --> 00:01:00,500
{\an3}
A letter followed by 05 hard spaces: A\h\h\h\h\h
A letter followed by normal  spaces: A     
A letter followed by no hard spaces: A
05 hard  spaces between letters: A\h\h\h\h\hA
5 normal spaces between letters: A     A
\N^--Forced line break

34
00:01:00,501 --> 00:01:02,500
<s>Both line should be strikethrough,
yes.</s>
<wwww>Correctly closed tags
should be hidden.</wwww>

35
00:01:02,501 --> 00:01:04,500
It shouldn't be strikethrough,
not opened tag showed as text.</s>
Not opened tag showed as text.</xxxxx>

36
00:01:04,501 --> 00:01:06,500
<s>Three lines should be strikethrough,
yes.
<yyyy>Not closed tags showed as text

37
00:01:06,501 --> 00:01:08,500
<s>Both line should be strikethrough but
the wrong closing tag should be showed</b>

