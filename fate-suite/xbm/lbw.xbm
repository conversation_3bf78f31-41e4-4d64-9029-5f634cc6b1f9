#define image_width 200
#define image_height 190
static unsigned char image_bits[] = {
 0x22, 0x22, 0xA2, 0xBB, 0xBB, 0xBB, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xBA, 0x22, 0x22, 0x22, 0x22, 0xA0, 0xBB, 0xBA, 0xBA, 0x3A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xA8, 0xA8, 0x80, 0xEE, 0xEE, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x8A, 0xAA, 0xAA, 0x08, 0xE0, 0xAA, 0xAA, 0xAA, 0xEA,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x51, 0x55, 0x55, 0x55, 0x55,
 0x22, 0x22, 0xA2, 0xBB, 0xBB, 0xAB, 0xAB, 0xAA, 0xAA, 0xAA, 0xAA, 0x2B, 0xAA, 0xAA, 0xAB, 0xBB, 0x23, 0x22, 0x22, 0x22, 0x80, 0xAB, 0xAB, 0xAB, 0xFA,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x45, 0x55, 0x55, 0x55, 0x55,
 0x8A, 0x0A, 0x88, 0xEE, 0xAE, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x8A, 0x8A, 0x8A, 0x8A, 0x00, 0xAE, 0xAA, 0xAA, 0xFE,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x11, 0x55, 0x55, 0x55, 0x77,
 0x22, 0x22, 0xA2, 0xBB, 0xBB, 0xBB, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xBA, 0xBA, 0xBA, 0x23, 0x22, 0x22, 0x22, 0x02, 0xBA, 0xBB, 0xAA, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0x88, 0x88, 0xA8, 0xEE, 0xEA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xEA, 0xAA, 0x88, 0xA8, 0xA8, 0x02, 0xA8, 0xAA, 0xEA, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x55, 0x55, 0x75, 0x77,
 0x22, 0x22, 0xA2, 0xBB, 0xBB, 0xBB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xAB, 0xBB, 0x2B, 0x22, 0x22, 0x22, 0x02, 0xA0, 0xAB, 0xFA, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55,
 0x0A, 0x88, 0x8A, 0xAE, 0xAE, 0xAE, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x8A, 0x8A, 0x8A, 0x0A, 0xE0, 0xAA, 0xFE, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x51, 0x55, 0x77, 0x77,
 0x22, 0x22, 0xA2, 0xBB, 0xBB, 0xBB, 0xBA, 0xAA, 0xAA, 0xAA, 0xBA, 0xBA, 0xBA, 0xAA, 0xBA, 0xBA, 0x2B, 0x22, 0x22, 0x22, 0x22, 0x80, 0xBB, 0xFF, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x45, 0xD5, 0x55, 0x55,
 0x88, 0xAA, 0xA8, 0xFE, 0xEE, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x0A, 0x00, 0xA8, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x80, 0xFA, 0xFF, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x11, 0x75, 0x77, 0x77,
 0x22, 0x2A, 0xA2, 0xBF, 0xBB, 0xAB, 0xAB, 0xAB, 0xAB, 0x2B, 0x22, 0x22, 0x82, 0xAB, 0xAB, 0xBB, 0x2B, 0x22, 0x22, 0x2A, 0x2A, 0x02, 0xFA, 0xFF, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x45, 0x5D, 0x55, 0x5D,
 0x88, 0x0A, 0x88, 0xEE, 0xAE, 0xAE, 0xAA, 0xAA, 0xAA, 0xAA, 0x0A, 0x08, 0x00, 0xA8, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x02, 0xFE, 0xFF, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x75, 0x77, 0x77,
 0xA2, 0x2B, 0xA2, 0xBB, 0xBB, 0xBB, 0xBB, 0xBA, 0xBA, 0xAA, 0x22, 0x22, 0x20, 0xA0, 0xBB, 0xBB, 0x2B, 0x22, 0x22, 0x22, 0x22, 0x02, 0xFF, 0xFF, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5,
 0xE8, 0x8B, 0xA8, 0xFE, 0xEE, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x00, 0x00, 0x80, 0xAE, 0xEA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xCA, 0xFF, 0xFF, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x57, 0x57, 0x77,
 0xA2, 0x2B, 0xA2, 0xBF, 0xBB, 0xBB, 0xAB, 0xA3, 0xAB, 0x2B, 0x2A, 0x02, 0x02, 0x02, 0xB0, 0xBB, 0x2B, 0xA2, 0x2B, 0x2A, 0x2A, 0xF2, 0xFF, 0xFF, 0x3F,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D,
 0xEA, 0x0B, 0x88, 0xEE, 0xEE, 0xAE, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x00, 0x00, 0x00, 0xE0, 0xEE, 0x8A, 0xAA, 0xAB, 0xAA, 0xAA, 0xFA, 0xFF, 0xFF, 0xAF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x11, 0x51, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x77, 0x77, 0x55,
 0xBA, 0x2B, 0xA2, 0xBB, 0xBB, 0xBB, 0xBB, 0xBA, 0xBB, 0xAB, 0x22, 0x22, 0x02, 0x00, 0x80, 0xBB, 0x2B, 0xA2, 0x2B, 0x22, 0x22, 0xFE, 0xFF, 0xFF, 0x2B,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFE, 0xAB, 0xA8, 0xFE, 0xEE, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x80, 0x00, 0x00, 0x00, 0xFE, 0xAA, 0xAA, 0xAF, 0xAA, 0xAA, 0xFF, 0xFF, 0xFF, 0xAA,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x15, 0x15, 0x55, 0x55, 0x55, 0x57, 0x55, 0x55, 0x77, 0x57, 0x57, 0x55,
 0xBB, 0x2B, 0xA2, 0xBF, 0xBB, 0xBB, 0xAB, 0xBB, 0xAB, 0x2A, 0x2A, 0x0A, 0x00, 0x00, 0x00, 0xB8, 0x2B, 0xA2, 0xBF, 0x2A, 0xA2, 0xFF, 0xFF, 0xBF, 0x2A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0xD5, 0x5D, 0x55, 0x55, 0x55,
 0xEF, 0x8F, 0x88, 0xEE, 0xEF, 0xAE, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x02, 0x00, 0x00, 0x00, 0xE0, 0x8B, 0xAA, 0xFF, 0xAA, 0xEA, 0xFF, 0xFF, 0xBF, 0x8A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x11, 0x51, 0x55, 0x55, 0x77, 0x55, 0x75, 0x77, 0x77, 0x57, 0x55,
 0xBB, 0x2B, 0xA2, 0xBB, 0xBB, 0xBB, 0xB3, 0xBB, 0xBB, 0xAA, 0x3A, 0x00, 0x00, 0x00, 0x00, 0x80, 0x2B, 0xA2, 0xFF, 0xA3, 0xFA, 0xFF, 0xFF, 0xAF, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x45, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFE, 0xAB, 0xA8, 0xFE, 0xEE, 0xAA, 0xEA, 0xEA, 0xAA, 0xAA, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x80, 0xAB, 0xAA, 0xFF, 0x01, 0xFC, 0xFF, 0xFF, 0xAB, 0x88,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x55, 0x15, 0x15, 0x55, 0x55, 0x77, 0x15, 0x75, 0x77, 0x57, 0x55, 0x55,
 0xBB, 0x2B, 0xA2, 0xBF, 0xBB, 0xBB, 0xBB, 0xBB, 0xAB, 0xAB, 0x22, 0x02, 0x02, 0x00, 0x00, 0x00, 0x2B, 0xA2, 0x3F, 0x02, 0xF8, 0xFF, 0xFF, 0x2B, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x45, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEF, 0x0F, 0x88, 0xFE, 0xEE, 0xAE, 0xA8, 0xAA, 0xAA, 0x2A, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8E, 0xAA, 0x0F, 0x00, 0xF8, 0xFF, 0xFF, 0x8A, 0x8A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x11, 0x55, 0x55, 0x55, 0x51, 0x71, 0x77, 0x77, 0x55, 0x55,
 0xBB, 0x2B, 0xA2, 0xBB, 0xBB, 0xBB, 0xB8, 0xBB, 0xAB, 0x2A, 0x22, 0x22, 0x20, 0x00, 0x20, 0x00, 0x20, 0xA2, 0x23, 0x00, 0xF8, 0xFF, 0x3F, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x54, 0x55, 0x5D, 0x55, 0x55,
 0xEE, 0xAA, 0xA8, 0xFE, 0xEE, 0xEA, 0xE8, 0xAA, 0xAA, 0xAA, 0x02, 0x00, 0x00, 0x00, 0x08, 0x00, 0xA0, 0x0A, 0x00, 0x00, 0xF8, 0xFF, 0xAF, 0x8A, 0xA8,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x55, 0x15, 0x55, 0x55, 0x15, 0x15, 0x75, 0x77, 0x57, 0x55, 0x55,
 0xBB, 0x2B, 0xA2, 0xBF, 0xBB, 0x3B, 0xBA, 0xBB, 0x2B, 0x22, 0x22, 0x22, 0x22, 0x02, 0x02, 0x00, 0x02, 0x02, 0x00, 0x00, 0xF8, 0xFF, 0x2F, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x5D, 0x55, 0x55, 0x55,
 0xEE, 0x0A, 0x88, 0xEE, 0xEE, 0x2E, 0xA8, 0xAE, 0xAA, 0xAA, 0x08, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0xFF, 0xAB, 0x8A, 0x8A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x75, 0x77, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0xA2, 0xBB, 0xBB, 0x3B, 0xA2, 0xBB, 0xAB, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x00, 0x00, 0x00, 0xFE, 0xFF, 0x23, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0x8A, 0x80, 0xFE, 0xEE, 0x2E, 0xA8, 0xAE, 0xAA, 0xAA, 0xAA, 0x88, 0x88, 0x08, 0x80, 0x08, 0x00, 0x00, 0x00, 0x20, 0xFE, 0xFF, 0xAA, 0x88, 0xA8,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x15, 0x15, 0x55, 0x57, 0x57, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0xA2, 0xBF, 0xBB, 0x3B, 0x22, 0x3B, 0xAA, 0x2A, 0x22, 0x22, 0x22, 0x22, 0x22, 0x02, 0x00, 0x00, 0x00, 0x2A, 0xFF, 0x3F, 0x22, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55,
 0xEF, 0x0A, 0x88, 0xEE, 0xEE, 0x2E, 0xA8, 0xAE, 0xAA, 0xAA, 0x8A, 0xA8, 0x8A, 0x0A, 0x8A, 0x00, 0x00, 0x00, 0x80, 0x8A, 0xFF, 0xBF, 0x8A, 0x8A, 0x8A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x51, 0x15, 0x77, 0x57, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0xA2, 0xBF, 0xBB, 0x3B, 0x20, 0xAA, 0xAA, 0xAA, 0xB2, 0x3B, 0x2A, 0xA2, 0x23, 0x00, 0x00, 0x00, 0xA0, 0x8B, 0xFF, 0x3F, 0x22, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0x8A, 0x80, 0xFE, 0xEE, 0x3E, 0xA0, 0xAA, 0xAA, 0xAA, 0xAA, 0xBE, 0xEA, 0xFF, 0x03, 0x00, 0x00, 0x00, 0xE8, 0xE2, 0xFF, 0xAF, 0x88, 0xA8, 0xA8,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x15, 0x55, 0x15, 0x55, 0x55, 0x55, 0x77, 0x57, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0x22, 0xBF, 0xBB, 0x3B, 0xA0, 0xBA, 0xAA, 0x2A, 0xBB, 0xBB, 0xBF, 0xFF, 0x02, 0x02, 0x02, 0xA0, 0x3B, 0xF8, 0xFF, 0x23, 0x22, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x5D, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0x0A, 0x88, 0xFE, 0xEF, 0x6E, 0xA0, 0xAA, 0xAA, 0xAA, 0xEA, 0xBF, 0xEF, 0xBF, 0x00, 0x00, 0x00, 0xEA, 0x0A, 0xFE, 0xFF, 0xAB, 0x88, 0x8A, 0x8A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x57, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x77, 0x77, 0x55, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0xA2, 0xBF, 0xBB, 0xBB, 0xA0, 0xBA, 0xAB, 0xAB, 0xFF, 0xBF, 0xFB, 0x23, 0x20, 0x22, 0xA0, 0x2B, 0x82, 0xFF, 0xFF, 0x23, 0x22, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEA, 0x8A, 0x80, 0xFE, 0xEE, 0xEE, 0x80, 0xAA, 0xAA, 0xFA, 0xFF, 0xFF, 0xFE, 0x08, 0x00, 0x00, 0xF8, 0xAA, 0xE0, 0xFF, 0xFF, 0x8A, 0x88, 0xA8, 0xA8,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x51, 0x55, 0x55, 0x55, 0x55, 0x77, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x57, 0x57, 0x55, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0x22, 0xBF, 0xBB, 0xBB, 0x03, 0xAB, 0xAA, 0xBB, 0xBF, 0xFF, 0xBB, 0x03, 0x22, 0x02, 0xBA, 0x3B, 0xFA, 0xFF, 0xBF, 0x22, 0x22, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0x0F, 0x88, 0xFE, 0xEF, 0xAE, 0x06, 0xAA, 0xAE, 0xFE, 0xFF, 0xFF, 0xBF, 0x00, 0x08, 0x00, 0xE8, 0xAE, 0xFF, 0xFF, 0xBF, 0x8A, 0x88, 0x88, 0x8A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x75, 0x77, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x77, 0x75, 0x57, 0x55, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0xA2, 0xBF, 0xBB, 0xBB, 0x03, 0xBB, 0xBA, 0xFB, 0xFB, 0xFF, 0x3B, 0x22, 0x22, 0x00, 0xA0, 0xAB, 0xFF, 0xFF, 0xBF, 0x22, 0x22, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0xAA, 0x80, 0xFE, 0xEE, 0xAE, 0x8E, 0xAA, 0xEA, 0xFF, 0xFF, 0xFE, 0x2A, 0x80, 0x08, 0x00, 0xE0, 0xAF, 0xFF, 0xFF, 0xAF, 0xAA, 0x88, 0x88, 0xA8,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x57, 0x55, 0x75, 0x55, 0x55, 0x55, 0x15, 0x51, 0x57, 0x57, 0x77, 0x57, 0x55, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0x22, 0xBF, 0xBB, 0xAB, 0x8B, 0xAB, 0xFB, 0xFF, 0xBF, 0xBF, 0x23, 0x22, 0x22, 0x00, 0xA0, 0x3F, 0xFF, 0xFF, 0x2F, 0x22, 0x22, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0x0F, 0x88, 0xEE, 0xAE, 0xAE, 0x8A, 0xAA, 0xFF, 0xFF, 0xFF, 0xAF, 0x80, 0x88, 0x08, 0x00, 0x80, 0xBF, 0xFF, 0xFF, 0xAF, 0x88, 0xAA, 0x8A, 0x8A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x77, 0x75, 0x75, 0x55, 0x55, 0x55, 0x55, 0x55, 0x51, 0x57, 0x77, 0x75, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0x22, 0xBB, 0xBB, 0xBB, 0xAB, 0xAA, 0xFF, 0xFB, 0xBF, 0x3B, 0x22, 0x22, 0x22, 0x00, 0x80, 0x3F, 0xFF, 0xFF, 0x23, 0x22, 0x22, 0x22, 0x22,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0xAE, 0x80, 0xFE, 0xEE, 0xAA, 0xAA, 0xEA, 0xFE, 0xFF, 0xFF, 0x8F, 0x80, 0xAA, 0x00, 0x00, 0x80, 0x3F, 0xFE, 0xFF, 0xAA, 0x88, 0xA8, 0xAA, 0xAA,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x55, 0x77, 0x77, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x57, 0x57, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0x22, 0xBF, 0xBB, 0xBB, 0xFB, 0xBA, 0xFB, 0xFF, 0xFF, 0x02, 0x22, 0xBA, 0x23, 0x02, 0xA0, 0x3F, 0xFE, 0xFF, 0x2B, 0x22, 0x22, 0x22, 0xAA,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0x0F, 0x88, 0xEE, 0xEE, 0xAE, 0xEA, 0xFE, 0xEF, 0xFF, 0xBF, 0x80, 0xA8, 0xAA, 0x0E, 0x80, 0xAE, 0x7F, 0xFE, 0xFF, 0x8A, 0x8A, 0x8A, 0x8A, 0xAA,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x57, 0x55, 0x77, 0x57, 0x55, 0x55, 0x57, 0x55, 0x55, 0x75, 0x55, 0x55, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0x22, 0xBB, 0xBB, 0x23, 0xFA, 0xFB, 0xFF, 0xFF, 0x3F, 0x22, 0xFF, 0xFF, 0x2A, 0xA0, 0xFF, 0x7F, 0xFE, 0xBF, 0x22, 0x22, 0x22, 0x22, 0xA2,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x5D, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0x2F, 0x80, 0xFE, 0xEE, 0xA3, 0xFA, 0xFE, 0xFE, 0xFF, 0xAF, 0x80, 0xFF, 0xE3, 0xAA, 0xE0, 0xEB, 0xFF, 0xFE, 0xBF, 0xAA, 0xA8, 0xAA, 0xAA, 0xAA,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x57, 0x75, 0x77, 0x57, 0x55, 0x57, 0x55, 0x55, 0x51, 0x55, 0x57, 0x55, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0x22, 0xBF, 0xBB, 0x0B, 0xBF, 0xBF, 0xFF, 0xFF, 0x2B, 0xA0, 0xBF, 0xA3, 0x2B, 0xB0, 0xE3, 0x7F, 0xFA, 0x3F, 0x22, 0x22, 0x22, 0x2A, 0x2A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xDD, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEE, 0x0F, 0x80, 0xAE, 0xFA, 0xEA, 0xEF, 0xBF, 0xFE, 0xFF, 0x0F, 0xA8, 0xAA, 0x8A, 0xAA, 0xA8, 0xFA, 0xFF, 0xFE, 0xAF, 0x8A, 0x8A, 0xAA, 0xAA, 0x0A,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x55, 0x55, 0x75, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55, 0x51, 0x55, 0x57, 0x75, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xBB, 0x2B, 0x22, 0xBA, 0x2B, 0xFE, 0xAB, 0xBF, 0xFA, 0xFF, 0x22, 0x2B, 0xA2, 0x22, 0x2A, 0xA0, 0xBA, 0xFF, 0xFA, 0xBF, 0x22, 0x22, 0xA2, 0x2A, 0x20,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFE, 0xAF, 0x80, 0xFE, 0xAA, 0xFF, 0xFB, 0xBF, 0xFF, 0xFF, 0x80, 0xAA, 0x88, 0x88, 0xAA, 0x80, 0xEA, 0xFF, 0xFC, 0xAF, 0xA8, 0xAA, 0xAA, 0x0A, 0x00,
 0x55, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x57, 0x75, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x51, 0x55, 0x57, 0x75, 0x57, 0x55, 0x55, 0x55, 0x15, 0x55,
 0xBB, 0x2F, 0x22, 0xBF, 0xAB, 0xBF, 0xFB, 0xBF, 0xFB, 0xBF, 0xE2, 0x2B, 0x22, 0x22, 0x2A, 0x20, 0xAA, 0xFF, 0xFA, 0x2F, 0x22, 0x22, 0xAA, 0x02, 0x02,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFF, 0x0F, 0x80, 0xAE, 0xEA, 0xEF, 0xFE, 0xFF, 0xEB, 0x2F, 0xF8, 0xAA, 0x0A, 0x88, 0xAA, 0x80, 0xAA, 0xFF, 0xFA, 0xAF, 0x8A, 0xAA, 0xAA, 0x00, 0x00,
 0x55, 0x57, 0x55, 0x55, 0x55, 0x75, 0x75, 0x77, 0x55, 0x57, 0x75, 0x55, 0x55, 0x55, 0x55, 0x51, 0x55, 0x77, 0x75, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFB, 0x2F, 0x22, 0xBA, 0xAB, 0xFB, 0xFB, 0xFF, 0xBB, 0x2F, 0xFE, 0x2B, 0x22, 0x22, 0xBA, 0x22, 0xAA, 0xFF, 0xFA, 0x23, 0x22, 0x22, 0x2A, 0x00, 0x00,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFF, 0xAF, 0x80, 0xEE, 0xBA, 0xFE, 0xFE, 0xFF, 0xB3, 0x0F, 0xFE, 0xAA, 0x8A, 0xA8, 0xAA, 0x80, 0xEA, 0xFF, 0xF8, 0x8B, 0xAA, 0xAA, 0x0A, 0x00, 0x00,
 0x55, 0x57, 0x55, 0x55, 0x55, 0x55, 0x75, 0x57, 0x75, 0x17, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x57, 0x75, 0x55, 0x55, 0x55, 0x55, 0x15, 0x15,
 0xFF, 0x2F, 0x22, 0xBF, 0xBB, 0xBB, 0xFB, 0xBF, 0xFF, 0x8B, 0xBF, 0x2B, 0x22, 0xA2, 0xAB, 0x22, 0xEA, 0xFF, 0xFB, 0x2B, 0x22, 0x22, 0x0A, 0x02, 0x00,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x45, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFF, 0x2F, 0x88, 0xAE, 0xBE, 0xEF, 0xEF, 0xFF, 0xFF, 0xC8, 0xEF, 0xAA, 0x8A, 0xAA, 0xAA, 0x82, 0xEA, 0xFF, 0xE9, 0x8A, 0x8A, 0xAA, 0x0A, 0x00, 0x00,
 0x55, 0x57, 0x55, 0x55, 0x55, 0x77, 0x75, 0x75, 0x75, 0x55, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x77, 0x75, 0x55, 0x55, 0x55, 0x55, 0x15, 0x11,
 0xFB, 0x2B, 0x22, 0xBA, 0xBB, 0xFB, 0xFB, 0xFF, 0x7F, 0xF0, 0xBF, 0xAB, 0x22, 0xA2, 0xBA, 0x23, 0xFA, 0xFF, 0xFB, 0x22, 0x22, 0xA2, 0x02, 0x00, 0x00,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFF, 0xAF, 0x80, 0xFE, 0xFE, 0xFF, 0xEA, 0xFB, 0xAF, 0xF8, 0xEF, 0xAA, 0xAA, 0xA8, 0xAA, 0xA0, 0xFA, 0xFF, 0xEB, 0xAA, 0xAA, 0xAA, 0x02, 0x00, 0x00,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x15,
 0xBF, 0x2B, 0x22, 0xBE, 0xBA, 0xBF, 0xBB, 0xFB, 0xA2, 0xFE, 0xBF, 0x2B, 0x2A, 0x22, 0x02, 0x20, 0xFE, 0xFB, 0x33, 0x22, 0x22, 0x2A, 0x02, 0x00, 0x00,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFF, 0x0F, 0x88, 0xBE, 0xEE, 0xFF, 0xAF, 0xFE, 0xA8, 0xFF, 0xFF, 0xAA, 0xAA, 0x8A, 0x0A, 0xA0, 0xFE, 0xFF, 0xAB, 0x8A, 0xAA, 0xAA, 0x02, 0x00, 0x00,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x57, 0x55, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x11, 0x11,
 0xFF, 0x2B, 0x22, 0xBE, 0xFF, 0xBF, 0xBB, 0x2F, 0xBA, 0xFB, 0xBF, 0xAB, 0xA2, 0x2A, 0x22, 0xB2, 0xFF, 0xFB, 0x33, 0x22, 0x22, 0xA2, 0x02, 0x00, 0x00,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFF, 0x0F, 0x80, 0xEE, 0xFE, 0xBF, 0xEE, 0x0B, 0xEE, 0xFF, 0xFF, 0xAB, 0xAA, 0xEE, 0xAE, 0xAA, 0xFF, 0xFF, 0xAB, 0xA8, 0xAA, 0xAA, 0x00, 0x00, 0x00,
 0x57, 0x55, 0x55, 0x55, 0x75, 0x55, 0x55, 0x15, 0x75, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x77, 0x55, 0x57, 0x55, 0x55, 0x55, 0x15, 0x15, 0x11,
 0xFF, 0x2B, 0x22, 0xBB, 0xFF, 0xBF, 0xBB, 0xAB, 0xFB, 0xFF, 0xBF, 0xAB, 0x2B, 0xAA, 0x22, 0xE2, 0xFF, 0xFF, 0x23, 0x22, 0x22, 0xAA, 0x02, 0x00, 0x00,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x45, 0x55, 0x55,
 0xFF, 0x0F, 0x80, 0xAE, 0xEE, 0xAF, 0xAF, 0x8A, 0xFE, 0xFF, 0xFF, 0xAF, 0xAA, 0xAA, 0xAA, 0xEA, 0xFF, 0xFF, 0x8F, 0x8A, 0xAA, 0xAA, 0x00, 0x00, 0x00,
 0x77, 0x55, 0x55, 0x55, 0x77, 0x57, 0x55, 0x55, 0x75, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75, 0x77, 0x55, 0x57, 0x55, 0x55, 0x55, 0x11, 0x11, 0x11,
 0xFF, 0x2B, 0x22, 0xBE, 0xFF, 0xAF, 0xBA, 0x2B, 0xFF, 0xFF, 0xFF, 0xBB, 0xAB, 0xAA, 0x2A, 0xFA, 0xFF, 0xFB, 0x23, 0x22, 0x22, 0xA2, 0x00, 0x00, 0x00,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFF, 0xAA, 0x80, 0xFE, 0xFF, 0xEF, 0xFA, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF, 0xAA, 0xAA, 0x88, 0xFE, 0xFF, 0xFF, 0x8F, 0xA8, 0xAA, 0xAA, 0x00, 0x00, 0x00,
 0x77, 0x55, 0x55, 0x55, 0x75, 0x57, 0x55, 0x55, 0x57, 0x77, 0x77, 0x57, 0x55, 0x55, 0x55, 0x75, 0x57, 0x55, 0x57, 0x55, 0x55, 0x55, 0x11, 0x11, 0x15,
 0xFF, 0x2B, 0x22, 0xBE, 0xFB, 0xEF, 0xAB, 0xBA, 0xFF, 0xFF, 0xFF, 0xFF, 0x2B, 0x22, 0x22, 0xFE, 0xFF, 0xBB, 0x27, 0x22, 0x22, 0xAA, 0x00, 0x00, 0x00,
 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x45, 0x55, 0x55,
 0xFF, 0x0F, 0x80, 0xEE, 0xFF, 0xAF, 0xAF, 0xEA, 0xFF, 0xFF, 0xFF, 0xFF, 0xBF, 0x8A, 0x88, 0xFE, 0xFF, 0xFF, 0x8F, 0xAA, 0xAA, 0xAA, 0x00, 0x00, 0xE8,
 0x77, 0x55, 0x55, 0x55, 0x75, 0x57, 0x55, 0x55, 0x57, 0x77, 0x77, 0x55, 0x55, 0x55, 0x55, 0x75, 0x75, 0x55, 0x55, 0x55, 0x55, 0x55, 0x11, 0x51, 0x75,
 0xFF, 0x2B, 0x22, 0xB3, 0xFB, 0xBF, 0xBF, 0xBB, 0xBF, 0xFF, 0xFF, 0xBF, 0xAB, 0x22, 0x22, 0xFA, 0xFF, 0xBB, 0xA3, 0xBB, 0x22, 0x3A, 0x00, 0x00, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xFF, 0xAF, 0x80, 0xFC, 0xFF, 0xBE, 0xEE, 0xBA, 0xFE, 0xFF, 0xFF, 0xFF, 0xAA, 0xAA, 0x0A, 0x80, 0xFF, 0xFF, 0xAF, 0xAA, 0xAA, 0x2A, 0x00, 0x80, 0xFF,
 0x75, 0x57, 0x55, 0x55, 0x57, 0x55, 0x55, 0x55, 0x55, 0x77, 0x77, 0x57, 0x55, 0x55, 0x55, 0x15, 0x75, 0x55, 0x57, 0x55, 0x55, 0x55, 0x15, 0x55, 0x57,
 0xFB, 0x3F, 0x02, 0xEF, 0xFF, 0xBF, 0xAF, 0xBB, 0xBB, 0xFF, 0xFF, 0xBF, 0x2B, 0x2A, 0x22, 0x00, 0xF8, 0xBF, 0x23, 0xAA, 0xBB, 0x3B, 0x00, 0xF0, 0xBF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xEA, 0x2F, 0x88, 0xEA, 0xEF, 0xFE, 0xFB, 0xEA, 0xFE, 0xFF, 0xFF, 0xFF, 0xAA, 0xAA, 0x0A, 0x00, 0xC0, 0xFF, 0xAB, 0xAA, 0xAA, 0x6B, 0x00, 0xFE, 0xEF,
 0x55, 0x57, 0x55, 0x75, 0x57, 0x55, 0x55, 0x55, 0x75, 0x75, 0x75, 0x55, 0x55, 0x55, 0x55, 0x55, 0x51, 0x55, 0x57, 0x55, 0x55, 0x55, 0x11, 0x55, 0x55,
 0xA2, 0x3F, 0x22, 0xF8, 0xBF, 0xFF, 0xFF, 0xAB, 0xEB, 0xFF, 0xFF, 0xBB, 0xBB, 0x22, 0x22, 0x02, 0x00, 0xFF, 0x23, 0xA2, 0xAA, 0x3B, 0x00, 0xBE, 0xBB,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0x8B, 0xAE, 0x80, 0xFE, 0xFF, 0xFF, 0xFF, 0xAE, 0xFA, 0xFF, 0xFF, 0xFB, 0xAA, 0xAA, 0x08, 0x00, 0x00, 0xFE, 0xAB, 0xAA, 0xAA, 0x2A, 0x80, 0xFE, 0xFA,
 0x55, 0x55, 0x55, 0x75, 0x77, 0x75, 0x57, 0x55, 0x55, 0x77, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x55, 0x55, 0x55, 0x55, 0x55, 0x51, 0x55, 0x55,
 0x2B, 0x3E, 0x22, 0xFA, 0xFF, 0xFF, 0xBB, 0x3B, 0xAE, 0xFF, 0xFF, 0xBB, 0x2B, 0x2A, 0x22, 0x02, 0x00, 0xF8, 0x2B, 0x2A, 0xAA, 0x23, 0x80, 0xBB, 0xBB,
 0x55, 0x5D, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0x0F, 0x2E, 0x88, 0xFA, 0xFF, 0xFE, 0xFF, 0x8E, 0xCE, 0xFF, 0xFF, 0xFE, 0xAA, 0xAA, 0x0A, 0x08, 0x00, 0xF0, 0x8B, 0xAA, 0xAA, 0x02, 0x80, 0xAF, 0xEF,
 0x57, 0x55, 0x55, 0x75, 0x77, 0x75, 0x57, 0x55, 0x55, 0x75, 0x75, 0x75, 0x55, 0x55, 0x55, 0x55, 0x11, 0x71, 0x55, 0x55, 0x55, 0x15, 0x51, 0x55, 0x55,
 0x2F, 0x3E, 0x22, 0xFA, 0xFF, 0xFE, 0xFF, 0x3B, 0xAE, 0xFF, 0xFF, 0xBB, 0xAA, 0x2A, 0x22, 0x22, 0x00, 0xE0, 0x23, 0xA2, 0xAA, 0x00, 0xA0, 0xBB, 0xBB,
 0x55, 0x55, 0x55, 0x5D, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0x8F, 0xBE, 0x80, 0xFE, 0xFF, 0xFE, 0xFF, 0xAE, 0xEA, 0xFF, 0xFF, 0xFA, 0xAA, 0xAA, 0xAA, 0x00, 0x00, 0xE0, 0xAB, 0xAA, 0xAA, 0x00, 0xE8, 0xEE, 0xEA,
 0x57, 0x55, 0x55, 0x75, 0x57, 0x55, 0x57, 0x55, 0x55, 0x57, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x51, 0x55, 0x55, 0x55, 0x11, 0x55, 0x55, 0x55,
 0x2F, 0x3E, 0x22, 0xFA, 0xFF, 0xFF, 0xBF, 0xAB, 0xAB, 0xFF, 0xBF, 0xBB, 0x2B, 0x2A, 0x22, 0x22, 0x02, 0x80, 0x2B, 0x2A, 0x2A, 0x00, 0xA8, 0xBB, 0xBB,
 0x57, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xC5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0x0F, 0x2E, 0x80, 0xFE, 0xFF, 0xFF, 0xFF, 0xAF, 0xFE, 0xFF, 0xAF, 0xBA, 0xAA, 0xAA, 0x8A, 0x08, 0x00, 0x80, 0xAA, 0xAA, 0x2A, 0x00, 0xA8, 0xEF, 0xEE,
 0x57, 0x55, 0x55, 0x75, 0x77, 0x75, 0x57, 0x55, 0x75, 0x57, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x51, 0x55, 0x55, 0x55, 0x15, 0x55, 0x55, 0x55,
 0x3F, 0x3E, 0x22, 0xFA, 0xFB, 0xFF, 0xFF, 0xAB, 0xFB, 0xFF, 0xBF, 0xBA, 0xA2, 0xAA, 0x22, 0x22, 0x00, 0x80, 0x2B, 0xA2, 0xAA, 0x02, 0xBA, 0xBB, 0xBB,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0xAF, 0xBE, 0x80, 0xFE, 0xFF, 0xFF, 0xFF, 0xBE, 0xEF, 0xFF, 0xAF, 0xAE, 0xAA, 0xAA, 0xAA, 0x08, 0x00, 0x00, 0xAE, 0xAA, 0xAA, 0x02, 0xEA, 0xEE, 0xEE,
 0x57, 0x55, 0x55, 0x75, 0x77, 0x77, 0x57, 0x55, 0x75, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x11, 0x55, 0x55, 0x55, 0x15, 0x55, 0x55, 0x55,
 0x3F, 0x3A, 0x22, 0xFA, 0xFF, 0xFF, 0xBF, 0xFF, 0xFB, 0xFF, 0xBB, 0x2F, 0x2A, 0x2A, 0x2A, 0x22, 0x02, 0x00, 0xBE, 0xBB, 0xA3, 0x02, 0xBA, 0xBB, 0xFB,
 0x5F, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x45, 0x55, 0x55, 0x55,
 0x3F, 0xBA, 0x88, 0xFE, 0xFF, 0xFF, 0xFF, 0xEA, 0xFF, 0xFF, 0xAB, 0xAF, 0xAA, 0xAA, 0xAA, 0x0A, 0x00, 0x00, 0xEE, 0xFF, 0x0F, 0x80, 0xFF, 0xEF, 0xFF,
 0x57, 0x55, 0x55, 0x75, 0x77, 0x77, 0x77, 0x55, 0x75, 0x77, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x11, 0x55, 0x75, 0x57, 0x51, 0x55, 0x55, 0x75,
 0x3B, 0x3A, 0x22, 0xFB, 0xFF, 0xFF, 0xFF, 0xA3, 0xFF, 0xFF, 0xFB, 0xAB, 0x22, 0xA2, 0x22, 0x22, 0x02, 0x00, 0xBA, 0xFB, 0x0F, 0xA0, 0xFF, 0xBB, 0xFB,
 0x55, 0x55, 0x55, 0x55, 0x55, 0xD5, 0x55, 0x55, 0xD5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0x2F, 0xB8, 0x80, 0xFA, 0xFF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0xFA, 0xAA, 0xAA, 0xAA, 0xAA, 0x8A, 0x00, 0x00, 0xE8, 0xFE, 0x0F, 0xE0, 0xFF, 0xFE, 0xFE,
 0x55, 0x55, 0x55, 0x75, 0x57, 0x77, 0x77, 0x55, 0x77, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x55, 0x55, 0x15, 0x55, 0x55, 0x55, 0x55,
 0x2B, 0x3A, 0x02, 0xFB, 0xFF, 0xBF, 0xFF, 0xBF, 0xFF, 0xBF, 0xBF, 0x2B, 0x2A, 0x2A, 0x2A, 0x22, 0x02, 0x00, 0xB8, 0xBB, 0x0B, 0xFA, 0xBF, 0xBF, 0xBB,
 0x55, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0x2F, 0xA8, 0x00, 0xFE, 0xFF, 0xFF, 0xEE, 0xAE, 0xFF, 0xFF, 0xAF, 0xAA, 0xAA, 0xAA, 0xAA, 0x8A, 0x08, 0x00, 0xF8, 0xEE, 0x8E, 0xFF, 0xEF, 0xAF, 0xFE,
 0x57, 0x55, 0x55, 0x75, 0x55, 0x77, 0x55, 0x55, 0x77, 0x57, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x51, 0x55, 0x55, 0x55, 0x55, 0x55, 0x75,
 0x3F, 0x3A, 0x22, 0xFE, 0xBF, 0xBB, 0xFF, 0xBB, 0xFF, 0xBB, 0xBB, 0xAA, 0x22, 0x22, 0x22, 0x22, 0x22, 0x00, 0xB0, 0xBB, 0xBB, 0xBB, 0xFB, 0xBB, 0xFF,
 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xDD, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 0x3F, 0xA8, 0x80, 0xFE, 0xFF, 0xFB, 0xFB, 0xFF, 0xFF, 0xAF, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0x00, 0x00, 0xE0, 0xAA, 0xEA, 0xEA, 0xFE, 0xEA, 0xFF,
 0x57, 0x55, 0x55, 0x75, 0x57, 0x77, 0x55, 0x57, 0x75, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x15, 0x51, 0x55, 0x55, 0x55, 0x55, 0x55, 0x57,
 0x3F, 0x20, 0x02, 0xFF, 0xFF, 0xBB, 0xBB, 0xFF, 0xBB, 0xBB, 0xAB, 0x2A, 0x2A, 0x2A, 0x22, 0x22, 0x22, 0x02, 0xA0, 0xBB, 0xBB, 0xBA, 0xBB, 0xBB, 0xBF,
 0x55, 0x55, 0x55, 0x5D, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
 };
