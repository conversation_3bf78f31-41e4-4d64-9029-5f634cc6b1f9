#pragma once

#include <cinttypes>


class BytestreamReader{
private:
    uint8_t *m_ptr{nullptr};

public:
    BytestreamReader(uint8_t *data): m_ptr(data) {}

    uint8_t* current_pos() const {
        return m_ptr;
    }

    uint32_t read32be(){
        uint32_t ret = (m_ptr[0] << 24) | (m_ptr[1] << 16) | (m_ptr[2] << 8) | m_ptr[3];
        m_ptr += 4;
        return ret;
    }

    uint32_t read32le(){
        uint32_t ret = (m_ptr[3] << 24) | (m_ptr[2] << 16) | (m_ptr[1] << 8) | m_ptr[0];
        m_ptr += 4;
        return ret;
    }

    uint16_t read16be(){
        uint16_t ret = (m_ptr[0] << 8) | m_ptr[1];
        m_ptr += 2;
        return ret;
    }

    uint16_t read16le(){
        uint16_t ret = (m_ptr[1] << 8) | m_ptr[0];
        m_ptr += 2;
        return ret;
    }

    uint8_t read8(){
        uint8_t ret = m_ptr[0];
        ++m_ptr;
        return ret;
    }

    void skip8(){
        ++m_ptr;
    }
};
