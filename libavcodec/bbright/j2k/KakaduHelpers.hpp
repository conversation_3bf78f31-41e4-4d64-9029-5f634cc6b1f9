#pragma once

extern "C"{
    #include "libavcodec/packet.h"
}

#include <cinttypes>
#include <iostream>
#include <cstring>

#define KDU_X86_INTRINSICS
#include <kdu_stripe_decompressor.h>

#include "j2k_elsm_header.hpp"



namespace bbright{

class AVPacketKakaduInterface : public kdu_supp::kdu_compressed_source  // TODO: can probably be improved
{
public:
    bool use_packet(AVPacket &pkt, bool interlaced=false, bool extended_capabilities=false, bool striped=false){
        std::array<uint8_t, j2k_elsm_header::max_size> header_buffer;
        std::memcpy(&header_buffer[0], pkt.data, header_buffer.size());  // TODO: don't use tmp array, directly parse memory

        if( m_header.parse(header_buffer, interlaced, extended_capabilities, striped) != j2k_elsm_header::parse_error::ok ){
            return true;
        }

        m_end = pkt.data + pkt.size;
        m_pos = pkt.data + m_header.size();

        // check if next bytes are startcode of raw j2k codestream
        if( (m_pos[0] != 0xff)  || (m_pos[1] != 0x4f) ){
            return true;
        }

        return false;
    }

    bool next_stripe(){
        const int size = m_end - m_pos;
        const auto found = find_next_codestream(m_pos+1, size);
        if( found == nullptr ){
            return true;
        }
        m_pos = found;
        return false;
    }

private:
    uint8_t* find_next_codestream(uint8_t *begin, size_t size){
        uint8_t *ptr     = begin;
        uint8_t *end_ptr = begin + size - 3;
        for(; ptr<end_ptr; ++ptr){
            // ffd9 ->   end of codestream
            // ff4f -> start of codestream
            if( ptr[0] == 0xff  &&  ptr[1] == 0xd9  &&  ptr[2] == 0xff  &&  ptr[3] == 0x4f )
                return ptr+2;
        }
        return nullptr;
    }

    bool close() override {
        return true;
    }

    int get_capabilities() override {
        return KDU_SOURCE_CAP_IN_MEMORY | KDU_SOURCE_CAP_SEQUENTIAL;// | KDU_SOURCE_CAP_SEEKABLE;  // TODO: KDU_SOURCE_CAP_IN_MEMORY ??  See the description of `kdu_compressed_source::get_capabilities' for an explanation of these constants.
    }

    kdu_core::kdu_membroker *get_membroker() override {
        return NULL;
    }

    int read(kdu_supp::kdu_byte *buf, int num_bytes) override {
        if( m_pos+num_bytes <= m_end ){
            memcpy(buf, m_pos, num_bytes);
            m_pos += num_bytes;
            return num_bytes;
        } else {
            memcpy(buf, m_pos, m_end - m_pos);
            m_pos = m_end;
            return m_end - m_pos;
        }
    }

    bool seek(kdu_core::kdu_long offset) override {
        (void)offset;
        return false;
    }

    kdu_core::kdu_byte *access_memory(kdu_core::kdu_long &pos, kdu_core::kdu_byte * &lim) override {
        pos = get_pos();
        lim = m_end;
        return m_pos;
    }

    kdu_core::kdu_long get_pos() override {
        return m_pos - m_startcode_ptr;
    }

    bool set_tileheader_scope(int tnum, int num_tiles) override {
        (void)tnum;
        (void)num_tiles;
        return false;
    }

    bool set_precinct_scope(kdu_core::kdu_long unique_id) override {
        (void)unique_id;
        return false;
    }

//    virtual bool write(const kdu_supp::kdu_byte *buf, int num_bytes) {
//        (void)buf;
//        (void)num_bytes;
//        return false;
//    }

private:
    j2k_elsm_header  m_header;
    uint8_t         *m_startcode_ptr{nullptr};
    uint8_t         *m_pos{nullptr};
    uint8_t         *m_end{nullptr};
};

}
