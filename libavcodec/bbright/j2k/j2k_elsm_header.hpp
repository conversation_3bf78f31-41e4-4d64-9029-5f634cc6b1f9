#pragma once

extern "C" {
    #include "libavutil/rational.h"
}

#include <cinttypes>
#include <array>

#include "BytestreamReader.hpp"


class j2k_elsm_header{
private:
    // noncopyable
    j2k_elsm_header(const j2k_elsm_header&) = delete;
    j2k_elsm_header& operator=(const j2k_elsm_header&) = delete;

    static constexpr uint32_t elsm_box_code{ 0x656c'736d };
    static constexpr uint32_t frat_box_code{ 0x6672'6174 };
    static constexpr uint32_t brat_box_code{ 0x6272'6174 };
    static constexpr uint32_t fiel_box_code{ 0x6669'656c };
    static constexpr uint32_t tcod_box_code{ 0x7463'6f64 };
    static constexpr uint32_t strp_box_code{ 0x7374'7270 };
    static constexpr uint32_t bcol_box_code{ 0x6263'6f6c };

    uint16_t m_frat_denominator{1};
    uint16_t m_frat_numerator{1};
    uint32_t m_brat_max_br{0};
    uint32_t m_brat_auf1{0};
    uint32_t m_brat_auf2{0};
    uint8_t  m_fiel_fic{0};
    uint8_t  m_fiel_fio{0};
    uint8_t  m_tcod_hh{0};
    uint8_t  m_tcod_mm{0};
    uint8_t  m_tcod_ss{0};
    uint8_t  m_tcod_ff{0};
    uint8_t  m_strp_max_idx{0};
    uint16_t m_frame_vertical_size{0};
    uint8_t  m_bcol_colcr{0};
    uint8_t  m_color_primaries{0};
    uint8_t  m_transfer_caracteristics{0};
    uint8_t  m_matrix_coefficients{0};
    bool     m_video_full_range_flag{0};

    uint8_t  m_header_size{0};
    uint8_t *m_frame_start_ptr{nullptr};


public:
    j2k_elsm_header() = default;

    static constexpr uint8_t max_size{ 48 };  // maximum 48 bytes needed for a complete header

    enum class parse_error{
        ok,
        bad_elsm_box_code,
        bad_frat_box_code,
        bad_brat_box_code,
        bad_fiel_box_code,
        bad_tcod_box_code,
        bad_strp_box_code,
        bad_bcol_box_code,
    };

    parse_error parse(std::array<uint8_t, max_size> &data, bool interlaced, bool extended_capabilities, bool striped){

        BytestreamReader bs( &data[0] );

        if( bs.read32be() != elsm_box_code ){
            return parse_error::bad_elsm_box_code;
        }

        if( bs.read32be() != frat_box_code ){
            return parse_error::bad_frat_box_code;
        }

        m_frat_denominator = bs.read16be();
        m_frat_numerator   = bs.read16be();

        if( bs.read32be() != brat_box_code ){
            return parse_error::bad_brat_box_code;
        }

        m_brat_max_br = bs.read32be();
        m_brat_auf1   = bs.read32be();

        if( interlaced ){
            m_brat_auf2 = bs.read32be();

            if( bs.read32be() != fiel_box_code ){
                return parse_error::bad_fiel_box_code;
            }

            m_fiel_fic = bs.read8();
            m_fiel_fio = bs.read8();
        }

        if( !extended_capabilities  ||  !striped ){
            if( bs.read32be() != tcod_box_code ){
                return parse_error::bad_tcod_box_code;
            }

            m_tcod_hh = bs.read8();
            m_tcod_mm = bs.read8();
            m_tcod_ss = bs.read8();
            m_tcod_ff = bs.read8();
        }else{
            if( bs.read32be() != strp_box_code ){
                return parse_error::bad_strp_box_code;
            }

            m_strp_max_idx        = bs.read8();
            m_frame_vertical_size = bs.read16be();
            bs.skip8();  // reserved
        }

        if( !extended_capabilities ){
            if( bs.read32be() != bcol_box_code ){
                return parse_error::bad_bcol_box_code;
            }

            m_bcol_colcr = bs.read8();
            bs.skip8();  // reserved
        }else{
            m_color_primaries         = bs.read8();
            m_transfer_caracteristics = bs.read8();
            m_matrix_coefficients     = bs.read8();
            m_video_full_range_flag   = bs.read8() >> 7; // 7 reserved bits
            bs.skip8();  // reserved
            bs.skip8();  // reserved
        }

        m_header_size = bs.current_pos() - &data[0];

        return parse_error::ok;
    }

    AVRational framerate() const {
        return {m_frat_numerator, m_frat_denominator};
    }

    uint8_t size() const {
        return m_header_size;
    }

    uint32_t frame_size() const {
        return m_brat_auf1;
    }
};
