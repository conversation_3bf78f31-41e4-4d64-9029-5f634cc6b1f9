#ifndef _LIB_MISC_LINUX_H_
#define _LIB_MISC_LINUX_H_

#define _LIB_MISC_VERSION(a, b, c) (((a) << 16) + ((b) << 8) + (c))
#define _LIB_MISC_LINUX_H_VERSION  _LIB_MISC_VERSION(0, 0, 2)

#ifdef __linux__

#define __FUNCTION__MACRO__WRAPPER__ __func__

#define SNPRINTF snprintf

#define SLEEP_SECOND(x)                     sleep(x)
#define SLEEP_MICROSECOND(x)                usleep(x)
#define SLEEP_MICROSECOND_RETURN(x, result) result = SLEEP_MICROSECOND(x)

#define STRTOULL strtoull
#define STRTOLL  strtoll

#define STRTOK(str, sep, tok) tok = strtok(str, sep)

#define STRCPY_SAFE(dst, buf_size, src) SNPRINTF(dst, buf_size, "%s", src)

#define GETOPT_CONSTRUCTOR(opt_name, opt_has_arg, opt_flag, opt_val) \
	{ \
		.name		= opt_name, \
		.has_arg	= opt_has_arg, \
		.flag		= opt_flag, \
		.val		= opt_val \
	}

#define GETTIMEOFDAY gettimeofday

#define UTSNAME                       struct utsname
#define UNAME(ptr_to_utsname, result) result = uname(ptr_to_utsname)

#define ROUND round

#define UNUSED_ARGUMENT __attribute__((unused))

#define UNUSED(x) (void)(x)

#endif //__linux__

#endif //_LIB_MISC_LINUX_H_
