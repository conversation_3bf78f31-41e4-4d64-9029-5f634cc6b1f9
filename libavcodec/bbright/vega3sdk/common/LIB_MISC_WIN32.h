#ifndef _LIB_MISC_WIN32_H_
#define _LIB_MISC_WIN32_H_

#define _LIB_MISC_VERSION(a,b,c) (((a) << 16) + ((b) << 8) + (c))
#define _LIB_MISC_WIN32_H_VERSION _LIB_MISC_VERSION(0,0,2)

#ifdef _WIN32

#define __FUNCTION__MACRO__WRAPPER__ __FUNCTION__

#define SNPRINTF(dst, sizeOfBuffer, format, ...) _snprintf_s(dst, sizeOfBuffer, sizeOfBuffer-1, format, __VA_ARGS__)

#define SLEEP_SECOND(x) Sleep(x*1000)
#define SLEEP_MICROSECOND(x) Sleep(x/1000)
#define SLEEP_MICROSECOND_RETURN(x, result) \
	{ \
		Sleep(x/1000); \
		result = 0; \
	}

#define STRTOULL _strtoui64
#define STRTOLL _strtoi64
#define STRTOU32 strtoul

//STRTOK - no thread-safe
#define STRTOK(str, sep, tok) \
	{ \
		static char *next_token = NULL; \
		tok = strtok_s( str, sep, &next_token); \
	}

#define STRCPY_SAFE(dst, buf_size, src) SNPRINTF(dst, buf_size, "%s", src)
#define STRNCPY_SAFE(dst, buf_size, src, count) strncpy_s(dst, buf_size, src, count)

#define GETOPT_CONSTRUCTOR(opt_name, opt_has_arg, opt_flag, opt_val) \
	{ \
		opt_name, \
		opt_has_arg, \
		opt_flag, \
		opt_val \
	}

#define GETTIMEOFDAY(tp, tzp) \
{ \
	UNREFERENCED_PARAMETER(tzp); \
	time_t clock; \
	struct tm tm; \
	SYSTEMTIME wtm; \
	GetLocalTime(&wtm); \
	tm.tm_year     = wtm.wYear - 1900; \
	tm.tm_mon     = wtm.wMonth - 1; \
	tm.tm_mday     = wtm.wDay; \
	tm.tm_hour     = wtm.wHour; \
	tm.tm_min     = wtm.wMinute; \
	tm.tm_sec     = wtm.wSecond; \
	tm. tm_isdst    = -1; \
	clock = mktime(&tm); \
	(tp)->tv_sec = (long)clock; \
	(tp)->tv_usec = wtm.wMilliseconds * 1000; \
}

#define LOCALTIME_SAFE(timeIn, timeOut, result) result = localtime_s(&timeOut, &timeIn)

#define _UTSNAME_LENGTH 256

# ifndef _UTSNAME_NODENAME_LENGTH
#  define _UTSNAME_NODENAME_LENGTH _UTSNAME_LENGTH
# endif
# ifndef _UTSNAME_SYSNAME_LENGTH
#  define _UTSNAME_SYSNAME_LENGTH _UTSNAME_LENGTH
# endif
# ifndef _UTSNAME_RELEASE_LENGTH
#  define _UTSNAME_RELEASE_LENGTH _UTSNAME_LENGTH
# endif
# ifndef _UTSNAME_VERSION_LENGTH
#  define _UTSNAME_VERSION_LENGTH _UTSNAME_LENGTH
# endif
# ifndef _UTSNAME_MACHINE_LENGTH
#  define _UTSNAME_MACHINE_LENGTH _UTSNAME_LENGTH
# endif

/* Structure describing the system and machine.  */
struct utsname
  {
    /* Name of this node on the network.  */
    char nodename[_UTSNAME_NODENAME_LENGTH];

    /* Name of the implementation of the operating system.  */
    char sysname[_UTSNAME_SYSNAME_LENGTH];
    /* Current release level of this implementation.  */
    char release[_UTSNAME_RELEASE_LENGTH];
    /* Current version level of this release.  */
    char version[_UTSNAME_VERSION_LENGTH];

    /* Name of the hardware type the system is running on.  */
    char machine[_UTSNAME_MACHINE_LENGTH];
};

#define UTSNAME struct utsname
#define UNAME(ptr_to_utsname, result) \
	{ \
		__pragma(warning(disable : 4996)); \
		DWORD dwSizeOfRelease = _UTSNAME_RELEASE_LENGTH; \
		DWORD dwVer = GetVersion(); \
		DWORD dwMajor = (DWORD)(LOBYTE(LOWORD(dwVer))); \
		DWORD dwMinor = (DWORD)(HIBYTE(LOWORD(dwVer))); \
		DWORD dwBuild = GetVersion() < 0x80000000 ? (DWORD)(HIWORD(GetVersion())) : 0; \
		SNPRINTF((char*)((ptr_to_utsname)->release), dwSizeOfRelease, "%u.%u (%u)", dwMajor, dwMinor, dwBuild); \
		DWORD dwSizeOfMachine = _UTSNAME_MACHINE_LENGTH; \
		BOOL bRet = GetComputerName((PCHAR)((ptr_to_utsname)->machine), &dwSizeOfMachine); \
		if (!bRet) \
		{ \
			result = -EACCES; \
		} \
	}


#define ROUND(x) (static_cast<int>((x) + 0.5))

#define UNUSED_ARGUMENT

#define UNUSED(P)          (P)

#endif //_WIN32

#endif //_LIB_MISC_WIN32_H_

