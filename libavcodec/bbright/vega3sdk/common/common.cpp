/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <ctype.h>
#include <stdarg.h>
#include <stdlib.h>

extern "C" {
#include "libavutil/log.h"
#include "libavutil/avstring.h"
}

#include "../common/common.h"
#include "../vegaff.h"

#ifdef __linux__
#include <sys/time.h>
#include "../common/LIB_MISC_LINUX.h"
#elif _WIN32
#include <time.h>
#include <Windows.h>
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#endif

int64_t vegaff_mdate(void)
{
	struct timeval tv_date;
	GETTIMEOFDAY(&tv_date, NULL);
	return (int64_t)tv_date.tv_sec * 1000000 + (int64_t)tv_date.tv_usec;
}

#define VEGA_BQB_AVC_ALIGNBYTES 32

void *vegaff_zalloc(size_t size)
{
	void *ptr;
	ptr = vegaff_malloc(size);
	if (ptr)
		memset(ptr, 0, size);
	return ptr;
}

#ifdef _WIN32

void *vegaff_malloc(size_t size)
{
	return _aligned_malloc(size, VEGA_BQB_AVC_ALIGNBYTES);
}

void vegaff_free(void *ptr)
{
	if (ptr)
	{
		_aligned_free(ptr);
		ptr = NULL;
	}
}

#else

void *vegaff_malloc(size_t size)
{
	int   ret;
	void *ptr = NULL;

	if (!size)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Null lengt allocate\n");
		return NULL;
	}

	ret = posix_memalign(&ptr, VEGA_BQB_AVC_ALIGNBYTES, size);
	if (ret != 0)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Alloc failed (%d)\n", ret);
		return NULL;
	}

	return ptr;
}

void vegaff_free(void *ptr)
{
	if (ptr)
	{
		free(ptr);
	}
}

#endif

int vegaff_atoi(const char *str, bool &bError)
{
	char *end;
	int   v = strtol(str, &end, 0);
	if ((end != NULL) && (end == str || *end != '\0'))
		bError = true;

	return v;
}

double vegaff_atof(const char *str, bool &bError)
{
	char * end;
	double v = strtod(str, &end);

	if ((end != NULL) && (end == str || *end != '\0'))
		bError = true;

	return v;
}

uint32_t vegaff_str_to_weighted_size(const char *srcstr, int *is_synerr)
{
	int    has_err = 0;
	char * endptr = NULL;
	double value = 0;

	if (is_synerr)
		*is_synerr = 0;

	value = strtod(srcstr, &endptr);
	if (endptr == srcstr)
	{
		if (is_synerr)
			*is_synerr = 1;
		return 0;
	}

	if (endptr != NULL)
	{
		switch (*endptr)
		{
		case 'k':
		case 'K':
			value = value * 1024.0;
			break;
		case 'm':
		case 'M':
			value = value * 1024.0 * 1024.0;
			break;
		case 'g':
		case 'G':
			value = value * 1024.0 * 1024.0 * 1024.0;
			break;
		case 0:
			break;
		default:
			if (iscntrl(*endptr) == 0)
				has_err = 1;
			break;
		}
	}
	*is_synerr = has_err;
	return (uint32_t)(value);
}

int vegaff_atobool(const char *str, bool &bError)
{
	if (!av_strcasecmp(str, "1") || !av_strcasecmp(str, "true") || !av_strcasecmp(str, "yes"))
		return 1;

	if (!av_strcasecmp(str, "0") || !av_strcasecmp(str, "false") || !av_strcasecmp(str, "no"))
		return 0;

	bError = true;
	return 0;
}

int vegaff_parseName(const char *arg, const char *const *names, bool &bError)
{
	for (int i = 0; names[i]; i++)
		if (!av_strcasecmp(arg, names[i]))
			return i;

	return vegaff_atoi(arg, bError);
}


#ifdef IDEBUILD
void vegaff_log(const void *param, int level, const char *fmt, ...)
{
//	if (param && level > param->logLevel)
//		return;
	const char *log_level;

	switch (level)
	{
	case VEGA_BQB_LOG_ERROR:
		log_level = "error";
		break;

	case VEGA_BQB_LOG_WARNING:
		log_level = "warning";
		break;

	case VEGA_BQB_LOG_INFO:
		log_level = "info";
		break;

	case VEGA_BQB_LOG_VERBOSE:
		log_level = "frame";
		break;

	case VEGA_BQB_LOG_DEBUG:
		log_level = "debug";
		break;

	case VEGA_BQB_LOG_FULL:
		log_level = "full";
		break;

	default:
		log_level = "unknown";
		break;
	}

	va_list arg;
	va_start(arg, fmt);
	vfprintf(stderr, fmt, arg);
	va_end(arg);
}
#else

extern "C" void av_vlog(void *avcl, int level, const char *fmt, va_list vl);

void vegaff_log(const void *param, int level, const char *fmt, ...)
{
	va_list arg;
	va_start(arg, fmt);
	av_vlog(NULL, level, fmt, arg);
	va_end(arg);
}
#endif

vegaff_picture_t *vega_bqb_picture_alloc()
{
	return (vegaff_picture_t *)vegaff_malloc(sizeof(vegaff_picture_t));
}

void vega_bqb_picture_init(vegaff_codec_param *p, vegaff_picture_t *pic)
{
	memset(pic, 0, sizeof(vegaff_picture_t));
	int colorSpace = p->internalCsp;
	int bitDepth = p->internalBitDepth;
	pic->bitDepth = bitDepth;

	if (colorSpace == VEGA_BQB_CSP_NV12)
		pic->imgFormat = (int)API_VEGA_BQB_IMAGE_FORMAT_NV12;
	else if (colorSpace == VEGA_BQB_CSP_NV16)
		pic->imgFormat = (int)API_VEGA_BQB_IMAGE_FORMAT_NV16;
	else if (colorSpace == VEGA_BQB_CSP_I420 && bitDepth == 8)
		pic->imgFormat = (int)API_VEGA_BQB_IMAGE_FORMAT_I420;
	else if (colorSpace == VEGA_BQB_CSP_I420 && bitDepth == 10)
		pic->imgFormat = (int)API_VEGA_BQB_IMAGE_FORMAT_I0AL;
	else if (colorSpace == VEGA_BQB_CSP_I422 && bitDepth == 10)
		pic->imgFormat = (int)API_VEGA_BQB_IMAGE_FORMAT_I2AL;
}




