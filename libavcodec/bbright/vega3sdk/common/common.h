/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#pragma once

#include <stddef.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <ctype.h>
#include <climits>

#ifdef _WIN32
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#else
#include "../common/LIB_MISC_LINUX.h"
#endif

#include <libvega_bqb_api/VEGA_BQB_types.h>
#include <libvega_bqb_api/VEGA_BQB_encoder.h>
#include <libvega_bqb_api/VEGA_BQB_decoder.h>
#include <libvega_bqb_api/apiVDEC_types.h>
#include <libvega_bqb_api/apiVDEC.h>

#include "ts_extent.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifndef HAS_AdvancedFeature
#if defined(__linux__)
#define HAS_AdvancedFeature (1)
#else
#define HAS_AdvancedFeature (0)
#endif
#endif

#define API_ES_READ_FRAME_SIZE 0x1518000 // 22118400

#define FFMPEG_NOPTS_VALUE ((int64_t)UINT64_C(0x8000000000000000))
#define STD_MIN(a, b)      ((a) > (b) ? (b) : (a))
#define STD_MAX(a, b)      ((a) < (b) ? (b) : (a))
#ifdef _MSC_VER
#define STR_TOK(x, y, z) strtok_s(x, y, z)
#else
#define STR_TOK(x, y, z) strtok_r(x, y, z)
#endif

#define CHECKED_MALLOC(var, type, count)                                                                               \
	{                                                                                                                  \
		var = (type *)vegaff_malloc(sizeof(type) * (count));                                                           \
		if (!var)                                                                                                      \
		{                                                                                                              \
			vegaff_log((vega_bqb_avc_param *)NULL, VEGA_BQB_LOG_ERROR, "malloc of size %d failed\n",                   \
					   sizeof(type) * (count));                                                                        \
			goto fail;                                                                                                 \
		}                                                                                                              \
	}
#define CHECKED_MALLOC_ZERO(var, type, count)                                                                          \
	{                                                                                                                  \
		var = (type *)vegaff_malloc(sizeof(type) * (count));                                                           \
		if (var)                                                                                                       \
			memset((void *)var, 0, sizeof(type) * (count));                                                            \
		else                                                                                                           \
		{                                                                                                              \
			vegaff_log((vega_bqb_avc_param *)NULL, VEGA_BQB_LOG_ERROR, "malloc of size %d failed\n",                   \
					   sizeof(type) * (count));                                                                        \
			goto fail;                                                                                                 \
		}                                                                                                              \
	}

#define MIN_LOG2_CU_SIZE 3                       // log2(minCUSize)
#define MAX_LOG2_CU_SIZE 6                       // log2(maxCUSize)
#define MIN_CU_SIZE      (1 << MIN_LOG2_CU_SIZE) // minimum allowable size of CU
#define MAX_CU_SIZE      (1 << MAX_LOG2_CU_SIZE) // maximum allowable size of CU

#define CHROMA_H_SHIFT(x) (x == VEGA_BQB_CSP_I420 || x == VEGA_BQB_CSP_I422)
#define CHROMA_V_SHIFT(x) (x == VEGA_BQB_CSP_I420)

#define VEGA_MAX_BITRATE  600000
#define MAX_VRAW_BUF_SIZE 0x21C0000
#define MAX_PIC_BUF_SIZE  0x7E90000
#define MAX_ES_BUF_SIZE   0x17800
#define MAX_SEI_BUF_SIZE  254

#ifndef UINT64_C
#define UINT64_C(c) (c##ULL)
#endif

typedef struct stResolution
{
	const char *                   string;
	int                            width;
	int                            height;
	API_VEGA_BQB_RESOLUTION_E      eRes;
	API_VEGA_BQB_DEVICE_ENC_MODE_E eMode1;
	API_VEGA_BQB_DEVICE_ENC_MODE_E eMode2;
} stResolution;

/* Log level */
#define VEGA_BQB_LOG_NONE    (-1)
#define VEGA_BQB_LOG_ERROR   16
#define VEGA_BQB_LOG_WARNING 24
#define VEGA_BQB_LOG_INFO    32
#define VEGA_BQB_LOG_VERBOSE 40
#define VEGA_BQB_LOG_DEBUG   48
#define VEGA_BQB_LOG_FULL    56

extern int      vegaff_atoi(const char *str, bool &bError);
extern double   vegaff_atof(const char *str, bool &bError);
extern uint32_t vegaff_str_to_weighted_size(const char *srcstr, int *is_synerr);
extern int      vegaff_parseName(const char *arg, const char *const *names, bool &bError);
extern int      vegaff_atobool(const char *str, bool &bError);
extern int64_t  vegaff_mdate(void);
extern void *   vegaff_malloc(size_t size);
extern void     vegaff_free(void *ptr);
extern void     vegaff_log(const void *param, int level, const char *fmt, ...);
extern void *   vegaff_zalloc(size_t size);

#define ROUND_UP(a, b) (((a + (b)-1) / (b)) * (b))

#if 0
#define TRACE() vegaff_log(NULL, VEGA_BQB_LOG_FULL, "%s:%s:%d\n", __FILE__, __FUNCTION__, __LINE__)
#else
#define TRACE()
#endif

#ifdef __cplusplus
}
#endif

// Allocate object and store size in first field //
template <class T> T *vegaff_zallocT(void)
{
	size_t vgz_objsize = sizeof(T);
	T *    vgz_obj = (T *)vegaff_zalloc(sizeof(T));
	if (vgz_obj)
		vgz_obj->objsize = vgz_objsize;
	return vgz_obj;
}

// Allocate object and store size in first field //
template <class T> T *vegaff_memdupT(const T *src)
{
	size_t vgz_objsize;
	T *    vgz_obj;

	if (!src)
		return NULL;
	vgz_objsize = src->objsize;
	if (vgz_objsize == 0)
	{
		return NULL;
	}
	vgz_obj = (T *)vegaff_zalloc(vgz_objsize);
	if (vgz_obj)
	{
		memcpy(vgz_obj, src, vgz_objsize);
		vgz_obj->objsize = vgz_objsize;
	}
	return vgz_obj;
}

#define vegaff_zeroT(x)                                                                                                \
	{                                                                                                                  \
		size_t _vgz_objsize;                                                                                           \
		if ((x) != NULL)                                                                                               \
		{                                                                                                              \
			_vgz_objsize = (x)->objsize;                                                                               \
			memset((x), 0, _vgz_objsize);                                                                              \
			(x)->objsize = _vgz_objsize;                                                                               \
		}                                                                                                              \
	}

#define vegaff_copyT(x, y)                                                                                             \
	{                                                                                                                  \
		size_t __sz1, __sz2;                                                                                           \
		if (((x) != NULL) && ((y) != NULL))                                                                            \
		{                                                                                                              \
			__sz1 = (x)->objsize;                                                                                      \
			__sz2 = (y)->objsize;                                                                                      \
			memcpy((x), (y), (__sz1 < __sz2) ? __sz1 : __sz2);                                                         \
			(x)->objsize = __sz1;                                                                                      \
		}                                                                                                              \
	}
