/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#include "../common/common.h"
#include "constants.h"

// int      g_ctuSizeConfigured = 0;
// uint32_t g_maxLog2CUSize = MAX_LOG2_CU_SIZE;
uint32_t g_maxCUSize = MAX_CU_SIZE;
#if 0
const uint8_t g_log2Size[MAX_CU_SIZE + 1] = {0, 0, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 4, 4,
											 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
											 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6};
#endif

/* String values accepted by vega_bqb_hevc_param_parse() (and CLI) for various parameters */
const char *const vega_bqb_hevc_motion_est_names[] = {"dia", "hex", "umh", "star", "full", 0};
const char *const vega_bqb_avc_motion_est_names[] = {"dia", "hex", "umh", "star", "full", 0};
const char *const vega_bqb_hevc_source_csp_names[] = {"i420", "i422", "nv12", "nv16", 0};
const char *const vega_bqb_avc_source_csp_names[] = {"i420", "i422", "nv12", "nv16", 0};
const char *const vega_bqb_mpeg_source_csp_names[] = {"i420", "i422", "nv12", "nv16", 0};
const char *const vega_bqb_hevc_video_format_names[] = {"component", "pal", "ntsc", "secam", "mac", "undef", 0};
const char *const vega_bqb_avc_video_format_names[] = {"component", "pal", "ntsc", "secam", "mac", "undef", 0};
const char *const vega_bqb_mpeg_video_format_names[] = {"component", "pal", "ntsc", "secam", "mac", "undef", 0};
const char *const vega_bqb_hevc_fullrange_names[] = {"limited", "full", 0};
const char *const vega_bqb_avc_fullrange_names[] = {"limited", "full", 0};
const char *const vega_bqb_mpeg_fullrange_names[] = {"limited", "full", 0};
const char *const vega_bqb_hevc_colorprim_names[] = {"",          "bt709",     "undef", "",       "bt470m", "bt470bg",
													 "smpte170m", "smpte240m", "film",  "bt2020", 0};
const char *const vega_bqb_avc_colorprim_names[] = {"",          "bt709",     "undef", "",       "bt470m", "bt470bg",
													"smpte170m", "smpte240m", "film",  "bt2020", 0};
const char *const vega_bqb_mpeg_colorprim_names[] = {"",          "bt709",     "undef", "",       "bt470m", "bt470bg",
													 "smpte170m", "smpte240m", "film",  "bt2020", 0};
const char *const vega_bqb_hevc_transfer_names[] = {"",
													"bt709",
													"undef",
													"",
													"bt470m",
													"bt470bg",
													"smpte170m",
													"smpte240m",
													"linear",
													"log100",
													"log316",
													"iec61966-2-4",
													"bt1361e",
													"iec61966-2-1",
													"bt2020-10",
													"bt2020-12",
													"smpte-st-2084",
													"smpte-st-428",
													"arib-std-b67",
													0};
const char *const vega_bqb_avc_transfer_names[] = {"",
												   "bt709",
												   "undef",
												   "",
												   "bt470m",
												   "bt470bg",
												   "smpte170m",
												   "smpte240m",
												   "linear",
												   "log100",
												   "log316",
												   "iec61966-2-4",
												   "bt1361e",
												   "iec61966-2-1",
												   "bt2020-10",
												   "bt2020-12",
												   "smpte-st-2084",
												   "smpte-st-428",
												   "arib-std-b67",
												   0};
const char *const vega_bqb_mpeg_transfer_names[] = {"",
													"bt709",
													"undef",
													"",
													"bt470m",
													"bt470bg",
													"smpte170m",
													"smpte240m",
													"linear",
													"log100",
													"log316",
													"iec61966-2-4",
													"bt1361e",
													"iec61966-2-1",
													"bt2020-10",
													"bt2020-12",
													"smpte-st-2084",
													"smpte-st-428",
													"arib-std-b67",
													0};
const char *const vega_bqb_hevc_colmatrix_names[] = {
	"GBR", "bt709", "undef", "", "fcc", "bt470bg", "smpte170m", "smpte240m", "YCgCo", "bt2020nc", "bt2020c", 0};
const char *const vega_bqb_avc_colmatrix_names[] = {"GBR",       "bt709",     "undef", "",         "fcc",     "bt470bg",
													"smpte170m", "smpte240m", "YCgCo", "bt2020nc", "bt2020c", 0};
const char *const vega_bqb_mpeg_colmatrix_names[] = {
	"GBR", "bt709", "undef", "", "fcc", "bt470bg", "smpte170m", "smpte240m", "YCgCo", "bt2020nc", "bt2020c", 0};
const char *const vega_bqb_hevc_sar_names[] = {"undef", "1,1",    "12,11", "10,11", "16,11", "40,33",
											   "24,11", "20,11",  "32,11", "80,33", "18,11", "15,11",
											   "64,33", "160,99", "4,3",   "3,2",   "2,1",   0};
const char *const vega_bqb_avc_sar_names[] = {"undef", "1,1",    "12,11", "10,11", "16,11", "40,33",
											  "24,11", "20,11",  "32,11", "80,33", "18,11", "15,11",
											  "64,33", "160,99", "4,3",   "3,2",   "2,1",   0};
const char *const vega_bqb_mpeg_sar_names[] = {"undef", "1,1",    "12,11", "10,11", "16,11", "40,33",
											   "24,11", "20,11",  "32,11", "80,33", "18,11", "15,11",
											   "64,33", "160,99", "4,3",   "3,2",   "2,1",   0};
const char *const vega_bqb_hevc_interlace_names[] = {"prog", "interlace", 0};
const char *const vega_bqb_avc_interlace_names[] = {"prog", "interlace", 0};
const char *const vega_bqb_mpeg_interlace_names[] = {"prog", "interlace", 0};
const char *const vega_bqb_hevc_slice_types[] = {"I", "P", "B", 0};
const char *const vega_bqb_avc_slice_types[] = {"I", "P", "B", 0};
const char *const vega_bqb_mpeg_pic_types[] = {"I", "P", "B", 0};
const char *const vega_bqb_encode_mode[] = {"auto",       "1ch-4k",  "4ch-1080p", "8ch-720p",
											"8ch-mixing", "16ch-sd", "16ch-seq",  "16ch-720p"};
