/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#pragma once

#include "../common/common.h"

extern uint32_t g_maxCUSize;

extern const char *const vega_bqb_hevc_motion_est_names[];
extern const char *const vega_bqb_avc_motion_est_names[];
extern const char *const vega_bqb_hevc_source_csp_names[];
extern const char *const vega_bqb_avc_source_csp_names[];
extern const char *const vega_bqb_mpeg_source_csp_names[];
extern const char *const vega_bqb_hevc_video_format_names[];
extern const char *const vega_bqb_avc_video_format_names[];
extern const char *const vega_bqb_mpeg_video_format_names[];
extern const char *const vega_bqb_hevc_fullrange_names[];
extern const char *const vega_bqb_avc_fullrange_names[];
extern const char *const vega_bqb_mpeg_fullrange_names[];
extern const char *const vega_bqb_hevc_colorprim_names[];
extern const char *const vega_bqb_avc_colorprim_names[];
extern const char *const vega_bqb_mpeg_colorprim_names[];
extern const char *const vega_bqb_hevc_transfer_names[];
extern const char *const vega_bqb_avc_transfer_names[];
extern const char *const vega_bqb_mpeg_transfer_names[];
extern const char *const vega_bqb_hevc_colmatrix_names[];
extern const char *const vega_bqb_avc_colmatrix_names[];
extern const char *const vega_bqb_mpeg_colmatrix_names[];
extern const char *const vega_bqb_hevc_sar_names[];
extern const char *const vega_bqb_avc_sar_names[];
extern const char *const vega_bqb_mpeg_sar_names[];
extern const char *const vega_bqb_hevc_interlace_names[];
extern const char *const vega_bqb_avc_interlace_names[];
extern const char *const vega_bqb_mpeg_interlace_names[];
extern const char *const vega_bqb_hevc_slice_types[];
extern const char *const vega_bqb_avc_slice_types[];
extern const char *const vega_bqb_mpeg_pic_types[];
extern const char *const vega_bqb_encode_mode[];
