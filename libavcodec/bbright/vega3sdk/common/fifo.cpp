#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "../common/common.h"
#include "../common/fifo.h"

FiFoBuffer::FiFoBuffer(uint32_t size)
{
	buffer = (uint8_t *)vegaff_malloc(size);
	if (!buffer)
		return;
	end = buffer + size;
	wptr = rptr = buffer;
}

FiFoBuffer::~FiFoBuffer()
{
	if (buffer)
		vegaff_free(buffer);
}

uint32_t FiFoBuffer::fifo_size()
{
	ptrdiff_t bufsize = (ptrdiff_t)(end - buffer);

	return (uint32_t)((wptr - rptr + bufsize) % bufsize);
}

uint32_t FiFoBuffer::fifo_space()
{
	return (uint32_t)(end - buffer - fifo_size());
}

int32_t FiFoBuffer::fifo_generic_read(void *dest, uint32_t size)
{
	// Read memory barrier needed for SMP here in theory
	do
	{
		ptrdiff_t len = STD_MIN(end - rptr, (ptrdiff_t)size);
		memcpy(dest, rptr, len);
		dest = (uint8_t *)dest + len;

		// memory barrier needed for SMP here in theory
		fifo_drain((uint32_t)len);
		size -= (uint32_t)len;
	} while (size > 0);
	return 0;
}

int32_t FiFoBuffer::fifo_generic_write(void *src, uint32_t size)
{
	int      total = size;
	uint8_t *_wptr = wptr;

	do
	{
		size_t len = STD_MIN(end - _wptr, size);
		memcpy(_wptr, src, len);
		src = (uint8_t *)src + len;
		_wptr += len;
		if (_wptr >= end)
			_wptr = buffer;
		size -= (uint32_t)len;
	} while (size > 0);
	wptr = _wptr;
	return total - size;
}

void FiFoBuffer::fifo_drain(uint32_t size)
{
	rptr += size;
	if (rptr >= end)
		rptr -= (end - buffer);
}

void FiFoBuffer::fifo_advance(uint32_t size)
{
	wptr += size;
	if (wptr >= end)
		wptr -= (end - buffer);
}
