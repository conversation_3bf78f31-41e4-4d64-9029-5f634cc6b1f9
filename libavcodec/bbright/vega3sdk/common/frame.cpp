/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "../common/common.h"
#include "../common/frame.h"
#include "../common/picyuv.h"

#ifdef _WIN32
#include <libvega_bqb_api/pxxc.h>
#define _PXX_PP210 PXX_PP210
#define _PXX_PP010 PXX_PP010
#else
#include <libvega_bqb_api/pxxc.h>
#define _PXX_PP210 PXX_P210
#define _PXX_PP010 PXX_P010
#endif



extern "C" const stResolution gstRes264[];
extern "C" const int          gstRes264_count;
extern "C" const stResolution gstRes265[];
extern "C" const int          gstRes265_count;

typedef struct _vegaff_cli_csp
{
	int planes;
	int width[3];
	int height[3];
} vegaff_cli_csp;

static const vegaff_cli_csp vegaff_cli_csps[] = {
	{3, {0, 1, 1}, {0, 1, 1}}, /* i420 */
	{3, {0, 1, 1}, {0, 0, 0}}, /* i422 */
	{2, {0, 0}, {0, 1}},       /* nv12 */
	{2, {0, 0}, {0, 0}},       /* nv16 */
};

// using namespace vega_bqb_avc;

Frame::Frame()
{
	_fencPic = NULL;
	_param = NULL;
	_width0 = 0;
	_height0 = 0;
	_width1 = 0;
	_height1 = 0;
	_maxPictureSize = 0;
	_user_data_registered_itu_t_t35 = false;
	_sizeChanged = false;
	_pxxc = NULL;
}

bool Frame::create(vegaff_codec_param *p)
{
	_fencPic = new PicYuv;
	_param = p;

	return _fencPic->create(p->inputWidth, p->inputHeight, p->internalCsp, p->internalBitDepth);
}

void Frame::destroy()
{
	if (_fencPic)
	{
		_fencPic->destroy();
		delete _fencPic;
		_fencPic = NULL;
	}
	if (_pxxc)
	{
		pxxc_destroy(_pxxc);
		_pxxc = NULL;
	}
}

uint32_t Frame::getPictureSize(const vegaff_picture_t *pic)
{
	vegaff_codec_param *p = _param;
	int                 colorSpace = p->internalCsp;
	int                 depth = p->internalBitDepth;
#if 0
	int width = pic ? pic->width : p->sourceWidth;
	int height = pic ? pic->height : p->sourceHeight;
#else
	int round = 16;
	if (pic)
	{
		if (pic->width <= 720)
			round = 32;
	}
	else
	{
		if (p->inputWidth <= 720)
			round = 32;
	}

	int width = pic ? ROUND_UP(pic->width, round) : ROUND_UP(p->inputWidth, round);
	int height = pic ? ROUND_UP(pic->height, round) : ROUND_UP(p->inputHeight, round);
#endif
	uint32_t pictureSize = 0;
	uint32_t pixelbytes = depth > 8 ? 2 : 1;

	for (int i = 0; i < vegaff_cli_csps[colorSpace].planes; i++)
	{
		uint32_t w = width >> vegaff_cli_csps[colorSpace].width[i];
		uint32_t h = height >> vegaff_cli_csps[colorSpace].height[i];
		pictureSize += w * h * pixelbytes;
	}
	if (pic && pic->bitDepth == 10)
	{
		if (colorSpace == VEGA_BQB_CSP_I420)
			pictureSize = _maxPictureSize = width * height * 15 / 8;
		else
			pictureSize = _maxPictureSize = width * height * 5 / 2;
	}
	else
	{
		_maxPictureSize = STD_MAX(_maxPictureSize, pictureSize);
	}

	return pictureSize;
}

static void memcpy_stride(uint8_t *dst, uint8_t *src, uint32_t width, uint32_t stride_width, uint32_t height, int round)
{
	int round_width = ROUND_UP(width, round);

	for (uint32_t i = 0; i < height; i++)
	{
		memcpy(dst, src, width);
		dst += round_width;
		src += stride_width;
	}
	//	dst += (ROUND_UP(height,16) - height) * ROUND_UP(width,16);
}

static bool check_resolution264(int width, int height, int interlace)
{
	if (interlace)
	{
		if (width == 1920 && height == 540) // 1080i
			return false;
		else if (width == 720 && height == 288) // PAL
			return false;
		else if (width == 720 && height == 240) // NTSC
			return false;
	}
	for (int i = 0; i < (int)gstRes264_count; i++)
	{
		if (width == gstRes264[i].width && height == gstRes264[i].height)
		{
			return false;
		}
	}
	return true;
}

static bool check_resolution265(int width, int height, int interlace)
{
	int res_table_size = gstRes265_count;

	if (interlace)
	{
		if (width == 1920 && height == 540) // 1080i
			return false;
		else if (width == 720 && height == 288) // PAL
			return false;
		else if (width == 720 && height == 240) // NTSC
			return false;
	}
	for (int i = 0; i < res_table_size; i++)
	{
		if (width == gstRes265[i].width && height == gstRes265[i].height)
		{
			return false;
		}
	}
	return true;
}

void Frame::copyFromPicture(vega_bqb_mpeg_encoder *enc, const vegaff_picture_t *pic)
{
	vega_bqb_mpeg_encoder *encoder = static_cast<vega_bqb_mpeg_encoder *>(enc);
	vegaff_codec_param *   p = _param;
	int                    colorSpace = p->internalCsp;
	int                    width = pic->width;
	int                    height = pic->height;
	uint8_t *              dst = (uint8_t *)encoder->_vrawBuf;

	if (colorSpace == VEGA_BQB_CSP_NV12 || colorSpace == VEGA_BQB_CSP_NV16)
	{
		memcpy(dst, pic->planes[0], pic->stride[0] * height);
		dst += pic->stride[0] * height;
		memcpy(dst, pic->planes[1], pic->stride[1] * (height >> vegaff_cli_csps[colorSpace].height[1]));
	}
	else if (colorSpace == VEGA_BQB_CSP_I420 || colorSpace == VEGA_BQB_CSP_I422)
	{
		int temp_width = pic->bitDepth == 10 ? width : (width / 2);
		if (pic->stride[0] % width != 0 || pic->stride[1] != temp_width || pic->stride[2] != temp_width)
		{
			_fencPic->copyFromPicture(*pic);
			memcpy(dst, _fencPic->_u8picOrg[0], width * height);
			dst += width * height;
			int widthC = width >> vegaff_cli_csps[colorSpace].width[1];
			int heightC = height >> vegaff_cli_csps[colorSpace].height[1];
			memcpy(dst, _fencPic->_u8picOrg[1], widthC * heightC);
			dst += widthC * heightC;
			int widthCC = width >> vegaff_cli_csps[colorSpace].width[2];
			int heightCC = height >> vegaff_cli_csps[colorSpace].height[2];
			memcpy(dst, _fencPic->_u8picOrg[2], widthCC * heightCC);
		}
		else if (pic->stride[0] % width == 0)
		{
			if (pic->bitDepth == 8)
			{
				memcpy(dst, pic->planes[0], pic->stride[0] * height);
				dst += pic->stride[0] * height;
				memcpy(dst, pic->planes[1], pic->stride[1] * (height >> vegaff_cli_csps[colorSpace].height[1]));
				dst += pic->stride[1] * (height >> vegaff_cli_csps[colorSpace].height[1]);
				memcpy(dst, pic->planes[2], pic->stride[2] * (height >> vegaff_cli_csps[colorSpace].height[2]));
			}
			else if (pic->bitDepth == 10)
			{
				void *sv210[2];
				void *yuv[3];
				int   sv210_size = (width * height * 10 / 8);
				sv210[0] = (void *)((((intptr_t)dst) + 15) & -16);
				dst = dst + sv210_size;
				sv210[1] = (void *)((((intptr_t)dst) + 15) & -16);
				yuv[0] = (void *)pic->planes[0];
				yuv[1] = (void *)pic->planes[1];
				yuv[2] = (void *)pic->planes[2];
				if (colorSpace == VEGA_BQB_CSP_I420)
				{
					if (!_pxxc)
					{
						_pxxc = pxxc_create(pic->width, pic->height, PXX_YUV420P10LE, _PXX_PP010);
					}
					pxxc_convert(_pxxc, yuv, sv210);
				}
				else
				{
					if (!_pxxc)
					{
						_pxxc = pxxc_create(pic->width, pic->height, PXX_YUV422P10LE, _PXX_PP210);
					}
					pxxc_convert(_pxxc, yuv, sv210);
				}
			}
		}
	}

	if (pic->sei_data && pic->sei_size)
	{
		memcpy((void *)encoder->_seiBuf, pic->sei_data, pic->sei_size);
		encoder->_seiSize = pic->sei_size;
		_user_data_registered_itu_t_t35 = true;
	}
	else
		_user_data_registered_itu_t_t35 = false;
}

void Frame::copyFromPicture(vega_bqb_avc_encoder *enc, const vegaff_picture_t *pic)
{
	vega_bqb_avc_encoder *encoder = static_cast<vega_bqb_avc_encoder *>(enc);
	vegaff_codec_param *  p = _param;
	int                   colorSpace = p->internalCsp;
	int                   width = pic->width;
	int                   height = pic->height;
	uint8_t *             dst = (uint8_t *)encoder->_vrawBuf;
	int                   round = 16;
	if (width <= 720)
		round = 32;
	if (colorSpace == VEGA_BQB_CSP_NV12 || colorSpace == VEGA_BQB_CSP_NV16)
	{
		if ((ROUND_UP(width, round) != width || ROUND_UP(height, round) != height) &&
			(check_resolution264(width, height, p->interlaceMode)))
		{

			memcpy_stride(dst, pic->planes[0], width, pic->stride[0], height, round);
			dst += ROUND_UP(width, round) * ROUND_UP(height, round);
			memcpy_stride(dst, pic->planes[1], width, pic->stride[1], (height >> vegaff_cli_csps[colorSpace].height[1]),
						  round);
		}
		else
		{
			memcpy(dst, pic->planes[0], pic->stride[0] * height);
			dst += pic->stride[0] * height;
			memcpy(dst, pic->planes[1], pic->stride[1] * (height >> vegaff_cli_csps[colorSpace].height[1]));
		}
	}
	else if (colorSpace == VEGA_BQB_CSP_I420 || colorSpace == VEGA_BQB_CSP_I422)
	{
		int temp_width = pic->bitDepth == 10 ? width : (width / 2);
		if (pic->stride[0] % width != 0 || pic->stride[1] != temp_width || pic->stride[2] != temp_width)
		{
			_fencPic->copyFromPicture(*pic);
			if ((ROUND_UP(width, round) != width || ROUND_UP(height, round) != height) &&
				check_resolution264(width, height, p->interlaceMode))
			{
				memcpy_stride(dst, _fencPic->_u8picOrg[0], width, width, height, round);
				dst += ROUND_UP(width, round) * ROUND_UP(height, round);

				int widthC = width >> vegaff_cli_csps[colorSpace].width[1];
				int heightC = height >> vegaff_cli_csps[colorSpace].height[1];

				memcpy_stride(dst, _fencPic->_u8picOrg[1], widthC, widthC, heightC, round / 2);
				dst += ROUND_UP(widthC, round / 2) * (ROUND_UP(height, round) >> vegaff_cli_csps[colorSpace].height[1]);

				int widthCC = width >> vegaff_cli_csps[colorSpace].width[2];
				int heightCC = height >> vegaff_cli_csps[colorSpace].height[2];
				memcpy_stride(dst, _fencPic->_u8picOrg[2], widthCC, widthCC, heightCC, round / 2);
			}
			else
			{
				memcpy(dst, _fencPic->_u8picOrg[0], width * height);
				dst += width * height;
				int widthC = width >> vegaff_cli_csps[colorSpace].width[1];
				int heightC = height >> vegaff_cli_csps[colorSpace].height[1];
				memcpy(dst, _fencPic->_u8picOrg[1], widthC * heightC);
				dst += widthC * heightC;
				int widthCC = width >> vegaff_cli_csps[colorSpace].width[2];
				int heightCC = height >> vegaff_cli_csps[colorSpace].height[2];
				memcpy(dst, _fencPic->_u8picOrg[2], widthCC * heightCC);
			}
		}
		else if (pic->stride[0] % width == 0)
		{
			if (pic->bitDepth == 8)
			{
				if ((ROUND_UP(width, round) != width || ROUND_UP(height, round) != height) &&
					check_resolution264(width, height, p->interlaceMode))
				{
					memcpy_stride(dst, pic->planes[0], width >> vegaff_cli_csps[colorSpace].width[0], pic->stride[0],
								  height, round);
					dst += ROUND_UP(pic->stride[0], round) * ROUND_UP(height, round);
					memcpy_stride(dst, pic->planes[1], width >> vegaff_cli_csps[colorSpace].width[1], pic->stride[1],
								  (height >> vegaff_cli_csps[colorSpace].height[1]), round / 2);
					dst += ROUND_UP(pic->stride[1], round / 2) *
						   (ROUND_UP(height, round) >> vegaff_cli_csps[colorSpace].height[1]);
					memcpy_stride(dst, pic->planes[2], width >> vegaff_cli_csps[colorSpace].width[2], pic->stride[2],
								  (height >> vegaff_cli_csps[colorSpace].height[2]), round / 2);
				}
				else
				{
					memcpy(dst, pic->planes[0], pic->stride[0] * height);
					dst += pic->stride[0] * height;
					memcpy(dst, pic->planes[1], pic->stride[1] * (height >> vegaff_cli_csps[colorSpace].height[1]));
					dst += pic->stride[1] * (height >> vegaff_cli_csps[colorSpace].height[1]);
					memcpy(dst, pic->planes[2], pic->stride[2] * (height >> vegaff_cli_csps[colorSpace].height[2]));
				}
			}
			else if (pic->bitDepth == 10)
			{
				void *sv210[2];
				void *yuv[3];
				int   sv210_size = (width * height * 10 / 8);
				sv210[0] = (void *)((((intptr_t)dst) + 15) & -16);
				dst = dst + sv210_size;
				sv210[1] = (void *)((((intptr_t)dst) + 15) & -16);
				yuv[0] = (void *)pic->planes[0];
				yuv[1] = (void *)pic->planes[1];
				yuv[2] = (void *)pic->planes[2];
				if (colorSpace == VEGA_BQB_CSP_I420)
				{
					if (!_pxxc)
					{
						_pxxc = pxxc_create(pic->width, pic->height, PXX_YUV420P10LE, _PXX_PP010);
					}
					pxxc_convert(_pxxc, yuv, sv210);
				}
				else
				{
					if (!_pxxc)
					{
						_pxxc = pxxc_create(pic->width, pic->height, PXX_YUV422P10LE, _PXX_PP210);
					}
					pxxc_convert(_pxxc, yuv, sv210);
				}
			}
		}
	}

	if (pic->sei_data && pic->sei_size)
	{
		memcpy((void *)encoder->_seiBuf, pic->sei_data, pic->sei_size);
		encoder->_seiSize = pic->sei_size;
		_user_data_registered_itu_t_t35 = true;
	}
	else
		_user_data_registered_itu_t_t35 = false;
}

void Frame::copyFromPicture(vega_bqb_hevc_encoder *enc, const vegaff_picture_t *pic)
{
	vega_bqb_hevc_encoder *encoder = static_cast<vega_bqb_hevc_encoder *>(enc);
	vegaff_codec_param *   p = _param;
	int                    colorSpace = p->internalCsp;
	int                    width = pic->width;
	int                    height = pic->height;
	uint8_t *              dst = (uint8_t *)encoder->_vrawBuf;

	int round = 16;
	if (width <= 720)
		round = 32;

	if (colorSpace == VEGA_BQB_CSP_NV12 || colorSpace == VEGA_BQB_CSP_NV16)
	{
		if ((ROUND_UP(width, round) != width || ROUND_UP(height, round) != height) &&
			(check_resolution265(width, height, p->interlaceMode)))
		{

			memcpy_stride(dst, pic->planes[0], width, pic->stride[0], height, round);
			dst += ROUND_UP(width, round) * ROUND_UP(height, round);
			memcpy_stride(dst, pic->planes[1], width, pic->stride[1],
						  (height >> vegaff_cli_csps[colorSpace].height[1]), round);
		}
		else
		{
			memcpy(dst, pic->planes[0], pic->stride[0] * height);
			dst += pic->stride[0] * height;
			memcpy(dst, pic->planes[1], pic->stride[1] * (height >> vegaff_cli_csps[colorSpace].height[1]));
		}
	}
	else if (colorSpace == VEGA_BQB_CSP_I420 || colorSpace == VEGA_BQB_CSP_I422)
	{

		int temp_width = pic->bitDepth == 10 ? width : (width / 2);
		if (pic->stride[0] % width != 0 || pic->stride[1] != temp_width || pic->stride[2] != temp_width)
		{
			_fencPic->copyFromPicture(*pic);
			if ((ROUND_UP(width, round) != width || ROUND_UP(height, round) != height) &&
				check_resolution265(width, height, p->interlaceMode))
			{
				memcpy_stride(dst, _fencPic->_u8picOrg[0], width, width, height, round);
				dst += ROUND_UP(width, round) * ROUND_UP(height, round);

				int widthC = width >> vegaff_cli_csps[colorSpace].width[1];
				int heightC = height >> vegaff_cli_csps[colorSpace].height[1];

				memcpy_stride(dst, _fencPic->_u8picOrg[1], widthC, widthC, heightC, round / 2);
				dst += ROUND_UP(widthC, round / 2) * (ROUND_UP(height, round) >> vegaff_cli_csps[colorSpace].height[1]);

				int widthCC = width >> vegaff_cli_csps[colorSpace].width[2];
				int heightCC = height >> vegaff_cli_csps[colorSpace].height[2];
				memcpy_stride(dst, _fencPic->_u8picOrg[2], widthCC, widthCC, heightCC, round / 2);
			}
			else
			{
				memcpy(dst, _fencPic->_u8picOrg[0], width * height);
				dst += width * height;
				int widthC = width >> vegaff_cli_csps[colorSpace].width[1];
				int heightC = height >> vegaff_cli_csps[colorSpace].height[1];
				memcpy(dst, _fencPic->_u8picOrg[1], widthC * heightC);
				dst += widthC * heightC;
				int widthCC = width >> vegaff_cli_csps[colorSpace].width[2];
				int heightCC = height >> vegaff_cli_csps[colorSpace].height[2];
				memcpy(dst, _fencPic->_u8picOrg[2], widthCC * heightCC);
			}
		}
		else if (pic->stride[0] % width == 0)
		{
			if (pic->bitDepth == 8)
			{
				if ((ROUND_UP(width, round) != width || ROUND_UP(height, round) != height) &&
					check_resolution265(width, height, p->interlaceMode))
				{
					memcpy_stride(dst, pic->planes[0], width >> vegaff_cli_csps[colorSpace].width[0], pic->stride[0],
								  height, round);
					dst += ROUND_UP(pic->stride[0], round) * ROUND_UP(height, round);
					memcpy_stride(dst, pic->planes[1], width >> vegaff_cli_csps[colorSpace].width[1], pic->stride[1],
								  (height >> vegaff_cli_csps[colorSpace].height[1]), round / 2);
					dst += ROUND_UP(pic->stride[1], round / 2) *
						   (ROUND_UP(height, round) >> vegaff_cli_csps[colorSpace].height[1]);
					memcpy_stride(dst, pic->planes[2], width >> vegaff_cli_csps[colorSpace].width[2], pic->stride[2],
								  (height >> vegaff_cli_csps[colorSpace].height[2]), round / 2);
				}
				else
				{
					memcpy(dst, pic->planes[0], pic->stride[0] * height);
					dst += pic->stride[0] * height;
					memcpy(dst, pic->planes[1], pic->stride[1] * (height >> vegaff_cli_csps[colorSpace].height[1]));
					dst += pic->stride[1] * (height >> vegaff_cli_csps[colorSpace].height[1]);
					memcpy(dst, pic->planes[2], pic->stride[2] * (height >> vegaff_cli_csps[colorSpace].height[2]));
				}
			}
			else if (pic->bitDepth == 10)
			{
				void *sv210[2];
				void *yuv[3];
				int   sv210_size = (width * height * 10 / 8);
				sv210[0] = (void *)((((intptr_t)dst) + 15) & -16);
				dst = dst + sv210_size;
				sv210[1] = (void *)((((intptr_t)dst) + 15) & -16);
				yuv[0] = (void *)pic->planes[0];
				yuv[1] = (void *)pic->planes[1];
				yuv[2] = (void *)pic->planes[2];
				if (colorSpace == VEGA_BQB_CSP_I420)
				{
					if (!_pxxc)
					{
						_pxxc = pxxc_create(pic->width, pic->height, PXX_YUV420P10LE, _PXX_PP010);
					}
					pxxc_convert(_pxxc, yuv, sv210);
				}
				else
				{
					if (!_pxxc)
					{
						_pxxc = pxxc_create(pic->width, pic->height, PXX_YUV422P10LE, _PXX_PP210);
					}
					pxxc_convert(_pxxc, yuv, sv210);
				}
			}
		}
	}

	if (pic->sei_data && pic->sei_size)
	{
		memcpy((void *)encoder->_seiBuf, pic->sei_data, pic->sei_size);
		encoder->_seiSize = pic->sei_size;
		_user_data_registered_itu_t_t35 = true;
	}
	else
		_user_data_registered_itu_t_t35 = false;
}

void Frame::contextReinit(const vegaff_picture_t *pic)
{
	vegaff_codec_param *p = _param;
#if 0
	_width0 = (uint32_t)p->sourceWidth;
	_height0 = (uint32_t)p->sourceHeight;
#else
	_width0 = (uint32_t)p->inputWidth;
	_height0 = (uint32_t)p->inputHeight;
#endif
	_width1 = (uint32_t)pic->width;
	_height1 = (uint32_t)pic->height;

	_sizeChanged = (_width0 != _width1 || _height0 != _height1) ? true : false;
}
