/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#pragma once

#include "../encode/encoder262.h"
#include "../encode/encoder264.h"
#include "../encode/encoder265.h"
#include "../common/pixfmt_convert.h"

class PicYuv;

class Frame
{
public:
	Frame();
	~Frame()
	{
	}

	bool     create(vegaff_codec_param *);
	void     destroy();
	uint32_t getPictureSize(const vegaff_picture_t *);
	void     copyFromPicture(vega_bqb_avc_encoder *, const vegaff_picture_t *);
	void     copyFromPicture(vega_bqb_hevc_encoder *, const vegaff_picture_t *);
	void     copyFromPicture(vega_bqb_mpeg_encoder *, const vegaff_picture_t *);
	void     contextReinit(const vegaff_picture_t *);

	PicYuv *            _fencPic;
	vegaff_codec_param *_param;
	uint32_t            _width0;
	uint32_t            _height0;
	uint32_t            _width1;
	uint32_t            _height1;
	uint32_t            _maxPictureSize;
	bool                _user_data_registered_itu_t_t35;
	bool                _sizeChanged;
	pxxc_t *            _pxxc;
};
