/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#pragma once
#include <stdint.h>

typedef enum
{
	BITRATE_SET = 0,
	VARIABLE_BITRATE_SET = 1,
	IDR_INSERT = 2,
	IDR_INSERT_AT = 3,
	FRAMERATE_SET_AT = 4,
	RESOLUTION_SET_AT = 5,
	UNKNOWN_OPT = 6
} MessageOptionType;

class MessageOption
{
public:
	MessageOption(MessageOptionType index = UNKNOWN_OPT)
		: _bitrate(0), _maxVbr(0), _aveVbr(0), _minVbr(0), _picNum(0), _fps(0), _width(0), _height(0), _optType(index)
	{
	}

	~MessageOption()
	{
	}

	MessageOptionType messageOptionType() const
	{
		return _optType;
	}

	uint32_t _bitrate;
	uint32_t _maxVbr;
	uint32_t _aveVbr;
	uint32_t _minVbr;
	uint32_t _picNum;
	uint32_t _fps;
	int      _width;
	int      _height;

protected:
	MessageOptionType _optType;
};
