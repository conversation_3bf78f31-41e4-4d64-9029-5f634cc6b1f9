/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "../common/common.h"
#include "constants.h"
#include "../common/picyuv.h"

PicYuv::PicYuv()
{
	_u8picBuf[0] = NULL;
	_u8picBuf[1] = NULL;
	_u8picBuf[2] = NULL;
	_u8picOrg[0] = NULL;
	_u8picOrg[1] = NULL;
	_u8picOrg[2] = NULL;
	_u16picBuf[0] = NULL;
	_u16picBuf[1] = NULL;
	_u16picBuf[2] = NULL;
	_u16picOrg[0] = NULL;
	_u16picOrg[1] = NULL;
	_u16picOrg[2] = NULL;
}

bool PicYuv::create(uint32_t picWidth, uint32_t picHeight, uint32_t picCsp, uint32_t picBitDepth)
{
	_picWidth = picWidth;
	_picHeight = picHeight;
	_hChromaShift = CHROMA_H_SHIFT(picCsp);
	_vChromaShift = CHROMA_V_SHIFT(picCsp);
	_picCsp = picCsp;
	uint32_t numCuInWidth = (_picWidth + g_maxCUSize - 1) / g_maxCUSize;
	uint32_t numCuInHeight = (_picHeight + g_maxCUSize - 1) / g_maxCUSize;
	_lumaMarginX = g_maxCUSize + 32; // search margin and 8-tap filter half-length, padded for 32-byte alignment
	_lumaMarginY = g_maxCUSize + 16; // margin for 8-tap filter and infinite padding
	_stride = (numCuInWidth * g_maxCUSize) + (_lumaMarginX << 1);
	_chromaMarginX = _lumaMarginX; // keep 16-byte alignment for chroma CTUs
	_chromaMarginY = _lumaMarginY >> _vChromaShift;
	_strideC = ((numCuInWidth * g_maxCUSize) >> _hChromaShift) + (_chromaMarginX * 2);
	int maxHeight = numCuInHeight * g_maxCUSize;

	if (picBitDepth == 8)
	{
		CHECKED_MALLOC(_u8picBuf[0], uint8_t, _stride * (maxHeight + (_lumaMarginY * 2)));
		CHECKED_MALLOC(_u8picBuf[1], uint8_t, _strideC * ((maxHeight >> _vChromaShift) + (_chromaMarginY * 2)));
		CHECKED_MALLOC(_u8picBuf[2], uint8_t, _strideC * ((maxHeight >> _vChromaShift) + (_chromaMarginY * 2)));
		_u8picOrg[0] = _u8picBuf[0] + _lumaMarginY * _stride + _lumaMarginX;
		_u8picOrg[1] = _u8picBuf[1] + _chromaMarginY * _strideC + _chromaMarginX;
		_u8picOrg[2] = _u8picBuf[2] + _chromaMarginY * _strideC + _chromaMarginX;
	}
	else /* picBitDepth > 8 */
	{
		CHECKED_MALLOC(_u16picBuf[0], uint16_t, _stride * (maxHeight + (_lumaMarginY * 2)));
		CHECKED_MALLOC(_u16picBuf[1], uint16_t, _strideC * ((maxHeight >> _vChromaShift) + (_chromaMarginY * 2)));
		CHECKED_MALLOC(_u16picBuf[2], uint16_t, _strideC * ((maxHeight >> _vChromaShift) + (_chromaMarginY * 2)));
		_u16picOrg[0] = _u16picBuf[0] + _lumaMarginY * _stride + _lumaMarginX;
		_u16picOrg[1] = _u16picBuf[1] + _chromaMarginY * _strideC + _chromaMarginX;
		_u16picOrg[2] = _u16picBuf[2] + _chromaMarginY * _strideC + _chromaMarginX;
	}

	return true;
fail:
	return false;
}

void PicYuv::destroy()
{
	vegaff_free(_u8picBuf[0]);
	vegaff_free(_u8picBuf[1]);
	vegaff_free(_u8picBuf[2]);
	vegaff_free(_u16picBuf[0]);
	vegaff_free(_u16picBuf[1]);
	vegaff_free(_u16picBuf[2]);
}

void PicYuv::copyFromPicture(const vegaff_picture_t &pic)
{
	int width = _picWidth;
	int height = _picHeight;

	if (pic.bitDepth == 8)
	{
		uint8_t *yPixel = _u8picOrg[0];
		uint8_t *uPixel = _u8picOrg[1];
		uint8_t *vPixel = _u8picOrg[2];
		uint8_t *yChar = (uint8_t *)pic.planes[0];
		uint8_t *uChar = (uint8_t *)pic.planes[1];
		uint8_t *vChar = (uint8_t *)pic.planes[2];

		for (int r = 0; r < height; r++)
		{
			memcpy(yPixel, yChar, width * sizeof(uint8_t));
			yPixel += width;
			yChar += pic.stride[0] / sizeof(*yChar);
		}

		for (int r = 0; r < height >> _vChromaShift; r++)
		{
			memcpy(uPixel, uChar, (width >> _hChromaShift) * sizeof(uint8_t));
			memcpy(vPixel, vChar, (width >> _hChromaShift) * sizeof(uint8_t));
			uPixel += width >> _hChromaShift;
			vPixel += width >> _hChromaShift;
			uChar += pic.stride[1] / sizeof(*uChar);
			vChar += pic.stride[2] / sizeof(*vChar);
		}
	}
}
