/*
 *
 * Copyright (C) 2017 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "../common/pixfmt_convert.h"

PixFmtConverter::PixFmtConverter()
{
	_frame_w = 0;
	_frame_h = 0;
	convert_func = NULL;
	_pxxc = NULL;
}

PixFmtConverter::~PixFmtConverter()
{
	if (_pxxc)
		pxxc_destroy(_pxxc);
}

int PixFmtConverter::do_configure(int32_t width, int32_t height, vega_bqb_pix_fmt_t from_fmt, vega_bqb_pix_fmt_t to_fmt)
{
	if (!width || !height)
		goto fail;

	_frame_w = width;
	_frame_h = height;
	_from_fmt = from_fmt;
	_to_fmt = to_fmt;

	if (_from_fmt == VEGA_BQB_PIX_FMT_NV16 && _to_fmt == VEGA_BQB_PIX_FMT_NV12)
	{
		convert_func = &PixFmtConverter::vega_bqb_nv16_to_nv12;
		converted_buf_stride[0] = _frame_w;
		converted_buf_stride[1] = _frame_w;
		converted_buf_stride[2] = 0;
	}
	else if (_from_fmt == VEGA_BQB_PIX_FMT_NV16 && _to_fmt == VEGA_BQB_PIX_FMT_YUV420P)
	{
		convert_func = &PixFmtConverter::vega_bqb_nv16_to_yuv420p;
		converted_buf_stride[0] = _frame_w;
		converted_buf_stride[1] = _frame_w >> 1;
		converted_buf_stride[2] = _frame_w >> 1;
	}
	else if (_from_fmt == VEGA_BQB_PIX_FMT_NV16 && _to_fmt == VEGA_BQB_PIX_FMT_YUV422P)
	{
		convert_func = &PixFmtConverter::vega_bqb_nv16_to_yuv422p;
		converted_buf_stride[0] = _frame_w;
		converted_buf_stride[1] = _frame_w >> 1;
		converted_buf_stride[2] = _frame_w >> 1;
	}
	else if (_from_fmt == VEGA_BQB_PIX_FMT_NV16 && _to_fmt == VEGA_BQB_PIX_FMT_NV16)
	{
		convert_func = &PixFmtConverter::vega_bqb_nv16_to_nv16;
		converted_buf_stride[0] = _frame_w;
		converted_buf_stride[1] = _frame_w;
		converted_buf_stride[2] = 0;
	}
	else if (_from_fmt == VEGA_BQB_PIX_FMT_V210 && _to_fmt == VEGA_BQB_PIX_FMT_YUV420P10LE)
	{
		_pxxc = pxxc_create(_frame_w, _frame_h, PXX_SNIV210, PXX_YUV422P10LE);
		convert_func = &PixFmtConverter::vega_bqb_sniv210_to_yuv420p10le;
		converted_buf_stride[0] = _frame_w * 2;
		converted_buf_stride[1] = _frame_w;
		converted_buf_stride[2] = _frame_w;

		_src_stride[0] = ((_frame_w * 16 / 12) + 63) / 64 * 64;
		_src_stride[1] = _src_stride[0];
		_src_stride[2] = 0;
	}
	else if (_from_fmt == VEGA_BQB_PIX_FMT_V210 && _to_fmt == VEGA_BQB_PIX_FMT_YUV422P10LE)
	{
		_pxxc = pxxc_create(_frame_w, _frame_h, PXX_SNIV210, PXX_YUV422P10LE);
		convert_func = &PixFmtConverter::vega_bqb_sniv210_to_yuv422p10le;
		converted_buf_stride[0] = _frame_w * 2;
		converted_buf_stride[1] = _frame_w;
		converted_buf_stride[2] = _frame_w;

		_src_stride[0] = ((_frame_w * 16 / 12) + 63) / 64 * 64;
		_src_stride[1] = _src_stride[0];
		_src_stride[2] = 0;
	}
	else
		goto fail;

	return 0;

fail:
	vegaff_log(NULL, VEGA_BQB_LOG_WARNING, "PixFmtConverter configuration parameters not correct\n");
	return -1;
}

int PixFmtConverter::do_convert(void *in_buf[], void *out_buf[])
{
	if (!convert_func)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_WARNING, "PixFmtConverter didn't configure yet\n");
		return -1;
	}

	return (this->*convert_func)(in_buf, out_buf);
}

int PixFmtConverter::vega_bqb_nv16_to_yuv420p(void *in_buf[], void *out_buf[])
{
	uint8_t *u_buf_ptr = (uint8_t *)out_buf[1];
	uint8_t *v_buf_ptr = (uint8_t *)out_buf[2];
	uint8_t *uv_ptr = (uint8_t *)in_buf[1];
	int32_t  i;
	int32_t  j;

	memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], _frame_h * _frame_w);

	for (i = 0; i < _frame_h; i++)
	{
		/* vertical downsampling */
		if (i % 2)
		{
			for (j = 0; j < _frame_w; j += 2)
			{
				*(u_buf_ptr++) = *(uv_ptr + j);
				*(v_buf_ptr++) = *(uv_ptr + j + 1);
			}
			uv_ptr += _frame_w * 2;
		}
	}

	return 0;
}

int PixFmtConverter::vega_bqb_nv16_to_nv12(void *in_buf[], void *out_buf[])
{
	uint8_t *uv16_buf_ptr = (uint8_t *)in_buf[1];
	uint8_t *uv12_buf_ptr = (uint8_t *)out_buf[1];
	int32_t  i;

	memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], _frame_h * _frame_w);

	for (i = 0; i < _frame_h; i++)
	{
		/* vertical downsampling */
		if (i % 2)
		{
			memcpy(uv12_buf_ptr, uv16_buf_ptr, _frame_w);
			uv12_buf_ptr += _frame_w;
		}
		uv16_buf_ptr += _frame_w;
	}

	return 0;
}

int PixFmtConverter::vega_bqb_nv16_to_yuv422p(void *in_buf[], void *out_buf[])
{
	uint8_t *u_buf_ptr = (uint8_t *)out_buf[1];
	uint8_t *v_buf_ptr = (uint8_t *)out_buf[2];
	uint8_t *uv_ptr = (uint8_t *)in_buf[1];
	int32_t  i;

	memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], _frame_h * _frame_w);

	for (i = 0; i < _frame_h * _frame_w; i++)
	{
		if ((i % 2) == 0)
			*u_buf_ptr++ = *(uv_ptr + i);
		else
			*v_buf_ptr++ = *(uv_ptr + i);
	}

	return 0;
}

int PixFmtConverter::vega_bqb_nv16_to_nv16(void *in_buf[], void *out_buf[])
{
	memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], _frame_h * _frame_w);
	memcpy((uint8_t *)out_buf[1], (uint8_t *)in_buf[1], _frame_h * _frame_w);

	return 0;
}

int PixFmtConverter::vega_bqb_sniv210_to_yuv422p10le(void *in_buf[], void *out_buf[])
{
	pxxc_convert2(_pxxc, in_buf, _src_stride, out_buf, converted_buf_stride);

	return 0;
}

int PixFmtConverter::vega_bqb_sniv210_to_yuv420p10le(void *in_buf[], void *out_buf[])
{
	int32_t i;

	pxxc_convert2(_pxxc, in_buf, _src_stride, out_buf, converted_buf_stride);

	for (i = 1; i < (_frame_h / 2); i++)
	{
		memcpy((uint8_t *)out_buf[1] + i * _frame_w, (uint8_t *)out_buf[1] + i * _frame_w * 2, _frame_w);
		memcpy((uint8_t *)out_buf[2] + i * _frame_w, (uint8_t *)out_buf[2] + i * _frame_w * 2, _frame_w);
	}

	return 0;
}

int PixFmtConverter::vega_bqb_sniv210_to_sniv210(void *in_buf[], void *out_buf[])
{
	memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], (_frame_h * _frame_w * 4) / 3);
	memcpy((uint8_t *)out_buf[1], (uint8_t *)in_buf[1], (_frame_h * _frame_w * 4) / 3);
	return 0;
}
