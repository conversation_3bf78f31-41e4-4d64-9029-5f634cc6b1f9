/*
 *
 * Copyright (C) 2017 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#pragma once

#include "../common/common.h"
#include "../vegaff.h"

#include <libvega_bqb_api/pxxc.h>

class PixFmtConverter
{
public:
	PixFmtConverter();
	~PixFmtConverter();

	int32_t do_configure(int32_t width, int32_t height, vega_bqb_pix_fmt_t from_fmt, vega_bqb_pix_fmt_t to_fmt);
	int32_t do_convert(void *in_buf[], void *out_buf[]);

	int32_t converted_buf_stride[3];

private:
	int32_t vega_bqb_nv16_to_yuv420p(void *in_buf[], void *out_buf[]);
	int32_t vega_bqb_nv16_to_nv12(void *in_buf[], void *out_buf[]);
	int32_t vega_bqb_nv16_to_yuv422p(void *in_buf[], void *out_buf[]);
	int32_t vega_bqb_nv16_to_nv16(void *in_buf[], void *out_buf[]);
	int32_t vega_bqb_sniv210_to_yuv422p10le(void *in_buf[], void *out_buf[]);
	int32_t vega_bqb_sniv210_to_yuv420p10le(void *in_buf[], void *out_buf[]);
	int32_t vega_bqb_sniv210_to_sniv210(void *in_buf[], void *out_buf[]);
	int32_t (PixFmtConverter::*convert_func)(void *in_buf[], void *out_buf[]);

	int32_t            _frame_w;
	int32_t            _frame_h;
	int32_t            _src_stride[3];
	vega_bqb_pix_fmt_t _from_fmt;
	vega_bqb_pix_fmt_t _to_fmt;
	pxxc_t *           _pxxc;
};


