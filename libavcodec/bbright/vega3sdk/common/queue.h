/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#pragma once

#include <mutex>
#include <condition_variable>
#include <deque>

template <typename T> class Queue
{
public:
	bool is_last_ES;
	Queue()
	{
		is_last_ES = false;
	}

	Queue(Queue<T> const &q) : _mutex(), _condition(), _deq(q._deq)
	{
		is_last_ES = q.is_last_ES;
	}

	virtual void push(T const &data)
	{
		std::unique_lock<std::mutex> lock(_mutex);
		_deq.push_front(data);
		_condition.notify_one();
	}

	virtual T pop()
	{
		std::unique_lock<std::mutex> lock(_mutex);
		_condition.wait(lock, [=] { return !_deq.empty(); });
		//T t(move(_deq.back()));
		T t = _deq.back();
		_deq.pop_back();
		return t;
	}

	virtual size_t size()
	{
		std::unique_lock<std::mutex> lock(_mutex);
		return _deq.size();
	}

	virtual bool empty()
	{
		std::unique_lock<std::mutex> lock(_mutex);
		return _deq.empty();
	}

private:
	std::mutex              _mutex;
	std::condition_variable _condition;
	std::deque<T>           _deq;
};
