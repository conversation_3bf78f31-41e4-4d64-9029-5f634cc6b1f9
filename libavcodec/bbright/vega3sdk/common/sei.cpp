/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "../common/common.h"
#include "../common/sei.h"
#include "../encode/encoder265.h"

/* vega_bqb_hevc's identifying GUID */
const uint8_t SEIuserDataUnregistered::_uuid_iso_iec_11578[16] = {
	0xDC, 0x45, 0xE9, 0xBD, 0xE9, 0xD9, 0x48, 0xB7, 0x96, 0x2C, 0xD8, 0x20, 0xD9, 0x23, 0xEE, 0xEF,
};

int SEI::write(void *p)
{
	if (!p)
		return -1;

	return 0;
}

int SEIuserDataRegistered::write(void *p)
{
	if (!p)
		return -1;

	API_VEGA_BQB_SEI_PARAM_T *pSei = static_cast<API_VEGA_BQB_SEI_PARAM_T *>(p);
	memset(pSei, 0, sizeof(API_VEGA_BQB_SEI_PARAM_T));

	if (VEGA_BQB_ENC_MakeSeiParam(pSei, API_VEGA_BQB_SEI_PAYLOAD_LOC_PICTURE,
								  (API_VEGA_BQB_SEI_PAYLOAD_TYPE_E)payloadType(),
							  _userDataLength, _userData))
		return -1;

	return 0;
}

int SEIuserDataUnregistered::write(void *p)
{
	if (!p)
		return -1;

	API_VEGA_BQB_SEI_PARAM_T *pSei = static_cast<API_VEGA_BQB_SEI_PARAM_T *>(p);
	memset(pSei, 0, sizeof(API_VEGA_BQB_SEI_PARAM_T));

	if (VEGA_BQB_ENC_MakeSeiParam(pSei, API_VEGA_BQB_SEI_PAYLOAD_LOC_PICTURE,
								  (API_VEGA_BQB_SEI_PAYLOAD_TYPE_E)payloadType(),
							  _userDataLength, _userData))
		return -1;

	return 0;
}

bool SEIMasteringDisplayColorVolume::parse(const char *value)
{
	return sscanf(value, "G[%hu,%hu]B[%hu,%hu]R[%hu,%hu]WP[%hu,%hu]L[%u,%u]", &displayPrimaryX[0], &displayPrimaryY[0],
				  &displayPrimaryX[1], &displayPrimaryY[1], &displayPrimaryX[2], &displayPrimaryY[2], &whitePointX,
				  &whitePointY, &maxDisplayMasteringLuminance, &minDisplayMasteringLuminance) == 10;
}

int SEIMasteringDisplayColorVolume::write(void *p)
{
	if (!p)
		return -1;

	vega_bqb_hevc_encoder *         encoder = static_cast<vega_bqb_hevc_encoder *>(p);
	API_VEGA_BQB_HEVC_INIT_PARAM_T *hp = &encoder->_apiInitParam->tHevcParam;
	hp->tHdrConfig.u8MasteringDisplayColourVolumeLocation = 2;
	hp->tHdrConfig.u16DisplayPrimariesX0 = displayPrimaryX[0];
	hp->tHdrConfig.u16DisplayPrimariesY0 = displayPrimaryY[0];
	hp->tHdrConfig.u16DisplayPrimariesX1 = displayPrimaryX[1];
	hp->tHdrConfig.u16DisplayPrimariesY1 = displayPrimaryY[1];
	hp->tHdrConfig.u16DisplayPrimariesX2 = displayPrimaryX[2];
	hp->tHdrConfig.u16DisplayPrimariesY2 = displayPrimaryY[2];
	hp->tHdrConfig.u16WhitePointX = whitePointX;
	hp->tHdrConfig.u16WhitePointY = whitePointY;
	hp->tHdrConfig.u32MaxDisplayMasteringLuminance = maxDisplayMasteringLuminance;
	hp->tHdrConfig.u32MinDisplayMasteringLuminance = minDisplayMasteringLuminance;

	return 0;
}

int SEIContentLightLevel::write(void *p)
{
	if (!p)
		return -1;

	vega_bqb_hevc_encoder *         encoder = static_cast<vega_bqb_hevc_encoder *>(p);
	vega_bqb_hevc_param *           param = (vega_bqb_hevc_param *)encoder->_param;
	API_VEGA_BQB_HEVC_INIT_PARAM_T *hp = &encoder->_apiInitParam->tHevcParam;
	hp->tHdrConfig.bEnable = 1;
	hp->tHdrConfig.u8LightContentLocation = 0;
	hp->tHdrConfig.u16MaxContentLightLevel = param->maxCLL;
	hp->tHdrConfig.u16MaxPictureAvgLightLevel = param->maxFALL;
	hp->tHdrConfig.u8AlternativeTransferCharacteristicsLocation = 1;
	//	hp->tHdrConfig.eAlternativeTransferCharacteristics = API_VEGA_BQB_TRANSFER_CHAR_BT2020_10;
	hp->tHdrConfig.eAlternativeTransferCharacteristics =
		(API_VEGA_BQB_TRANSFER_CHAR_E)param->vui.transferCharacteristics;

	return 0;
}
