/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#pragma once

#include <stdint.h>

class SEI
{
public:
	virtual int write(void *);
	virtual ~SEI()
	{
	}

protected:
	enum PayloadType
	{
		BUFFERING_PERIOD = 0,
		PICTURE_TIMING = 1,
		PAN_SCAN_RECT = 2,
		FILLER_PAYLOAD = 3,
		USER_DATA_REGISTERED_ITU_T_T35 = 4,
		USER_DATA_UNREGISTERED = 5,
		RECOVERY_POINT = 6,
		SCENE_INFO = 9,
		FULL_FRAME_SNAPSHOT = 15,
		PROGRESSIVE_REFINEMENT_SEGMENT_START = 16,
		PROGRESSIVE_REFINEMENT_SEGMENT_END = 17,
		FILM_GRAIN_CHARACTERISTICS = 19,
		POST_FILTER_HINT = 22,
		TONE_MAPPING_INFO = 23,
		FRAME_PACKING = 45,
		DISPLAY_ORIENTATION = 47,
		SOP_DESCRIPTION = 128,
		ACTIVE_PARAMETER_SETS = 129,
		DECODING_UNIT_INFO = 130,
		TEMPORAL_LEVEL0_INDEX = 131,
		DECODED_PICTURE_HASH = 132,
		SCALABLE_NESTING = 133,
		REGION_REFRESH_INFO = 134,
		NO_DISPLAY = 135,
		TIME_CODE = 136,
		MASTERING_DISPLAY_INFO = 137,
		SEGM_RECT_FRAME_PACKING = 138,
		TEMP_MOTION_CONSTRAINED_TILE_SETS = 139,
		CHROMA_SAMPLING_FILTER_HINT = 140,
		KNEE_FUNCTION_INFO = 141,
		CONTENT_LIGHT_LEVEL_INFO = 144
	};

	virtual PayloadType payloadType() const = 0;
};

class SEIuserDataRegistered : public SEI
{
public:
	uint8_t  _userDataLength;
	uint8_t *_userData;

	SEIuserDataRegistered() : _userData(NULL)
	{
	}

	PayloadType payloadType() const
	{
		return USER_DATA_REGISTERED_ITU_T_T35;
	}

	int write(void *p);
};

class SEIuserDataUnregistered : public SEI
{
public:
	static const uint8_t _uuid_iso_iec_11578[16];
	uint8_t              _userDataLength;
	uint8_t *            _userData;

	SEIuserDataUnregistered() : _userData(NULL)
	{
	}

	PayloadType payloadType() const
	{
		return USER_DATA_UNREGISTERED;
	}

	int write(void *p);
};

class SEIMasteringDisplayColorVolume : public SEI
{
public:
	uint16_t displayPrimaryX[3];
	uint16_t displayPrimaryY[3];
	uint16_t whitePointX, whitePointY;
	uint32_t maxDisplayMasteringLuminance;
	uint32_t minDisplayMasteringLuminance;

	PayloadType payloadType() const
	{
		return MASTERING_DISPLAY_INFO;
	}

	bool parse(const char *value);
	int write(void *p);
};

class SEIContentLightLevel : public SEI
{
public:
	uint16_t max_content_light_level;
	uint16_t max_pic_average_light_level;

	PayloadType payloadType() const
	{
		return CONTENT_LIGHT_LEVEL_INFO;
	}

	int write(void *p);
};
