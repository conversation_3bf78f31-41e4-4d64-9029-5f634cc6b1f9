#include <string.h>
#include <stdlib.h>
#include "ts_extent.h"

void ts_extent_reset(ts_extent_t *tsext, int negative_initial)
{
	tsext->s64_ext_ts = 0;
	tsext->high_carry_bits = 0;
	tsext->flags = 0;
	if (negative_initial)
		tsext->flags |= (1 << 1);
}

int64_t ts_extent_incrstep(ts_extent_t *tsext, int64_t src_ts)
{
	int64_t trimmed_ts;
	int64_t return_ts;
	int64_t overflow_ts;
	int64_t underflow_ts;
	int64_t absdiff[3];
	int64_t mindiff;

#define TS_EXTENT_MASK_LOW33  ((1ULL << 33ULL) - 1ULL)
#define TS_EXTENT_HIGH_BIT    ((1ULL << 32ULL))
#define TS_EXTENT_CARRY_BIT   ((1ULL << 33ULL))
#define TS_EXTENT_SIGN_EXT(x) ((((x)&TS_EXTENT_HIGH_BIT) == 0) ? (x) : ((~TS_EXTENT_MASK_LOW33) | (x)))

	trimmed_ts = src_ts & TS_EXTENT_MASK_LOW33;
	if ((tsext->flags & 1) == 0)
	{ // initial state: take the 1st TS
		tsext->high_carry_bits = 0;

		if ((tsext->flags & 2) != 0)
		{ // if TS starts from negative
			if (src_ts < 0)
				tsext->high_carry_bits = (~TS_EXTENT_MASK_LOW33);
		}
	}

	// set default return TS
	return_ts = trimmed_ts + tsext->high_carry_bits;
	underflow_ts = TS_EXTENT_SIGN_EXT(trimmed_ts) + tsext->high_carry_bits;
	overflow_ts = (src_ts & TS_EXTENT_MASK_LOW33) + TS_EXTENT_CARRY_BIT + tsext->high_carry_bits;

	if ((tsext->flags & 1) != 0)
	{
		absdiff[0] = llabs(overflow_ts - tsext->s64_ext_ts);
		absdiff[1] = llabs(return_ts - tsext->s64_ext_ts);
		absdiff[2] = llabs(underflow_ts - tsext->s64_ext_ts);

		// take the value with minimal distance to last TS.
		if (absdiff[0] < absdiff[1])
		{
			mindiff = absdiff[0];
			return_ts = overflow_ts;
		}
		else
			mindiff = absdiff[1];

		if (absdiff[2] < mindiff)
		{
			return_ts = underflow_ts;
		}
	}
	tsext->flags |= 1;
	tsext->s64_ext_ts = return_ts;
	tsext->high_carry_bits = tsext->s64_ext_ts & (~TS_EXTENT_MASK_LOW33);
	return tsext->s64_ext_ts;
}
