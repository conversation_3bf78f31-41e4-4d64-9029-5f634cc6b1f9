#pragma once

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct _ts_extent_t
{
	int64_t s64_ext_ts;
	// Bit-0: 1 modified; 0 untocuhed
	// Bit-1: 1 allow negative initial value.	//
	unsigned flags;
	int64_t  high_carry_bits;
} ts_extent_t;

/// @brief Reset TS-extent structure. Should calling before following the timestamp.
/// @param tsext Internal structure.
extern void ts_extent_reset(ts_extent_t *tsext, int negative_initial);

/// @brief Following a increased 33-bits timestamp.
/// @param tsext Internal structure.
/// @param incoming_s33ts Next 333-bits timestamp.
/// @return Return the 64-bits extented timestamp.
extern int64_t ts_extent_incrstep(ts_extent_t *tsext, int64_t incoming_s33ts);

#ifdef __cplusplus
}
#endif
