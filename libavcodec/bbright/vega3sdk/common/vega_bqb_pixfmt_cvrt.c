/*
 *
 * Copyright (C) 2017 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */


#include <libvega_bqb_api/pxxc.h>
#include "libavutil/mem.h"
#include "libavutil/avutil.h"
#include "libswscale/swscale.h"
#include "vega_bqb_pixfmt_cvrt.h"

#define ALIGN64(x)      (((x) + 63) & ~63)

#if defined(_MSC_VER)
#define RESTRICT 
#else
#define RESTRICT __restrict__
#endif


struct VegaBqbPixfmtConverter_S {
    int32_t _frame_w;
    int32_t _frame_h;
    int32_t _src_stride[4];
    int32_t _dst_stride[4];

    pxxc_t *_pxxc;
    struct SwsContext *_sws_ctx;
    int32_t (*convert_func)(VegaBqbPixfmtConverter *cvrt, void *in_buf[],
                            void *out_buf[]);
};

static int32_t vega_bqb_nv16_to_yuv420p(VegaBqbPixfmtConverter *cvrt,
                                        void *in_buf[], void *out_buf[]);

static int32_t vega_bqb_nv16_to_nv12(VegaBqbPixfmtConverter *cvrt,
                                     void *in_buf[], void *out_buf[]);

static int32_t vega_bqb_nv16_to_yuv422p(VegaBqbPixfmtConverter *cvrt,
                                        void *in_buf[], void *out_buf[]);

static int32_t vega_bqb_nv12_to_yuv420p(VegaBqbPixfmtConverter *cvrt,
                                     void *in_buf[], void *out_buf[]);

static int32_t vega_bqb_sniv210_to_yuv422p10le(VegaBqbPixfmtConverter *cvrt,
                                               void *in_buf[], void *out_buf[]);

static int32_t vega_bqb_sniv010_to_yuv420p10le(VegaBqbPixfmtConverter *cvrt,
                                               void *in_buf[], void *out_buf[]);

static int32_t _alloc_sws_ctx(VegaBqbPixfmtConverter *cvrt,
                                enum AVPixelFormat from_fmt,
                                enum AVPixelFormat to_fmt)
{
    int32_t w, h;

    if (!cvrt)
        return AVERROR_INVALIDDATA;

    w = cvrt->_frame_w;
    h = cvrt->_frame_h;

    cvrt->_sws_ctx = sws_getContext(w, h, from_fmt, w, h, to_fmt,
            0, NULL, NULL, NULL);

    if (!cvrt->_sws_ctx) {
        av_log(NULL, AV_LOG_ERROR,
                "PixFmtConverter fail to create sws_ctx for the conversion\n");
        return AVERROR(ENOMEM);
    }

    return 0;
}

int32_t vega_bqb_pixfmt_cvrt_destroy(VegaBqbPixfmtConverter* cvrt)
{
    if (!cvrt)
        return 0;

    if (cvrt->_pxxc)
        pxxc_destroy(cvrt->_pxxc);

    if (cvrt->_sws_ctx)
        sws_freeContext(cvrt->_sws_ctx);

    av_freep(&cvrt);

    return 0;
}

int32_t vega_bqb_pixfmt_cvrt_do_convert(VegaBqbPixfmtConverter *cvrt,
                                        void *in_buf[], void *out_buf[])
{
    if (!cvrt || !in_buf || !out_buf)
        return AVERROR_INVALIDDATA;

    if (!cvrt->convert_func) {
        av_log(NULL, AV_LOG_ERROR, "PixFmtConverter didn't configure yet\n");
        return AVERROR_BUG;
    }

    cvrt->convert_func(cvrt, in_buf, out_buf);

    return 0;
}

VegaBqbPixfmtConverter *vega_bqb_pixfmt_cvrt_create(int32_t width,
                                                    int32_t height,
                                                    enum AVPixelFormat from_fmt,
                                                    enum AVPixelFormat to_fmt)
{
    VegaBqbPixfmtConverter *cvrt = NULL;

    if (!width || !height)
        goto FAIL;

    cvrt = av_mallocz(sizeof(VegaBqbPixfmtConverter));
    if (!cvrt)
    {
        av_log(NULL, AV_LOG_ERROR, "PixFmtConverter create fail\n");
        return NULL;
    }

    cvrt->_frame_w = width;
    cvrt->_frame_h = height;

    memset(cvrt->_src_stride, 0, sizeof(int32_t) * 4);
    memset(cvrt->_dst_stride, 0, sizeof(int32_t) * 4);

    if (from_fmt == AV_PIX_FMT_NV16 && to_fmt == AV_PIX_FMT_NV12) {
        cvrt->convert_func = &vega_bqb_nv16_to_nv12;
    } else if (from_fmt == AV_PIX_FMT_NV16 && to_fmt == AV_PIX_FMT_YUV420P) {
        cvrt->convert_func = &vega_bqb_nv16_to_yuv420p;
    } else if (from_fmt == AV_PIX_FMT_NV16 && to_fmt == AV_PIX_FMT_YUV422P) {
        cvrt->convert_func = &vega_bqb_nv16_to_yuv422p;
    } else if (from_fmt == AV_PIX_FMT_NV12 && to_fmt == AV_PIX_FMT_YUV420P) {
        if (_alloc_sws_ctx(cvrt, from_fmt, to_fmt))
            goto FAIL;

        cvrt->_src_stride[0] = cvrt->_src_stride[1] = width;
        cvrt->_dst_stride[0] = width;
        cvrt->_dst_stride[1] = cvrt->_dst_stride[2] = width / 2;

        cvrt->convert_func = &vega_bqb_nv12_to_yuv420p;
    // } else if (from_fmt == AV_PIX_FMT_SNIV010 &&
    //            to_fmt == AV_PIX_FMT_YUV420P10LE) {
    //     cvrt->_pxxc = pxxc_create(cvrt->_frame_w, cvrt->_frame_h, PXX_SNIV010, PXX_YUV420P10LE);

    //     cvrt->_src_stride[0] = cvrt->_src_stride[1] = ALIGN64((width * 16 / 12));
    //     cvrt->_dst_stride[0] = width * 2;
    //     cvrt->_dst_stride[1] = cvrt->_dst_stride[2] = width;

    //     cvrt->convert_func = &vega_bqb_sniv010_to_yuv420p10le;
    // } else if (from_fmt == AV_PIX_FMT_SNIV210 &&
    //            to_fmt == AV_PIX_FMT_YUV422P10LE) {
    //     cvrt->_pxxc = pxxc_create(cvrt->_frame_w, cvrt->_frame_h, PXX_SNIV210, PXX_YUV422P10LE);

    //     cvrt->_src_stride[0] = cvrt->_src_stride[1] = ALIGN64((width * 16 / 12));
    //     cvrt->_dst_stride[0] = width * 2;
    //     cvrt->_dst_stride[1] = cvrt->_dst_stride[2] = width;

    //     cvrt->convert_func = &vega_bqb_sniv210_to_yuv422p10le;
    } else
        goto FAIL;

    return cvrt;

FAIL:
    if (cvrt)
        av_freep(&cvrt);

    av_log(NULL, AV_LOG_ERROR,
           "PixFmtConverter configuration parameters not correct\n");

    return NULL;
}


static int32_t vega_bqb_nv16_to_yuv420p(VegaBqbPixfmtConverter *cvrt,
                                        void *in_buf[], void *out_buf[])
{
    uint8_t *u_buf_ptr = (uint8_t *)out_buf[1];
    uint8_t *v_buf_ptr = (uint8_t *)out_buf[2];
    uint8_t *uv_ptr = (uint8_t *)in_buf[1];
    int32_t i;
    int32_t j;

    memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], cvrt->_frame_h * cvrt->_frame_w);

    for (i = 0; i < cvrt->_frame_h; i++) {
        /* vertical downsampling */
        if (i % 2) {
            for (j = 0; j < cvrt->_frame_w; j += 2) {
                *(u_buf_ptr++) = *(uv_ptr + j);
                *(v_buf_ptr++) = *(uv_ptr + j + 1);
            }
            uv_ptr += cvrt->_frame_w * 2;
        }
    }

    return 0;
}

static int32_t vega_bqb_nv16_to_nv12(VegaBqbPixfmtConverter *cvrt,
                                     void *in_buf[], void *out_buf[])
{
    uint8_t *uv16_buf_ptr = (uint8_t *)in_buf[1];
    uint8_t *uv12_buf_ptr = (uint8_t *)out_buf[1];
    int32_t i;

    memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], cvrt->_frame_h * cvrt->_frame_w);

    for (i = 0; i < cvrt->_frame_h; i++) {
        /* vertical downsampling */
        if (i % 2) {
            memcpy(uv12_buf_ptr, uv16_buf_ptr, cvrt->_frame_w);
            uv12_buf_ptr += cvrt->_frame_w;
        }
        uv16_buf_ptr += cvrt->_frame_w;
    }

    return 0;
}

#if !defined(_MSC_VER)
	#pragma GCC push_options
	#pragma GCC optimize ("O3")
#endif /* !defined(_MSC_VER) */
static int32_t vega_bqb_nv16_to_yuv422p(VegaBqbPixfmtConverter *cvrt,
                                        void *in_buf[], void *out_buf[])
{
#if 0
    uint8_t *u_buf_ptr = (uint8_t *)out_buf[1];
    uint8_t *v_buf_ptr = (uint8_t *)out_buf[2];
    uint8_t *uv_ptr = (uint8_t *)in_buf[1];
    int32_t i;

    memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], cvrt->_frame_h * cvrt->_frame_w);

    for (i = 0; i < cvrt->_frame_h * cvrt->_frame_w; i++) {
        if ((i % 2) == 0)
            *u_buf_ptr++ = *(uv_ptr + i);
        else
            *v_buf_ptr++ = *(uv_ptr + i);
    }
#else
	
#if !defined(_MSC_VER)
    uint8_t * RESTRICT u_buf_ptr =  __builtin_assume_aligned(out_buf[1], 32);
    uint8_t * RESTRICT v_buf_ptr =  __builtin_assume_aligned(out_buf[2], 32);
    uint8_t * RESTRICT uv_ptr =  __builtin_assume_aligned(in_buf[1], 32);
#else
	__declspec(align(32)) uint8_t* u_buf_ptr = out_buf[1];
	__declspec(align(32)) uint8_t* v_buf_ptr = out_buf[2];
	__declspec(align(32)) uint8_t* uv_ptr = in_buf[1];
#endif	
    int32_t end = cvrt->_frame_w * cvrt->_frame_h;

    memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], end);

    while (end) {
        *u_buf_ptr++ = *uv_ptr++;
        *v_buf_ptr++ = *uv_ptr++;
        end -= 2;
    }
#endif

    return 0;
}
#if !defined(_MSC_VER)
#pragma GCC pop_options
#endif
static int32_t vega_bqb_nv12_to_yuv420p(VegaBqbPixfmtConverter *cvrt,
                                        void *in_buf[], void *out_buf[])
{
#if 1

#if !defined(_MSC_VER)
    uint8_t * RESTRICT u_buf_ptr =  __builtin_assume_aligned(out_buf[1], 32);
    uint8_t * RESTRICT v_buf_ptr =  __builtin_assume_aligned(out_buf[2], 32);
    uint8_t * RESTRICT uv_ptr =  __builtin_assume_aligned(in_buf[1], 32);
#else
    __declspec(align(32)) uint8_t* u_buf_ptr = out_buf[1];
    __declspec(align(32)) uint8_t* v_buf_ptr = out_buf[2];
    __declspec(align(32)) uint8_t* uv_ptr = in_buf[1];	
#endif
    int32_t end = cvrt->_frame_w * cvrt->_frame_h / 2;

    memcpy((uint8_t *)out_buf[0], (uint8_t *)in_buf[0], cvrt->_frame_h * cvrt->_frame_w);

    while (end) {
        *u_buf_ptr++ = *uv_ptr++;
        *v_buf_ptr++ = *uv_ptr++;
        end -= 2;
    }
#else
    sws_scale(cvrt->_sws_ctx, (const uint8_t *const *)in_buf,
            cvrt->_src_stride, 0, cvrt->_frame_h, (uint8_t *const *)out_buf, cvrt->_dst_stride);
#endif

    return 0;
}

static int32_t vega_bqb_sniv210_to_yuv422p10le(VegaBqbPixfmtConverter *cvrt,
                                               void *in_buf[], void *out_buf[])
{
    pxxc_convert2(cvrt->_pxxc, in_buf, cvrt->_src_stride, out_buf, cvrt->_dst_stride);

    return 0;
}

static int32_t vega_bqb_sniv010_to_yuv420p10le(VegaBqbPixfmtConverter *cvrt,
                                               void *in_buf[], void *out_buf[])
{
    pxxc_convert2(cvrt->_pxxc, in_buf, cvrt->_src_stride, out_buf, cvrt->_dst_stride);

    return 0;
}
