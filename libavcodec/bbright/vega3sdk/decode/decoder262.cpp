/*
 *
 * Copyright (C) 2017 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following condition *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#define __STDC_FORMAT_MACROS
#elif _WIN32
#include <Windows.h>
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#define __STDC_FORMAT_MACROS
#else
#endif
//#include <inttypes.h>

#include "decoder262.h"
#include "../common/common.h"

#include <libvega_bqb_api/apiVDEC.h>

using namespace std;

extern "C" void vega_bqb_mpeg_process_vraw(API_VDEC_IMG_T vraw, void *args);

vega_bqb_decoder_base::vega_bqb_decoder_base()
{
	int i;

	for (i = 0; i < 3; i++)
		_convertedPicPlanes[i] = NULL;
	for (i = 0; i < 3; i++)
		_rawPicBuf[i] = NULL;

	_aborted = false;
	_apiInitParam = NULL;
	_called_stop = false;
	_channel = (API_VDEC_CHN_E)0;
	_decoder_stopped = false;
	_device = (API_VDEC_DEVICE_E)0;
	_direct_copy = false;
	_eof = false;
	_ffmpeg_quit = false;
	_got_frame_cnt = 0;
	_includePTS = false;
	_lastVraw = false;
	_sent_frame_cnt = 0;
	_vrawCbCrData = NULL;
	_vrawYData = NULL;
	_picHeight = 0;
	_picWidth = 0;
	_pushPacketData = NULL;
	_param = NULL;
}

bool vega_bqb_decoder_base::create(const vegaff_codec_param *arg_param)
{
	int32_t i;
	int32_t buf_size[3];
	int32_t mul = 1;

	if (!arg_param)
		goto fail;

	_param = vegaff_memdupT<vegaff_codec_param>(arg_param);
	if (!_param)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "init parameter allocation failure, aborting\n");
		goto fail;
	}
	_picWidth = _param->sourceWidth;
	_picHeight = _param->sourceHeight;

	if (_picWidth == 1920 && _picHeight == 1088)
		_picHeight = 1080;

	if (_param->pix_fmt == VEGA_BQB_PIX_FMT_YUV420P10LE || _param->pix_fmt == VEGA_BQB_PIX_FMT_YUV422P10LE ||
		_param->pix_fmt == VEGA_BQB_PIX_FMT_V210)
	{
		mul = 2;
	}

	_rawPicSize_Y = _picWidth * _picHeight * mul;
	_rawPicSize_UV = _picWidth * _picHeight * mul / 2;
	buf_size[0] = _rawPicSize_Y * MAX_FRAME_NUMS_IN_BUF;
	buf_size[1] = _rawPicSize_UV * MAX_FRAME_NUMS_IN_BUF;
	buf_size[2] = buf_size[1];

	_apiInitParam = (API_VDEC_INIT_PARAM_T *)vegaff_malloc(sizeof(API_VDEC_INIT_PARAM_T));

	if (!_apiInitParam)
	{
		vegaff_log(arg_param, VEGA_BQB_LOG_ERROR, "init parameter allocation failure, aborting\n");
		goto fail;
	}

	memset(_apiInitParam, 0, sizeof(API_VDEC_INIT_PARAM_T));

	for (i = 0; i < 3; i++)
	{
		_convertedPicPlanes[i] = new FiFoBuffer(buf_size[i]);

		if (!_convertedPicPlanes[i])
		{
			vegaff_log(arg_param, VEGA_BQB_LOG_ERROR, "covertedPicPlanes alloc failure, aborting\n");
			goto fail;
		}
	}

	for (i = 0; i < 3; i++)
	{
		_rawPicBuf[i] = vegaff_malloc(_rawPicSize_Y);
		if (!_rawPicBuf[i])
		{
			vegaff_log(arg_param, VEGA_BQB_LOG_ERROR, "temp buf for fmt convert alloc failure, aborting\n");
			goto fail;
		}
	}

	_vrawYData = new FiFoBuffer((_rawPicSize_Y + SNI_INVALID_DATA_SIZE) * MAX_FRAME_NUMS_IN_BUF);
	if (!_vrawYData)
	{
		goto fail;
	}

	_vrawCbCrData = new FiFoBuffer((_picWidth * _picHeight * mul + SNI_INVALID_DATA_SIZE) * MAX_FRAME_NUMS_IN_BUF);
	if (!_vrawCbCrData)
	{
		goto fail;
	}

	_pushPacketData = new FiFoBuffer(API_ES_READ_FRAME_SIZE * 10);
	if (!_pushPacketData)
	{
		goto fail;
	}

	return true;

fail:
	_aborted = true;
	return false;
}

void vega_bqb_decoder_base::configure(const vegaff_codec_param *arg_param)
{
	int i;

	if (_aborted)
		return;

	if (arg_param)
	{
		vegaff_copyT(_param, arg_param);
	}
	_device = (API_VDEC_DEVICE_E)_param->device;
	_channel = (API_VDEC_CHN_E)_param->channel;
	_apiInitParam->eCodecType = API_VDEC_CODEC_TYPE_MPEG2;

	_apiInitParam->eOutputFps = (API_VDEC_FPS_E)(_param->fpsNum / _param->fpsDenom);

	_apiInitParam->eInputMode = (API_VDEC_INPUT_MODE_E)_param->inputMode;
	_apiInitParam->eOutputPath = (API_VDEC_VIDEO_OUTPUT_PATH_E)_param->outputPath;
	_apiInitParam->eOutputFmt = (API_VDEC_VIDEO_OUTPUT_FORMAT_E)_param->outputFormat;
	_apiInitParam->eInputFileMode = API_VDEC_INPUT_FILE_ES;

	_apiInitParam->u32OutputBufSizeY = _picWidth * _picHeight * 16 / 12 + SNI_INVALID_DATA_SIZE;
	_apiInitParam->u32OutputBufSizeC = _picWidth * _picHeight * 16 / 12 + SNI_INVALID_DATA_SIZE;

	if (_param->sourceWidth == 4096 && _param->sourceHeight == 2160)
		_apiInitParam->eResolution = API_VDEC_RESOLUTION_4096x2160;
	else if (_param->sourceWidth == 3840 && _param->sourceHeight == 2160)
		_apiInitParam->eResolution = API_VDEC_RESOLUTION_3840x2160;
	else if (_param->sourceWidth == 1920 && _param->sourceHeight == 1080)
		_apiInitParam->eResolution = API_VDEC_RESOLUTION_1920x1080;
	else if (_param->sourceWidth == 1280 && _param->sourceHeight == 720)
		_apiInitParam->eResolution = API_VDEC_RESOLUTION_1280x720;
	else if (_param->sourceWidth == 720 && _param->sourceHeight == 480)
		_apiInitParam->eResolution = API_VDEC_RESOLUTION_720x480;

	if (_param->pix_fmt == VEGA_BQB_PIX_FMT_YUV420P10LE || _param->pix_fmt == VEGA_BQB_PIX_FMT_YUV422P10LE ||
		_param->pix_fmt == VEGA_BQB_PIX_FMT_V210)
	{
		_apiInitParam->eOutputBitDepth = API_VDEC_BIT_DEPTH_10;
		_pixfmtConverter.do_configure(_picWidth, _picHeight, VEGA_BQB_PIX_FMT_V210, _param->pix_fmt);
		if (VEGA_BQB_PIX_FMT_V210 == _param->pix_fmt)
		{
			_direct_copy = true;
		}
	}
	else
	{
		_apiInitParam->eOutputBitDepth = API_VDEC_BIT_DEPTH_8;
		_pixfmtConverter.do_configure(_picWidth, _picHeight, VEGA_BQB_PIX_FMT_NV16, _param->pix_fmt);
		if (VEGA_BQB_PIX_FMT_NV16 == _param->pix_fmt)
		{
			_direct_copy = true;
		}
	}

	/* Please note the YUV420P10LE in pxxc must be converted from YUV422P10LE
	 * So we need to reserve the convertedFrameSize same for both
	 */
	for (i = 0; i < 3; i++)
		_convertedFrameSize[i] = _pixfmtConverter.converted_buf_stride[i] * _picHeight;
}

void vega_bqb_mpeg_decoder::configure(const vegaff_codec_param *arg_param)
{
	vega_bqb_decoder_base::configure(arg_param);
	if (_apiInitParam)
	{
		_apiInitParam->eCodecType = API_VDEC_CODEC_TYPE_MPEG2;
	}
}

void vega_bqb_decoder_base::init()
{
	if (_aborted)
		return;

	API_VDEC_STATUS_E st = Api_VDEC_GetStatus(_device, _channel);
	if (st != API_VDEC_STATUS_OFF)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) VEGA_BQB decoder not OFF\n", _device, _channel);
		return;
	}

	Api_VDEC_SetDbgMsgLevel(_device, _channel, (API_VDEC_DBG_LEVEL_E)_param->dbgLevel);

	if (Api_VDEC_Init(_device, _channel, _apiInitParam))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) unable to init VEGA_BQB decoder\n", _device, _channel);
		_aborted = true;
		return;
	}

	_pixfmtConverterThread = thread(&vega_bqb_decoder_base::pixfmtConverterThreadMain, this);
#ifndef _WIN32
	pthread_setname_np(_pixfmtConverterThread.native_handle(), "pixfmt_cvrt");
#endif
	_pushPacketThread = thread(&vega_bqb_decoder_base::pushPacketThreadMain, this);
#ifndef _WIN32
	pthread_setname_np(_pushPacketThread.native_handle(), "push_pkts");
#endif
}

void vega_bqb_mpeg_decoder::registVrawPopCallback()
{
	if (_aborted)
		return;

	if (_param->outputPath == API_VDEC_VIDEO_OUTPUT_PATH_DATA)
	{
		API_VDEC_STATUS_E st = Api_VDEC_GetStatus(_device, _channel);

		if (st != API_VDEC_STATUS_STANDBY)
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) VEGA_BQB decoder not STANDBY\n", _device, _channel);
			return;
		}

		if (Api_VDEC_RegisterCallback(_device, _channel, &vega_bqb_mpeg_process_vraw, (void *)this))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) unable to register pop-raw callback function\n", _device,
					   _channel);
			_aborted = true;
			return;
		}
	}
}

void vega_bqb_decoder_base::deregistVrawPopCallback()
{
	if (_param->outputPath == API_VDEC_VIDEO_OUTPUT_PATH_DATA)
	{
		if (Api_VDEC_RegisterCallback(_device, _channel, NULL, NULL))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) unable to deregister pop-raw callback function\n", _device,
					   _channel);
			_aborted = true;
			return;
		}
	}
}

void vega_bqb_decoder_base::start()
{
	if (_aborted)
		return;

	API_VDEC_STATUS_E st = Api_VDEC_GetStatus(_device, _channel);
	if (st != API_VDEC_STATUS_STANDBY)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) VEGA_BQB decoder not STANDBY\n", _device, _channel);
		return;
	}

	if (Api_VDEC_Start(_device, _channel))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) unable to start VEGA_BQB decoder\n", _device, _channel);
		_aborted = true;
		return;
	}
}

int vega_bqb_decoder_base::decode(const vegaff_packet_t *pkt_in)
{
	API_VDEC_AU_T au = {0};

	if (_aborted)
		return -1;

	if (_eof)
		return 0;

	if (pkt_in && pkt_in->data && pkt_in->size)
	{
		_sent_frame_cnt++;

		if (_sent_frame_cnt == 1)
		{
			if (pkt_in->pts == FFMPEG_NOPTS_VALUE)
				_includePTS = false;
			else
				_includePTS = true;
		}

		while (_pushPacketData->fifo_space() <= (uint32_t)pkt_in->size)
		{
			vegaff_log(_param, VEGA_BQB_LOG_DEBUG, "pushPacketQueue full:%lu\n", _pushPacketQueue.size());
			SLEEP_MICROSECOND(12000);

			if (_ffmpeg_quit)
				return 0;
		}
		au.pu8Addr = (uint8_t *)_pushPacketData->wptr;
		_pushPacketData->fifo_generic_write((void *)pkt_in->data, pkt_in->size);

		au.u32Size = pkt_in->size;
		au.u64Pts = (uint64_t)pkt_in->pts;
		au.u64Dts = (uint64_t)pkt_in->dts;
		au.bLast = false;
		au.bIncludeDtsPts = _includePTS;
	}
	else
	{
		au.u32Size = 0;
		au.bLast = true;
		_eof = true;
	}

	_pushPacketQueue.push(au);

	return 0;
}

void vega_bqb_decoder_base::stop()
{
#define MAX_STOP_RETRY_CNT 200
	API_VDEC_STATUS_E st;
	int               loop = 0;

	/* Since we already call stop after sending last frame, what do here is just
	 * to wait decoder transit to standby state */
	do
	{
		st = Api_VDEC_GetStatus(_device, _channel);
		SLEEP_MICROSECOND(50000);
		loop++;

		if (!_called_stop && loop == 50 && st == API_VDEC_STATUS_DECODING)
		{
			_stopDecoderThread = thread(&vega_bqb_decoder_base::stopDecoderThreadMain, this);
		}
	} while (st != API_VDEC_STATUS_STANDBY && loop <= MAX_STOP_RETRY_CNT);

	if (loop > MAX_STOP_RETRY_CNT)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "fail stop (%d:%d) state:%d, loop=%d\n", _device, _channel, int(st), loop);
	}
	else
	{
		_decoder_stopped = true;
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "stop (%d:%d) completed!\n", _device, _channel);
	}

	return;
}

void vega_bqb_decoder_base::exit()
{
	API_VDEC_STATUS_E st = Api_VDEC_GetStatus(_device, _channel);

	if (st != API_VDEC_STATUS_STANDBY)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) decoder in error state[%d]!\n", _device, _channel, int(st));
		return;
	}

	if (_pushPacketThread.joinable())
		_pushPacketThread.join();

	if (_stopDecoderThread.joinable())
		_stopDecoderThread.join();

	if (_pixfmtConverterThread.joinable())
		_pixfmtConverterThread.join();

	if (Api_VDEC_Exit(_device, _channel))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) unable to leave vega decoder\n", _device, _channel);
		return;
	}
}

void vega_bqb_decoder_base::destroy()
{
	int i;

	for (i = 0; i < 3; i++)
	{
		vegaff_free(_rawPicBuf[i]);
		_rawPicBuf[i] = NULL;
	}

	for (i = 0; i < 3; i++)
	{
		delete (_convertedPicPlanes[i]);
		_convertedPicPlanes[i] = NULL;
	}

	vegaff_free(_apiInitParam);

	if (_param)
		vegaff_free(_param);

	if (_vrawYData)
		delete _vrawYData;

	if (_vrawCbCrData)
		delete _vrawCbCrData;

	if (_pushPacketData)
		delete _pushPacketData;

	_aborted = false;
	_apiInitParam = NULL;
	_called_stop = false;
	_channel = (API_VDEC_CHN_E)0;
	_decoder_stopped = false;
	_device = (API_VDEC_DEVICE_E)0;
	_direct_copy = false;
	_eof = false;
	_ffmpeg_quit = false;
	_got_frame_cnt = 0;
	_includePTS = false;
	_lastVraw = false;
	_sent_frame_cnt = 0;
	_vrawCbCrData = NULL;
	_vrawYData = NULL;
	_picHeight = 0;
	_picWidth = 0;
	_pushPacketData = NULL;
	_param = NULL;
}

void vega_bqb_decoder_base::flush()
{
	while (!_videoInfoQueue.empty())
		_videoInfoQueue.pop_front();
}

void vega_bqb_decoder_base::stopDecoderThreadMain(void)
{
	std::lock_guard<std::mutex> lock(_stopperLock);
	API_VDEC_AU_T               au = {0};

	if (_called_stop == false)
	{
		_called_stop = true;
		au.u32Size = 0;
		au.bLast = true;
		au.bIncludeDtsPts = _includePTS;

		Api_VDEC_Push(_device, _channel, &au);
		Api_VDEC_Stop(_device, _channel);
	}
}

void vega_bqb_decoder_base::pixfmtConverterThreadMain(void)
{
	int32_t             i;
	void *              yuv[3];
	vega_bqb_video_info video_info;
	vegaff_picture_t    convertedPic;

	convertedPic.width = _picWidth;
	convertedPic.height = _picHeight;

	for (;;)
	{
		if (_ffmpeg_quit || _decoder_stopped || (_eof && _lastVraw && _videoInfoQueue.empty()))
			break;

		if (_videoInfoQueue.empty())
		{
			SLEEP_MICROSECOND(4000);
			continue;
		}

		video_info = _videoInfoQueue.front();

		if (_vrawYData->fifo_size() >= (video_info.Y_raw_data_size + video_info.Y_invalid_data_size) &&
			_vrawCbCrData->fifo_size() >= (video_info.C_raw_data_size + video_info.C_invalid_data_size))
		{
			for (i = 0; i < 3; i++)
			{
				if (_convertedPicPlanes[i]->fifo_space() <= _convertedFrameSize[i])
				{
					vegaff_log(_param, VEGA_BQB_LOG_DEBUG, "convertedPicQueue full:%lu\n", _convertedPicQueue.size());

					SLEEP_MICROSECOND(60000);
					continue;
				}
			}

			_videoInfoQueue.pop_front();

			for (i = 0; i < 3; i++)
				yuv[i] = (void *)(_convertedPicPlanes[i]->wptr);

			if (_direct_copy)
			{
				_vrawYData->fifo_generic_read(yuv[0], video_info.Y_raw_data_size);
				_vrawCbCrData->fifo_generic_read(yuv[1], video_info.C_raw_data_size);

				_vrawYData->fifo_drain(video_info.Y_invalid_data_size);
				_vrawCbCrData->fifo_drain(video_info.C_invalid_data_size);
			}
			else
			{
				_vrawYData->fifo_generic_read(_rawPicBuf[0], video_info.Y_raw_data_size);
				_vrawCbCrData->fifo_generic_read(_rawPicBuf[1], video_info.C_raw_data_size);

				_vrawYData->fifo_drain(video_info.Y_invalid_data_size);
				_vrawCbCrData->fifo_drain(video_info.C_invalid_data_size);
				_pixfmtConverter.do_convert(_rawPicBuf, yuv);
			}

			for (i = 0; i < 3; i++)
			{
				convertedPic.planes[i] = (uint8_t *)yuv[i];
				convertedPic.stride[i] = _pixfmtConverter.converted_buf_stride[i];

				_convertedPicPlanes[i]->fifo_advance(_convertedFrameSize[i]);
			}

			_convertedPicQueue.push(convertedPic);
		}
		else
			SLEEP_MICROSECOND(4000);
	}
}

void vega_bqb_decoder_base::pushPacketThreadMain(void)
{
	API_VDEC_AU_T au = {0};

	for (;;)
	{
		if (_aborted || _ffmpeg_quit)
			break;

		if (_pushPacketQueue.empty())
		{
			SLEEP_MICROSECOND(6000);
			continue;
		}

		au = _pushPacketQueue.front();
		_pushPacketQueue.pop();

		if (au.u32Size > 0)
		{
			Api_VDEC_Push(_device, _channel, &au);
			_pushPacketData->fifo_drain(au.u32Size);
		}
		else
		{
			if (au.u32Size == 0 && au.bLast == true)
			{
				_stopDecoderThread = thread(&vega_bqb_decoder_base::stopDecoderThreadMain, this);
				break;
			}
		}
	}
}
