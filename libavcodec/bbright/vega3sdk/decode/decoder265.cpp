/*
 *
 * Copyright (C) 2017 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following condition *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#define __STDC_FORMAT_MACROS
#elif _WIN32
#include <Windows.h>
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#define __STDC_FORMAT_MACROS
#else
#endif
#include <inttypes.h>
#include <cmath>

#include "decoder265.h"
#include "../common/common.h"

#include <libvega_bqb_api/apiVDEC.h>

using namespace std;

extern "C" void vega_bqb_hevc_process_vraw(API_VDEC_IMG_T vraw, void *args);

void vega_bqb_hevc_decoder::configure(const vegaff_codec_param *arg_param)
{
	vega_bqb_decoder_base::configure(arg_param);
	if (_apiInitParam)
	{
		_apiInitParam->eCodecType = API_VDEC_CODEC_TYPE_HEVC;
	}
}

void vega_bqb_hevc_decoder::registVrawPopCallback()
{
	if (_aborted)
		return;

	vegaff_codec_param *p = _param;
	if (p->outputPath == API_VDEC_VIDEO_OUTPUT_PATH_DATA)
	{
		API_VDEC_STATUS_E st = Api_VDEC_GetStatus(_device, _channel);

		if (st != API_VDEC_STATUS_STANDBY)
			return;

		if (Api_VDEC_RegisterCallback(_device, _channel, &vega_bqb_hevc_process_vraw, (void *)this))
		{
			vegaff_log(p, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to register pop-raw callback function\n",
					   _device, _channel);
			_aborted = true;
			return;
		}
	}
}
