/*
 *
 * Copyright (C) 2017 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#elif _WIN32
#include <Windows.h>
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#else
#endif
#include "../encode/param265.h"
#include "../common/common.h"
#include "decoder265.h"
#include "../vegaff.h"

extern "C" void vega_bqb_hevc_process_vraw(API_VDEC_IMG_T vraw, void *args);

void vega_bqb_hevc_process_vraw(API_VDEC_IMG_T vraw, void *args)
{
	vega_bqb_hevc_decoder *  decoder = static_cast<vega_bqb_hevc_decoder *>(args);
	vega_bqb_hevc_dec_param *p = decoder->_param;
	int                      i;
	static int               frame_count = 0;

	vegaff_log(p, VEGA_BQB_LOG_VERBOSE,
			   "Vraw Y plane size = %d, Vraw C plane size = %d, Vraw Video info size = %d,Vraw Format:%d,Last:%d\n",
			   vraw.u32ImgYSize, vraw.u32ImgCSize, vraw.u32ImgInfoSize, vraw.eFormat, vraw.bLast);

	if (decoder->_ffmpeg_quit)
		return;

	if (vraw.u32ImgYSize)
	{
		while (decoder->_convertedPicQueue.size() +
				   decoder->_vrawYData->fifo_size() / (decoder->_rawPicSize_Y + SNI_INVALID_DATA_SIZE) >=
			   MAX_FRAME_NUMS_IN_BUF - 2)
		{
			vegaff_log(p, VEGA_BQB_LOG_DEBUG, "waiting for frame consuming, converted:%lu, vraw:%d\n",
					   decoder->_convertedPicQueue.size(), decoder->_vrawYData->fifo_size() / vraw.u32ImgYSize);
			SLEEP_MICROSECOND(150000);

			if (decoder->_ffmpeg_quit)
				return;
		}

		if (decoder->_vrawYData->fifo_space() < vraw.u32ImgYSize)
		{
			vegaff_log(p, VEGA_BQB_LOG_ERROR, "Y FIFO overflow, space left:%u, image size:%u\n",
					   decoder->_vrawYData->fifo_space(), vraw.u32ImgYSize);
		}
		else
			decoder->_vrawYData->fifo_generic_write((void *)vraw.u8ImgYAddr, (int32_t)vraw.u32ImgYSize);
	}

	if (vraw.u32ImgCSize)
	{
		if (decoder->_vrawCbCrData->fifo_space() < vraw.u32ImgCSize)
		{
			vegaff_log(p, VEGA_BQB_LOG_ERROR, "UV FIFO overflow, space left:%d, image size:%d\n",
					   decoder->_vrawCbCrData->fifo_space(), vraw.u32ImgCSize);
		}
		else
			decoder->_vrawCbCrData->fifo_generic_write((void *)vraw.u8ImgCAddr, (int32_t)vraw.u32ImgCSize);
	}

	for (i = 0; i < (int32_t)vraw.u32ImgInfoSize; i += sizeof(vega_bqb_video_info))
	{
		vega_bqb_video_info *video_info;
		uint8_t *            data = vraw.u8ImgInfoAddr + i;
		video_info = (vega_bqb_video_info *)data;

		decoder->_videoInfoQueue.push_back(*video_info);
		frame_count++;
	}

	decoder->_lastVraw = (vraw.bLast) ? true : false;

	if (decoder->_lastVraw)
		vegaff_log(p, VEGA_BQB_LOG_VERBOSE, "EOF,%d\n", frame_count);

	return;
}

vegaffhandle_t vega_bqb_hevc_decoder_open(const vega_bqb_hevc_dec_param *param)
{
	vega_bqb_hevc_decoder *  decoder = NULL;

	if (!param)
		return NULL;
	
	if (vega_bqb_hevc_dec_check_params(param))
		goto function_fail;

	vega_bqb_hevc_dec_print_params(param);

	decoder = new vega_bqb_hevc_decoder;
	if ( (!decoder) || (!decoder->create(param)) )
		goto function_fail;

	decoder->configure(param);
	decoder->init();
	decoder->registVrawPopCallback();
	decoder->start();

	if (decoder->_aborted)
		goto function_fail;

	return decoder;

function_fail:
	if (decoder) delete decoder;
	return NULL;
}

int vega_bqb_hevc_decoder_decode(vegaffhandle_t dec, int *got_frame, vegaff_packet_t *pkt_in,
											vegaff_picture_t *pic_out)
{
	int i;

	if (!dec)
	{
		*got_frame = 0;
		return -1;
	}

	vega_bqb_hevc_decoder *decoder = static_cast<vega_bqb_hevc_decoder *>(dec);

	if (decoder->_aborted)
	{
		*got_frame = 0;
		return -1;
	}

	decoder->decode(pkt_in);

#if 0
    while ((decoder->_sent_frame_cnt - decoder->_got_frame_cnt >= 256) &&
        decoder->_convertedPicQueue.empty() &&
        !decoder->_eof)
    {
        vegaff_log (decoder->_param, VEGA_BQB_LOG_DEBUG,
                "waiting decoder pop frames\n");

        SLEEP_MICROSECOND(12000);
    }
#endif

retry:
	if (!decoder->_convertedPicQueue.empty())
	{
		*pic_out = decoder->_convertedPicQueue.front();
		decoder->_convertedPicQueue.pop();

		for (i = 0; i < 3; i++)
			decoder->_convertedPicPlanes[i]->fifo_drain(decoder->_convertedFrameSize[i]);

		*got_frame = 1;
		decoder->_got_frame_cnt++;

		vegaff_log(decoder->_param, VEGA_BQB_LOG_DEBUG, "sent:%d, got:%d\n", decoder->_sent_frame_cnt,
				   decoder->_got_frame_cnt);

		return 0;
	}

	if (decoder->_eof)
	{
		if (decoder->_lastVraw && decoder->_convertedPicQueue.empty() && decoder->_videoInfoQueue.empty())
		{
			/* Read to end */
		}
		else
		{
			SLEEP_MICROSECOND(33000);
			vegaff_log(decoder->_param, VEGA_BQB_LOG_VERBOSE, "Wait EOF\n");
			goto retry;
		}
	}

	vegaff_log(decoder->_param, VEGA_BQB_LOG_DEBUG, "sent:%d, got:%d\n", decoder->_sent_frame_cnt,
			   decoder->_got_frame_cnt);

	*got_frame = 0;

	return 0;
}

void vega_bqb_hevc_decoder_close(vegaffhandle_t dec)
{
	if (dec)
	{
		vega_bqb_hevc_decoder *decoder = static_cast<vega_bqb_hevc_decoder *>(dec);
		decoder->_ffmpeg_quit = true;
		decoder->stop();
		decoder->deregistVrawPopCallback();
		decoder->exit();
		decoder->destroy();
		decoder->flush();
		delete decoder;
	}
}
