#pragma once

#include <thread>
#include <queue>
#include <mutex>
#include "../common/common.h"
#include "../vegaff.h"
#include "../common/fifo.h"
#include "../common/pixfmt_convert.h"

#define MAX_FRAME_NUMS_IN_BUF 20
#define SNI_INVALID_DATA_SIZE 0x12000

class vega_bqb_decoder_base
{
public:
	vega_bqb_decoder_base();
	virtual ~vega_bqb_decoder_base()
	{
		destroy();
	}

	virtual void configure(const vegaff_codec_param *p);
	virtual bool create(const vegaff_codec_param *p);
	virtual int  decode(const vegaff_packet_t *pkt_in);
	virtual void deregistVrawPopCallback();
	virtual void destroy();
	virtual void exit();
	virtual void flush();
	virtual void init();
	virtual void pixfmtConverterThreadMain(void);
	virtual void pushPacketThreadMain(void);
	virtual void registVrawPopCallback() = 0;
	virtual void start();
	virtual void stop();
	virtual void stopDecoderThreadMain(void);

	bool                            _aborted;
	API_VDEC_INIT_PARAM_T *         _apiInitParam;
	bool                            _called_stop;
	API_VDEC_CHN_E                  _channel;
	uint32_t                        _convertedFrameSize[3];
	FiFoBuffer *                    _convertedPicPlanes[3];
	std::queue<vegaff_picture_t>    _convertedPicQueue;
	bool                            _decoder_stopped;
	API_VDEC_DEVICE_E               _device;
	bool                            _direct_copy; // input format and output format is the same, so do direct copy
	bool                            _eof;
	bool                            _ffmpeg_quit;
	uint32_t                        _got_frame_cnt;
	bool                            _includePTS;
	bool                            _lastVraw;
	int32_t                         _picHeight;
	int32_t                         _picWidth;
	PixFmtConverter                 _pixfmtConverter;
	std::thread                     _pixfmtConverterThread;
	FiFoBuffer *                    _pushPacketData;
	std::queue<API_VDEC_AU_T>       _pushPacketQueue;
	std::thread                     _pushPacketThread;
	void *                          _rawPicBuf[3];
	uint32_t                        _rawPicSize_UV;
	uint32_t                        _rawPicSize_Y;
	uint32_t                        _sent_frame_cnt;
	std::thread                     _stopDecoderThread;
	std::mutex                      _stopperLock;
	std::deque<vega_bqb_video_info> _videoInfoQueue;
	FiFoBuffer *                    _vrawCbCrData;
	FiFoBuffer *                    _vrawYData;

	vegaff_codec_param *_param;
};
