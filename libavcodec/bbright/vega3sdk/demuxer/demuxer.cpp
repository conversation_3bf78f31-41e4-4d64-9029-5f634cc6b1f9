/*
 *
 * Copyright (C) 2017 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#define __STDC_FORMAT_MACROS
#elif _WIN32
#include <Windows.h>
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#define __STDC_FORMAT_MACROS
#else
#endif
#include <inttypes.h>
#include "demuxer.h"
#include "../common/common.h"
#include <boost/date_time/posix_time/posix_time.hpp>
#include <libvega_bqb_api/apiVDEC.h>


using namespace std;
using namespace boost::interprocess;
using namespace boost::posix_time;

vega_bqb_demuxer::vega_bqb_demuxer()
{
	_ddMutex = NULL;
	_ddCondition = NULL;
	_dmxParam = NULL;
	_device = (API_VDEC_DEVICE_E)0;
	_channel = (API_VDEC_CHN_E)0;
	_rawPktBuf1 = NULL;
	_rawPktBuf2 = NULL;
	_activeBuf = NULL;
	_activeBufPtr = NULL;
	_activeBufEnd = NULL;
	_backBuf = NULL;
	_aborted = false;
	_tempBuf = NULL;
	_tempCount = 0;
	_tsPacketCount = 0;
}

bool vega_bqb_demuxer::create(vega_bqb_dmx_param *p)
{
	if (!p)
		goto fail;

	_dmxInitParam = (API_DMX_INIT_PARAM_T *)vegaff_malloc(sizeof(API_DMX_INIT_PARAM_T));

	if (!_dmxInitParam)
	{
		vegaff_log(p, VEGA_BQB_LOG_ERROR, "init parameter allocation failure, aborting\n");
		goto fail;
	}

	memset(_dmxInitParam, 0, sizeof(API_DMX_INIT_PARAM_T));

	_rawPktBuf1 = (uint8_t *)vegaff_malloc(MAX_RAW_PACKET_BUFFER_SIZE);

	if (!_rawPktBuf1)
	{
		vegaff_log(p, VEGA_BQB_LOG_ERROR, "ts buffer allocation failure, aborting\n");
		goto fail;
	}

	memset(_rawPktBuf1, 0, MAX_RAW_PACKET_BUFFER_SIZE);

	_rawPktBuf2 = (uint8_t *)vegaff_malloc(MAX_RAW_PACKET_BUFFER_SIZE);

	if (!_rawPktBuf2)
	{
		vegaff_log(p, VEGA_BQB_LOG_ERROR, "ts buffer allocation failure, aborting\n");
		goto fail;
	}

	memset(_rawPktBuf1, 0, MAX_RAW_PACKET_BUFFER_SIZE);

	_tempBuf = (uint8_t *)vegaff_malloc(TEMP_TS_BUF_SIZE);
	if (!_tempBuf)
	{
		vegaff_log(p, VEGA_BQB_LOG_ERROR, "ts buffer allocation failure, aborting\n");
		goto fail;
	}
	memset(_tempBuf, 0, TEMP_TS_BUF_SIZE);

	_activeBufPtr = _activeBuf = _rawPktBuf1;
	_activeBufEnd = _rawPktBuf1 + MAX_RAW_PACKET_BUFFER_SIZE;
	_backBuf = _rawPktBuf2;

	_tempBuf = (uint8_t *)vegaff_malloc(TEMP_TS_BUF_SIZE);
	if (!_tempBuf)
	{
		vegaff_log(p, VEGA_BQB_LOG_ERROR, "ts buffer allocation failure, aborting\n");
		goto fail;
	}
	memset(_tempBuf, 0, TEMP_TS_BUF_SIZE);

	memset(_ddMtxName, 0, sizeof(_ddMtxName));
	memset(_ddCondName, 0, sizeof(_ddCondName));
	memset(_ddShmName, 0, sizeof(_ddShmName));
	SNPRINTF(_ddMtxName, sizeof(_ddMtxName), "ddMutex:Bd%02u:Ch%02u", p->device, p->channel);
	SNPRINTF(_ddCondName, sizeof(_ddCondName), "ddCondition:Bd%02u:Ch%02u", p->device, p->channel);
	SNPRINTF(_ddShmName, sizeof(_ddShmName), "ddSharedMemory:Bd%02u:Ch%02u", p->device, p->channel);

	try
	{
		named_mutex::remove(_ddMtxName);
		named_condition::remove(_ddCondName);
		shared_memory_object::remove(_ddShmName);
		_ddSharedMemory = managed_shared_memory(create_only, _ddShmName, 1024);
		_ddSharedMemory.construct<named_mutex>(_ddMtxName)(create_only, _ddMtxName);
		_ddSharedMemory.construct<named_condition>(_ddCondName)(create_only, _ddCondName);

		_ddMutex = _ddSharedMemory.find<named_mutex>(_ddMtxName).first;
		_ddCondition = _ddSharedMemory.find<named_condition>(_ddCondName).first;
		if (!_ddMutex || !_ddCondition)
		{
			vegaff_log(p, VEGA_BQB_LOG_ERROR, "demuxer: mutex or condition not found\n");
			goto fail;
		}
	}
	catch (interprocess_exception &err)
	{
		vegaff_log(p, VEGA_BQB_LOG_ERROR, "demuxer: interprocess_exception of shared memory = %d\n", err.what());
	}

	return true;

fail:
	_aborted = true;
	return false;
}

void vega_bqb_demuxer::configure(vega_bqb_dmx_param *p)
{
	if (!p)
	{
		_aborted = true;
		return;
	}

	if (_aborted)
		return;

	_dmxParam = p;
	_device = (API_VDEC_DEVICE_E)p->device;
	_channel = (API_VDEC_CHN_E)p->channel;
}

void vega_bqb_demuxer::init()
{
	if (_aborted)
		return;

	vega_bqb_dmx_param *p = _dmxParam;
	API_VDEC_STATUS_E   st = Api_VDEC_GetStatus(_device, _channel);

	if (st != API_VDEC_STATUS_OFF)
	{
		_aborted = true;
		return;
	}
#if 0
	_dmxInitParam->u32PMT = 256;
	_dmxInitParam->u32PCR = 257;
	_dmxInitParam->u32VID = 8176;
#else
	_dmxInitParam->u32PMT = (uint32_t)p->pmt_id;
	_dmxInitParam->u32PCR = (uint32_t)p->pcr_id;
	_dmxInitParam->u32VID = (uint32_t)p->vid;

#endif
	if (Api_DMX_Init(_device, _channel, _dmxInitParam))
	{
		vegaff_log(p, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to init VEGA_BQB_AVC demuxer\n", _device,
						 _channel);
		_aborted = true;
		return;
	}
}

void vega_bqb_demuxer::destroy()
{
	named_mutex::remove(_ddMtxName);
	named_condition::remove(_ddCondName);
	shared_memory_object::remove(_ddShmName);
	vegaff_free(_dmxInitParam);
	vegaff_free(_rawPktBuf1);
	vegaff_free(_rawPktBuf2);
	vegaff_free(_tempBuf);
	_rawPktBuf1 = NULL;
	_rawPktBuf2 = NULL;
	_activeBuf = NULL;
	_activeBufPtr = NULL;
	_activeBufEnd = NULL;
	_backBuf = NULL;
	_tempBuf = NULL;

	if (_dmxParam)
		vega_bqb_dmx_param_free(_dmxParam);

	Api_DMX_Exit(_device, _channel);
}

void vega_bqb_demuxer::start()
{
	_pktThread = thread(&vega_bqb_demuxer::pktThreadMain, this);
}

void vega_bqb_demuxer::stop()
{
	if (_pktThread.joinable())
		_pktThread.join();
}

void vega_bqb_demuxer::pktThreadMain(void *arg)
{
	vega_bqb_demuxer *demuxer = static_cast<vega_bqb_demuxer *>(arg);

	for (;;)
	{
		scoped_lock<named_mutex> lock(*(demuxer->_ddMutex));

		demuxer->_ddCondition->wait(lock);
#if 0
		if (demuxer->_activeBuf == demuxer->_rawPktBuf1)
		{
			demuxer->_backBuf = demuxer->_rawPktBuf2;
			demuxer->pushPacket(true);
		}
		else if (demuxer->_activeBuf == demuxer->_rawPktBuf2)
		{
			demuxer->_backBuf = demuxer->_rawPktBuf1;
			demuxer->pushPacket(true);
		}
#else
		demuxer->pushPacket(true);
#endif
		break;
	}
}

int vega_bqb_demuxer::readPacket(void *buffer, int size)
{
	if ((_activeBufEnd - _activeBufPtr) < size)
	{
		if (_activeBuf == _rawPktBuf1)
		{
			_activeBufPtr = _activeBuf = _rawPktBuf2;
			_activeBufEnd = _rawPktBuf2 + MAX_RAW_PACKET_BUFFER_SIZE;
			_backBuf = _rawPktBuf1;
			pushPacket(false);
		}
		else if (_activeBuf == _rawPktBuf2)
		{
			_activeBufPtr = _activeBuf = _rawPktBuf1;
			_activeBufEnd = _rawPktBuf1 + MAX_RAW_PACKET_BUFFER_SIZE;
			_backBuf = _rawPktBuf2;
			pushPacket(false);
		}
		_tsPacketCount = 0;
	}

	memcpy(_activeBufPtr, buffer, size);
	_activeBufPtr += size;
	_tsPacketCount++;
	return 0;
}

int vega_bqb_demuxer::pushPacket(bool is_last_pkt)
{
	vega_bqb_dmx_param *p = _dmxParam;

	API_VDEC_STATUS_E st = Api_VDEC_GetStatus(_device, _channel);

	if (st != API_VDEC_STATUS_DECODING)
	{
		memcpy(_tempBuf + _tempCount, _backBuf, MAX_RAW_PACKET_BUFFER_SIZE);
		_tempCount += MAX_RAW_PACKET_BUFFER_SIZE;
		return 0;
	}
	else
	{
		if (_tempCount > 0)
		{

			int tempTSNum = (_tempCount / RAW_PACKET_SIZE);
			for (int i = 0; i < tempTSNum; i++)
			{
				_tempPkts[i].pu8Addr = _tempBuf + i * RAW_PACKET_SIZE;
				_tempPkts[i].bLast = false;
			}
			vegaff_log(p, VEGA_BQB_LOG_ERROR, "push first TS:%d,%d\n", _tempCount, tempTSNum);

			if (Api_DMX_PushPackets(_device, _channel, _tempPkts, tempTSNum))
			{
				vegaff_log(p, VEGA_BQB_LOG_ERROR, "Could not push packet\n");
				return -1;
			}

			_tempCount = 0;
#ifdef _WIN32
			Sleep(1);
#else
			usleep(1000);
#endif
		}
	}

	for (int i = 0; i < _tsPacketCount; i++)
	{
		_pkts[i].pu8Addr = _backBuf + i * RAW_PACKET_SIZE;
		_pkts[i].bLast = is_last_pkt;
	}

	if (is_last_pkt)
	{
		vegaff_log(p, VEGA_BQB_LOG_DEBUG, "Push Last packet,%d\n", _tsPacketCount);
	}

	if (Api_DMX_PushPackets(_device, _channel, _pkts, _tsPacketCount))
	{
		vegaff_log(p, VEGA_BQB_LOG_ERROR, "Could not push packet\n");
		return -1;
	}
	if (is_last_pkt)
	{

		Api_VDEC_Stop(_device, _channel);
		vegaff_log(p, VEGA_BQB_LOG_VERBOSE, "Api Vdec Stop\n");
	}

#ifdef _WIN32
	Sleep(1);
#else
	usleep(1000);
#endif

	return 0;
}
