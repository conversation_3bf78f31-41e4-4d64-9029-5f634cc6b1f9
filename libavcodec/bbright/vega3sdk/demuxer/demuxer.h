/*
 *
 * Copyright (C) 2017 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#pragma once

#include <thread>
#include <boost/interprocess/managed_shared_memory.hpp>
#include <boost/interprocess/sync/named_mutex.hpp>
#include <boost/interprocess/sync/named_condition.hpp>
#include <boost/interprocess/sync/scoped_lock.hpp>
#include "../common/common.h"
#include "../vegaff.h"

#define RAW_PACKET_SIZE            188
#define APP_TS_PKT_NUM             512
#define MAX_RAW_PACKET_BUFFER_SIZE (188 * 512)
#define TEMP_TS_PKT_NUM_MAX        65535 * 4
#define TEMP_TS_BUF_SIZE           (TEMP_TS_PKT_NUM_MAX * RAW_PACKET_SIZE)


using namespace std;
using namespace boost::interprocess;

class vega_bqb_demuxer 
{
public:
	vega_bqb_demuxer();
	~vega_bqb_demuxer()
	{
	}

	bool        create(vega_bqb_dmx_param *p);
	void        configure(vega_bqb_dmx_param *p);
	void        init();
	void        destroy();
	void        start();
	void        stop();
	static void pktThreadMain(void *arg);
	int         readPacket(void *buffer, int size);
	int         pushPacket(bool is_last_pkt);

protected:
	thread                _pktThread;
	managed_shared_memory _ddSharedMemory;
	named_mutex *         _ddMutex;
	named_condition *     _ddCondition;
	vega_bqb_dmx_param *  _dmxParam;
	API_DMX_INIT_PARAM_T *_dmxInitParam;
	API_VDEC_DEVICE_E     _device;
	API_VDEC_CHN_E        _channel;
	API_VDEC_TS_PKT_T     _pkts[APP_TS_PKT_NUM];
	uint8_t *             _rawPktBuf1;
	uint8_t *             _rawPktBuf2;
	uint8_t *             _activeBuf;
	uint8_t *             _activeBufPtr;
	uint8_t *             _activeBufEnd;
	uint8_t *             _backBuf;
	API_VDEC_TS_PKT_T     _tempPkts[TEMP_TS_PKT_NUM_MAX];
	uint8_t *             _tempBuf;
	int32_t               _tempCount;
	char                  _ddShmName[32];
	char                  _ddMtxName[32];
	char                  _ddCondName[32];
	bool                  _aborted;
	int32_t               _tsPacketCount;
};
