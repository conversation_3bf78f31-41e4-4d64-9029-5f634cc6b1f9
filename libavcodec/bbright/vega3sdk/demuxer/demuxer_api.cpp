/*
 *
 * Copyright (C) 2017 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#elif _WIN32
#include <Windows.h>
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#else
#endif
#include "../common/common.h"
#include "demuxer.h"
#include "../vegaff.h"

using namespace std;

vega_bqb_dmx_param *vega_bqb_dmx_param_alloc()
{
	return vegaff_zallocT<vega_bqb_dmx_param>();
}

void vega_bqb_dmx_param_free(vega_bqb_dmx_param *p)
{
	vegaff_free(p);
}

vegaffhandle_t vega_bqb_demuxer_open(vega_bqb_dmx_param *p)
{
	if (!p)
		return NULL;

	vega_bqb_demuxer *  demuxer = NULL;
	vega_bqb_dmx_param *dmx_param = vega_bqb_dmx_param_alloc();
	if (!dmx_param)
		goto alloc_fail;

	memcpy(dmx_param, p, sizeof(vega_bqb_dmx_param));
	demuxer = new vega_bqb_demuxer;

	if (!demuxer->create(dmx_param))
		goto create_fail;

	demuxer->configure(dmx_param);
	demuxer->init();
	demuxer->start();

	return demuxer;

create_fail:
	demuxer->destroy();
	delete demuxer;
alloc_fail:
	vega_bqb_dmx_param_free(dmx_param);
	return NULL;
}

extern "C" int vega_bqb_demuxer_read_packet(vegaffhandle_t dmx, void *buffer, int size)
{
	if (dmx)
	{
		vega_bqb_demuxer *demuxer = static_cast<vega_bqb_demuxer *>(dmx);
		return demuxer->readPacket(buffer, size);
	}

	return 0;
}

extern "C" void vega_bqb_demuxer_close(vegaffhandle_t dmx)
{
	if (dmx)
	{
		vega_bqb_demuxer *demuxer = static_cast<vega_bqb_demuxer *>(dmx);
		demuxer->stop();
		demuxer->destroy();
		delete demuxer;
	}
}
