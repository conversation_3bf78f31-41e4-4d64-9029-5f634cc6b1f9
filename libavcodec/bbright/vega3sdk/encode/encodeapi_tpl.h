#pragma once

#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#elif _WIN32
#include <Windows.h>
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#endif

#include <time.h>
#include "../vegaff.h"
#include "../common/common.h"
#include "../common/picyuv.h"
#include "../common/frame.h"
#include "../common/constants.h"

#define ES_BUF_SIZE ((ptrdiff_t)(encoder->_esBufEnd - encoder->_esBufPtr))

extern int vegaff_encoder_encode(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal, vegaff_picture_t *pic_in,
								 vegaff_picture_t *pic_out);

extern int vegaff_encoder_getes(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal, vegaff_picture_t *pic_in,
								vegaff_picture_t *pic_out);

extern void vegaff_signal_capture_start(const int capture_counter, vega_bqb_encoder *args);
extern int  vegaff_encoder_pushimage(vega_bqb_encoder *enc, vegaff_picture_t *pic_in);
extern void vegaff_encoder_start(vegaffhandle_t enc);
extern void vegaff_encoder_stop(vegaffhandle_t enc);
extern void vegaff_encoder_reset(vegaffhandle_t enc);
extern int  vegaff_encoder_setbitrate(vegaffhandle_t enc, uint32_t bitrate);
extern int  vegaff_encoder_setvbr(vegaffhandle_t enc, uint32_t max, uint32_t ave, uint32_t min);
extern int  vegaff_encoder_forceidr(vegaffhandle_t enc);
extern int  vegaff_encoder_forceidrat(vegaffhandle_t enc, uint32_t pic_num);
extern int  vegaff_encoder_setframerate(vegaffhandle_t enc, uint32_t fps);
extern int  vegaff_encoder_setframerateat(vegaffhandle_t enc, uint32_t fps, uint32_t pic_num);
