/*
 *
 * Copyright (C) 2019 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#define __STDC_FORMAT_MACROS
#elif _WIN32
#include <Windows.h>
#include "../common/LIB_MISC_WIN32.h"
#define __STDC_FORMAT_MACROS
#endif
#include <inttypes.h>
#include <cmath>
#include "encoder262.h"
#include "../common/common.h"
#include "../common/sei.h"
#include "../common/message_option.h"
#include "../common/frame.h"

using namespace boost::interprocess;

extern "C" const stResolution gstRes262[];
const stResolution            gstRes262[] = {
    {"320x180", 320, 180, API_VEGA_BQB_RESOLUTION_320x180, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"320x240", 320, 240, API_VEGA_BQB_RESOLUTION_320x240, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"352x288", 352, 288, API_VEGA_BQB_RESOLUTION_352x288, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"384x160", 384, 160, API_VEGA_BQB_RESOLUTION_384x160, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"416x240", 416, 240, API_VEGA_BQB_RESOLUTION_416x240, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"480x200", 480, 200, API_VEGA_BQB_RESOLUTION_480x200, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"480x270", 480, 270, API_VEGA_BQB_RESOLUTION_480x270, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"480x360", 480, 360, API_VEGA_BQB_RESOLUTION_480x360, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"528x480", 528, 480, API_VEGA_BQB_RESOLUTION_528x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"544x480", 544, 480, API_VEGA_BQB_RESOLUTION_544x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"640x360", 640, 360, API_VEGA_BQB_RESOLUTION_640x360, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"640x480", 640, 480, API_VEGA_BQB_RESOLUTION_640x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"720x480", 720, 480, API_VEGA_BQB_RESOLUTION_720x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"720x540", 720, 540, API_VEGA_BQB_RESOLUTION_720x540, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"720x576", 720, 576, API_VEGA_BQB_RESOLUTION_720x576, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"848x480", 848, 480, API_VEGA_BQB_RESOLUTION_848x480, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"864x360", 864, 360, API_VEGA_BQB_RESOLUTION_864x360, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"960x400", 960, 400, API_VEGA_BQB_RESOLUTION_960x400, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"960x540", 960, 540, API_VEGA_BQB_RESOLUTION_960x540, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"960x720", 960, 720, API_VEGA_BQB_RESOLUTION_960x720, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"1280x532", 1280, 532, API_VEGA_BQB_RESOLUTION_1280x532, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"1280x720", 1280, 720, API_VEGA_BQB_RESOLUTION_1280x720, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"1280x1024", 1280, 1024, API_VEGA_BQB_RESOLUTION_1280x1024, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"1280x1080", 1280, 1080, API_VEGA_BQB_RESOLUTION_1280x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"1440x1080", 1440, 1080, API_VEGA_BQB_RESOLUTION_1440x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"1920x800", 1920, 800, API_VEGA_BQB_RESOLUTION_1920x800, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"1920x1080", 1920, 1080, API_VEGA_BQB_RESOLUTION_1920x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING} //,
};

extern "C" const int gstRes262_count;
const int            gstRes262_count = sizeof(gstRes262) / sizeof(stResolution);

vega_bqb_mpeg_encoder::vega_bqb_mpeg_encoder()
{
	gst_res_table = (const stResolution *)gstRes262;
	gst_res_count = gstRes262_count;
}

int vega_bqb_mpeg_encoder::configure(const vegaff_codec_param *arg_param)
{
	API_VEGA_BQB_RET     bqbret;
	vega_bqb_mpeg_param *lparam;
	if (_aborted)
		return -1;

	if (arg_param)
	{
		vegaff_copyT(_param, arg_param);
	}

	lparam = (vega_bqb_mpeg_param *)_param;
	_device = (API_VEGA_BQB_DEVICE_E)lparam->device;
	_channel = (API_VEGA_BQB_CHN_E)lparam->channel;
	_apiInitParam->eCodecType = API_VEGA_BQB_CODEC_TYPE_MPEG2;
	_apiInitParam->eOutputFmt = API_VEGA_BQB_STREAM_OUTPUT_FORMAT_ES;

	API_VEGA_BQB_MPEG_INIT_PARAM_T *ap = &_apiInitParam->tMpegParam;
	ap->eInputMode = (API_VEGA_BQB_INPUT_MODE_E)lparam->inputMode;
	ap->eInputPort = API_VEGA_BQB_VIF_MODE_INPUT_PORT_DEFAULT;
	ap->eLevel = (API_VEGA_BQB_MPEG_LEVEL_E)lparam->levelIdc;
	API_VEGA_BQB_ENCODE_CONFIG_T enc_cfg;

	ap->eResolution = API_VEGA_BQB_RESOLUTION_INVALID;
	for (int i = 0; i < gst_res_count; i++)
	{
		if (lparam->sourceWidth == gst_res_table[i].width && lparam->sourceHeight == gst_res_table[i].height)
		{
			if ((lparam->fpsNum / lparam->fpsDenom) <= 30)
				enc_cfg.eMode = gst_res_table[i].eMode2;
			else
				enc_cfg.eMode = gst_res_table[i].eMode1;
			ap->eResolution = gst_res_table[i].eRes;
			break;
		}
	}
	if (ap->eResolution == API_VEGA_BQB_RESOLUTION_INVALID)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "unsupported resolution\n");
		_aborted = true;
		return -2;
	}

	/// @TODO (05/17/2022) This wasn't in mpeg portion, should we enable them as HEVC/AVC does?
	/// if (p->encodeMode != 0) // it is not auto mode
	/// {
	/// 	enc_cfg.eMode = (API_VEGA_BQB_DEVICE_ENC_MODE_E)(p->encodeMode - 1);
	/// }
	bqbret = VEGA_BQB_ENC_ConfigDeviceMode(_device, &enc_cfg);
	if ((int)bqbret != 0)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Failed to change mode (%d)\n", (int)bqbret);
		_aborted = true;
		return -3;
	}

	switch (lparam->internalCsp)
	{
	case VEGA_BQB_CSP_I420:
		switch (lparam->internalBitDepth)
		{
		case 8:
			if (lparam->inputMode) // vif mode, M30 only supports NV12, it cannot support YUV420
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV12;
			else
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I420;
			break;
		case 10:
			ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I0AL;
			break;
		}
		ap->eInputChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_420;
		break;
	case VEGA_BQB_CSP_NV12:
		ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV12;
		ap->eInputChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_420;
		break;

	case VEGA_BQB_CSP_I422:
		switch (lparam->internalBitDepth)
		{
		case 8:
			if (lparam->inputMode) // vif mode, M30 only supports NV16, it cannot support YUV420
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV16;
			else
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I420;
			break;
		case 10:
			ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I2AL;
			break;
		}
		ap->eInputChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_422;
		break;
	case VEGA_BQB_CSP_NV16:
		ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV16;
		ap->eInputChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_422;
		break;
	}

	switch (lparam->internalBitDepth)
	{
	case 10:
		ap->eInputBitDepth = API_VEGA_BQB_BIT_DEPTH_10;
		break;
	case 8:
	default:
		ap->eInputBitDepth = API_VEGA_BQB_BIT_DEPTH_8;
		break;
	}

	if (lparam->inputMode)
	{
		if (!lparam->interlaceMode)
		{
			ap->tChromaConvertInfo.s11C420Coef[0] = -3;
			ap->tChromaConvertInfo.s11C420Coef[1] = -19;
			ap->tChromaConvertInfo.s11C420Coef[2] = 34;
			ap->tChromaConvertInfo.s11C420Coef[3] = 500;
			ap->tChromaConvertInfo.s11C420Coef[4] = 500;
			ap->tChromaConvertInfo.s11C420Coef[5] = 34;
			ap->tChromaConvertInfo.s11C420Coef[6] = -19;
			ap->tChromaConvertInfo.s11C420Coef[7] = -3;
		}
		else
		{
			ap->tChromaConvertInfo.s11C420Coef[0] = -8;
			ap->tChromaConvertInfo.s11C420Coef[1] = -26;
			ap->tChromaConvertInfo.s11C420Coef[2] = 115;
			ap->tChromaConvertInfo.s11C420Coef[3] = 586;
			ap->tChromaConvertInfo.s11C420Coef[4] = 409;
			ap->tChromaConvertInfo.s11C420Coef[5] = -48;
			ap->tChromaConvertInfo.s11C420Coef[6] = -4;
			ap->tChromaConvertInfo.s11C420Coef[7] = 0;
		}
	}

	ap->tVideoSignalType.bPresentFlag = lparam->vui.bEnableVideoSignalTypePresentFlag != 0;
	ap->tVideoSignalType.eVideoFormat = (API_VEGA_BQB_VIDEO_FORMAT_E)lparam->vui.videoFormat;
	ap->tVideoSignalType.tColorDesc.bPresentFlag = lparam->vui.bEnableColorDescriptionPresentFlag != 0;
	ap->tVideoSignalType.tColorDesc.eColorPrimaries = (API_VEGA_BQB_COLOR_PRIMARY_E)lparam->vui.colorPrimaries;
	ap->tVideoSignalType.tColorDesc.eTransferCharacteristics =
		(API_VEGA_BQB_TRANSFER_CHAR_E)lparam->vui.transferCharacteristics;
	ap->tVideoSignalType.tColorDesc.eMatrixCoeff = (API_VEGA_BQB_MATRIX_COEFFS_E)lparam->vui.matrixCoeffs;
	ap->bInterlace = (lparam->interlaceMode == 1) ? true : false;

	switch (lparam->bOpenGOP)
	{
	case 0:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_ALL;
		break;

	case 1:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_FIRST;
		break;

	case 2:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_TWO;
		break;

	case 3:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_100;
		break;
	}

	if (lparam->inputMode == API_VEGA_BQB_INPUT_MODE_DATA)
	{
		ap->ePtsMode = API_VEGA_BQB_PTS_MODE_USER;
	}
	else if (lparam->inputMode == API_VEGA_BQB_INPUT_MODE_VIF_SQUARE ||
			 lparam->inputMode == API_VEGA_BQB_INPUT_MODE_VIF_2_SAMPLE_INTERLEAVE)
	{
		ap->ePtsMode = API_VEGA_BQB_PTS_MODE_VANC;
		// mp->ePtsMode = API_VEGA_BQB_PTS_MODE_VANC;
	}

	switch (ap->eResolution)
	{
	// case API_VEGA_BQB_RESOLUTION_2048x1080:
	case API_VEGA_BQB_RESOLUTION_1920x1080:
	case API_VEGA_BQB_RESOLUTION_1280x720:
	case API_VEGA_BQB_RESOLUTION_960x540:
	case API_VEGA_BQB_RESOLUTION_848x480:
	case API_VEGA_BQB_RESOLUTION_640x360:
	case API_VEGA_BQB_RESOLUTION_480x270:
	case API_VEGA_BQB_RESOLUTION_416x240:
	case API_VEGA_BQB_RESOLUTION_320x180: {
		ap->eAspectRatioInfo = API_VEGA_BQB_MPEG_ASPECT_RATIO_9_16;
		break;
	}
	case API_VEGA_BQB_RESOLUTION_1440x1080:
	case API_VEGA_BQB_RESOLUTION_1280x1080:
	case API_VEGA_BQB_RESOLUTION_1280x1024:
	case API_VEGA_BQB_RESOLUTION_960x720:
	case API_VEGA_BQB_RESOLUTION_720x576:
	case API_VEGA_BQB_RESOLUTION_720x540:
	case API_VEGA_BQB_RESOLUTION_720x480:
	case API_VEGA_BQB_RESOLUTION_640x480:
	case API_VEGA_BQB_RESOLUTION_480x360:
	case API_VEGA_BQB_RESOLUTION_352x288:
	case API_VEGA_BQB_RESOLUTION_320x240: {
		ap->eAspectRatioInfo = API_VEGA_BQB_MPEG_ASPECT_RATIO_3_4;
		break;
	}
	case API_VEGA_BQB_RESOLUTION_384x160:
	case API_VEGA_BQB_RESOLUTION_480x200:
	case API_VEGA_BQB_RESOLUTION_864x360:
	case API_VEGA_BQB_RESOLUTION_960x400:
	case API_VEGA_BQB_RESOLUTION_1280x532:
	case API_VEGA_BQB_RESOLUTION_1920x800: {
		ap->eAspectRatioInfo = API_VEGA_BQB_MPEG_ASPECT_RATIO_1_221;
		break;
	}
	case API_VEGA_BQB_RESOLUTION_528x480:
	case API_VEGA_BQB_RESOLUTION_544x480: {
		ap->eAspectRatioInfo = API_VEGA_BQB_MPEG_ASPECT_RATIO_1_1;
		break;
	}
	default: {
		ap->eAspectRatioInfo = API_VEGA_BQB_MPEG_ASPECT_RATIO_9_16;
		break;
	}
	}

	uint32_t fps = lparam->fpsNum / lparam->fpsDenom;

	if (lparam->interlaceMode && fps <= 30) // AVC interlace mode, fps must be 60/59.94/50/(48?)
	{
		if (fps == 30 || fps == 29)
		{
			fps += 30;
		}
		else
		{
			fps = fps * 2;
		}
	}

	ap->eTargetFrameRate = (API_VEGA_BQB_FPS_E)fps;

	switch (lparam->gopType)
	{
	case 0: // I Only
		ap->eGopType = API_VEGA_BQB_GOP_IP;
		ap->eBFrameNum = API_VEGA_BQB_B_FRAME_NONE;
		ap->eGopSize = (API_VEGA_BQB_GOP_SIZE_E)lparam->keyframeMax; // 1
		break;
	case 1: // IPPP
		ap->eGopType = API_VEGA_BQB_GOP_IP;
		ap->eBFrameNum = API_VEGA_BQB_B_FRAME_NONE;
		ap->eGopSize = (API_VEGA_BQB_GOP_SIZE_E)lparam->keyframeMax; // > 1
		break;
	case 2: // IBP
		ap->eGopType = API_VEGA_BQB_GOP_IPB;
		ap->eBFrameNum = API_VEGA_BQB_B_FRAME_ONE;
		ap->eGopSize = (API_VEGA_BQB_GOP_SIZE_E)lparam->keyframeMax; // multiple of 2
		break;
	case 3: // IBBP
	default:
		ap->eGopType = API_VEGA_BQB_GOP_IPB;
		ap->eBFrameNum = API_VEGA_BQB_B_FRAME_TWO;
		ap->eGopSize = (API_VEGA_BQB_GOP_SIZE_E)lparam->keyframeMax; // multiple of 3
		break;
	}
	ap->bDisableSceneChangeDetect = (lparam->scenecutThreshold == 1) ? false : true;
	if (lparam->rc.bStrictCbr)
		lparam->rc.rateControlMode = VEGA_BQB_RC_CAPPED_VBR;
	ap->eRateCtrlAlgo = (API_VEGA_BQB_RATE_CTRL_ALGO_E)lparam->rc.rateControlMode;
	ap->u32Bitrate = (uint32_t)lparam->rc.bitrate;
	ap->u32AveVBR = (uint32_t)lparam->rc.vbrAveBitrate;
	ap->eRobustMode = (API_VEGA_BQB_VIF_ROBUST_MODE_E)(lparam->robustMode);

	return 0;
}

void vega_bqb_mpeg_encoder::init()
{
	vega_bqb_mpeg_param * p = (vega_bqb_mpeg_param *)_param;
	API_VEGA_BQB_STATUS_E st;

	if (_aborted)
		return;

	st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_OFF)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Encoder is not off.\n");
		return;
	}

	VEGA_BQB_ENC_SetDbgMsgLevel(_device, _channel, (API_VEGA_BQB_DBG_LEVEL_E)p->dbgLevel);

	if ((p->inputHeight != p->sourceHeight || p->inputWidth != p->sourceWidth) && !p->interlaceMode)
	{
		API_VEGA_BQB_XC_INIT_PARAM_T    api_xc_init_param;
		API_VEGA_BQB_MPEG_INIT_PARAM_T *ap = &_apiInitParam->tMpegParam;
		API_VEGA_BQB_RET                api_ret;
		memset(&api_xc_init_param, 0, sizeof(API_VEGA_BQB_XC_INIT_PARAM_T));

		api_xc_init_param.eInputSource = API_VEGA_BQB_XC_INPUT_SOURCE_PCIE;
		api_xc_init_param.u32SourceId = _channel;
		api_xc_init_param.eInputResolution = getResolution(p->inputWidth, p->inputHeight);
		api_xc_init_param.eOutputResolution = getResolution(p->sourceWidth, p->sourceHeight);
		api_xc_init_param.eChromaFmt = ap->eInputChromaFmt;
		api_xc_init_param.eBitDepth = API_VEGA_BQB_BIT_DEPTH_8;

		api_ret = VEGA_BQB_XC_Init(_device, _channel, &api_xc_init_param);

		if (api_ret != API_VEGA_BQB_RET_SUCCESS)
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to init VEGA_BQB_MPEG scaler \n", _device,
					   _channel);
			_aborted = true;
			return;
		}
		else
		{
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "Init XC %dx%d -> %dx%d , input chroma:%d\n", p->inputWidth,
					   p->inputHeight, p->sourceWidth, p->sourceHeight, ap->eInputChromaFmt);
		}
	}

	if (VEGA_BQB_ENC_Init(_device, _channel, _apiInitParam))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to init VEGA_BQB_AVC encoder\n", _device,
				   _channel);
		_aborted = true;
		return;
	}
	VEGA_BQB_ENC_SetTimeStampUnit(_device, _channel, API_VEGA_BQB_TIMEBASE_90KHZ);
}

void vega_bqb_mpeg_encoder::registEsPopCallback(void *pfunc_callback)
{
	if (_aborted)
		return;

	API_VEGA_BQB_STATUS_E st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_STANDBY)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Encoder isn't in standby mode\n");
		return;
	}

	if (VEGA_BQB_ENC_RegisterMpegCallback(_device, _channel, (API_VEGA_BQB_MPEG_CALLBACK)pfunc_callback, (void *)this))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to register pop-es callback function\n",
				   _device, _channel);
		_aborted = true;
		return;
	}
}

int vega_bqb_mpeg_encoder::encode(const vegaff_picture_t *pic_in)
{
	if (_aborted)
		return -1;

	vegaff_codec_param *p = _param;
	API_VEGA_BQB_IMG_T  img, last_img;

	if (p->inputMode)
	{ /* operates at VIF mode */
		if (!pic_in)
		{
			deregistVideoCaptureStartCallback();
			stop();
			TRACE();
		}
		return 0;
	}

	if (pic_in && !_pendingFrame)
	{
		_picInSize = _inFrame->getPictureSize(pic_in);
		_picInPts = pic_in->pts;
		_picInFmt = (API_VEGA_BQB_IMAGE_FORMAT_E)pic_in->imgFormat;
		_inFrame->copyFromPicture(this, pic_in);
		_inFrame->contextReinit(pic_in);
		_pendingFrame = true;
	}
	else if (pic_in && _pendingFrame)
	{
		if (_inFrame->_sizeChanged)
		{
			uint8_t *buf = (uint8_t *)vegaff_malloc(_picInSize);
			if (!buf)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device:%d channel:%d image backup allocation for context change failure, aborting\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}

			memcpy(buf, _vrawBuf, _picInSize);
			memset(_vrawBuf, 0, MAX_VRAW_BUF_SIZE);

			VEGA_BQB_ENC_Rescale(_vrawBuf, buf, _inFrame->_width0, _inFrame->_height0, _inFrame->_width1,
								 _inFrame->_height1);

			vegaff_free(buf);
		}

		img.pu8Addr = (uint8_t *)_vrawBuf;
		img.u32Size = _inFrame->_maxPictureSize;
		img.pts = (uint64_t)getPicInPts();
		img.eTimeBase = API_VEGA_BQB_TIMEBASE_90KHZ;
		img.bLastFrame = false;
		if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I0AL)
		{
			img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP01;
		}
		else if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I2AL)
		{
			img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP21;
		}
		else
			img.eFormat = _picInFmt;

		img.u32SeiNum = 0;
		img.bSeiPassThrough = false;

		if (_inFrame->_user_data_registered_itu_t_t35)
		{
			img.u32SeiNum = 1;
			img.bSeiPassThrough = false;

			if (writeUserDataRegisteredSEI(img.tSeiParam) < 0)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to write UserDataRegisteredSEI\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}
		}

		if (VEGA_BQB_ENC_PushImage(_device, _channel, &img))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
			_aborted = true;
			return -1;
		}

		_picInSize = _inFrame->getPictureSize(pic_in);
		_picInPts = pic_in->pts;
		_picInFmt = (API_VEGA_BQB_IMAGE_FORMAT_E)pic_in->imgFormat;
		_inFrame->copyFromPicture(this, pic_in);

		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega input frame=%d\n", _device, _channel,
				   _inFrameCnt++);
		_inFrame->contextReinit(pic_in);
		_pendingFrame = true;
	}
	else if (!pic_in && _pendingFrame)
	{
		if (_inFrame->_sizeChanged)
		{
			uint8_t *buf = (uint8_t *)vegaff_malloc(_picInSize);
			if (!buf)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device:%d channel:%d image backup allocation for context change failure, aborting\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}

			memcpy(buf, _vrawBuf, _picInSize);
			memset(_vrawBuf, 0, MAX_VRAW_BUF_SIZE);

			VEGA_BQB_ENC_Rescale(_vrawBuf, buf, _inFrame->_width0, _inFrame->_height0, _inFrame->_width1,
								 _inFrame->_height1);

			vegaff_free(buf);
		}

		last_img.pu8Addr = (uint8_t *)_vrawBuf;
		last_img.u32Size = _inFrame->_maxPictureSize;
		last_img.pts = (uint64_t)getPicInPts();
		last_img.eTimeBase = API_VEGA_BQB_TIMEBASE_90KHZ;
		last_img.bLastFrame = true;

		if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I0AL)
		{
			last_img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP01;
		}
		else if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I2AL)
		{
			last_img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP21;
		}
		else
			last_img.eFormat = _picInFmt;

		last_img.u32SeiNum = 0;
		last_img.bSeiPassThrough = false;

		if (_inFrame->_user_data_registered_itu_t_t35)
		{
			last_img.u32SeiNum = 1;
			last_img.bSeiPassThrough = false;

			if (writeUserDataRegisteredSEI(last_img.tSeiParam) < 0)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to write UserDataRegisteredSEI\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}
		}

		if (p->interlaceMode && (_inFrameCnt % 2 == 0))
		{
			last_img.bLastFrame = false;
			if (VEGA_BQB_ENC_PushImage((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
									   (API_VEGA_BQB_IMG_T *)&last_img))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
				_aborted = true;
				return -1;
			}
			vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega input frame=%d\n", _device, _channel,
					   _inFrameCnt++);
			_picInPts += 1;
			last_img.pts = (uint64_t)getPicInPts();
			last_img.bLastFrame = true;
			if (VEGA_BQB_ENC_PushImage((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
									   (API_VEGA_BQB_IMG_T *)&last_img))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
				_aborted = true;
				return -1;
			}
			vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega last_frame, input frame=%d\n", _device,
					   _channel, _inFrameCnt++);
			_pendingFrame = false;
			return 0;
		}

		if (VEGA_BQB_ENC_PushImage((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
								   (API_VEGA_BQB_IMG_T *)&last_img))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
			_aborted = true;
			return -1;
		}

		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega last_frame, input frame=%d\n", _device,
				   _channel, _inFrameCnt++);

		_pendingFrame = false;
	}
	else if (!pic_in && !_pendingFrame && !_inFrameCnt && !_outFrameCnt)
	{
		TRACE();
		_aborted = true;
		return -1;
	}

	return 0;
}

void vega_bqb_mpeg_encoder::flush()
{
	while (!mp2Queue.empty())
	{
		mp2Queue.pop();
	}
}
