/*
 *
 * Copyright (C) 2019 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "param262.h"
#include "encoder262.h"
#include "encodeapi_tpl.h"

static void vega_bqb_mpeg_process_coded_frame(API_VEGA_BQB_MPEG_CODED_PICT_T *p_pict, void *args)
{
	vega_bqb_mpeg_encoder *encoder = static_cast<vega_bqb_mpeg_encoder *>(args);
	int                    occupancy = 0;
	static bool            fBufferOFPrint = true;

	vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "bd=%d ch=%d pts=%ld dts=%ld itc=%ld itc_ext=%u tb=%d cpb=%u type=%s\n",
			   encoder->_device, encoder->_channel, p_pict->pts, p_pict->dts, p_pict->u64ItcBase, p_pict->u32ItcExt,
			   p_pict->eTimeBase, p_pict->u32CpbBoc, vega_bqb_mpeg_pic_types[p_pict->eFrameType]);

	Queue<vegaff_nal_t> tempQueue;

	occupancy = p_pict->u32Length;

	if (((ES_BUF_SIZE / 5 > encoder->_esBufSize) || ((uint32_t)occupancy > encoder->_esBufSize)) && fBufferOFPrint)
	{
		char       local_time[256];
		time_t     timep;
		struct tm *ptime;
		time(&timep);
		ptime = localtime(&timep);
		sprintf(local_time,

				"%d-%d-%d %d:%d:%d", (1900 + ptime->tm_year), (1 + ptime->tm_mon), ptime->tm_mday, ptime->tm_hour,
				ptime->tm_min, ptime->tm_sec);
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "[%s]Encoder Buffer(%d) is full, this ES(%d) will be dropped\n",
				   local_time, encoder->_esBufSize, occupancy);
		fBufferOFPrint = false;
		return;
	}

	fBufferOFPrint = true;
	if ((encoder->_esBufEnd - encoder->_esBufPtr) < occupancy)
	{
		encoder->_esBufPtr = encoder->_esBuf;
	}

	vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "pic len=%u type=%u\n", p_pict->u32Length, p_pict->eFrameType);

	vegaff_nal_t tempPic;

	memcpy(encoder->_esBufPtr, p_pict->pu8Addr, p_pict->u32Length);

	tempPic.pu8Addr = encoder->_esBufPtr;
	tempPic.u32Length = p_pict->u32Length;
	tempPic.type = p_pict->eFrameType;
	tempPic.pts = encoder->getPicOutPts((int64_t)p_pict->pts);
	tempPic.dts = encoder->getPicOutDts(p_pict->dts);
	tempPic.sliceType = (int)p_pict->eFrameType;
	tempPic.is_last_ES = (p_pict->bLastES) ? 1 : 0;
	encoder->_esBufPtr += p_pict->u32Length;
	encoder->_esBufSize -= occupancy;

	encoder->mp2Queue.push(tempPic);
	encoder->_lastES = (p_pict->bLastES) ? true : false;
}

static void vega_bqb_mpeg_signal_capture_start(const int capture_counter, void *args)
{
	vegaff_signal_capture_start(capture_counter, (vega_bqb_encoder *)args);
}

vegaffhandle_t vega_bqb_mpeg_encoder_open(const vega_bqb_mpeg_param *arg_param)
{
	if (!arg_param)
		return NULL;

	vega_bqb_mpeg_encoder *encoder = NULL;
	if (vega_bqb_mpeg_check_params(arg_param))
	{
		printf("vega_bqb_mpeg_check_params fail\n");
		goto function_fail;
	}

	vega_bqb_mpeg_print_params(arg_param);

	encoder = new vega_bqb_mpeg_encoder;
	if ((!encoder) || (!encoder->create((const vegaff_codec_param *)arg_param)))
	{
		printf("encoder create fail\n");
		goto function_fail;
	}

	encoder->configure((const vegaff_codec_param *)arg_param);
	encoder->init();
	encoder->registEsPopCallback((void *)vega_bqb_mpeg_process_coded_frame);
	encoder->registVideoCaptureStartCallback((void *)vega_bqb_mpeg_signal_capture_start);
	encoder->start();

	if (encoder->_aborted)
	{
		printf("enc abort fail\n");
		goto function_fail;
	}

	return encoder;

function_fail:
	if (encoder)
	{
		encoder->destroy();
		delete encoder;
	}

	return NULL;
}

int vega_bqb_mpeg_encoder_encode(vegaffhandle_t enc, vegaff_picture_t *pic_in, vegaff_picture_t *pic_out)
{
	if (!enc)
		return -1;

	vega_bqb_mpeg_encoder *encoder = static_cast<vega_bqb_mpeg_encoder *>(enc);
	int                    loop = 0;
	int                    timeout = 1000;
	encoder->encode(pic_in);

	if (encoder->_aborted)
		return -1;

	// !pic_in indicates ffmpeg has no more input,
	// hang on here until at least one ES be popped.
	if (!pic_in)
	{
		if (encoder->_outFrameCnt < encoder->_inFrameCnt || encoder->_lastES == false)
		{
			while (loop < timeout)
			{
				if (!encoder->mp2Queue.empty())
					break;
				else
				{
					loop++;
					SLEEP_MICROSECOND(1000);
				}
			}

			if ((loop == timeout) && (encoder->_lastES == false))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device %d channel %d: waiting for lastES timeout, lastES = %s, input frame = %d, output "
						   "frame = %d\n",
						   encoder->_device, encoder->_channel, ((encoder->_lastES) ? "true" : "false"),
						   encoder->_inFrameCnt, encoder->_outFrameCnt);
				return -1;
			}
		}
	}

	if (!encoder->mp2Queue.empty())
	{
		vegaff_nal_t mpeg_pic = encoder->mp2Queue.pop();
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d from vega: output frame=%d\n", encoder->_device,
				   encoder->_channel, encoder->_outFrameCnt++);
		pic_out->is_last_pic = mpeg_pic.is_last_ES;
		pic_out->pts = mpeg_pic.pts;
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d: pic out pts=%ld\n", encoder->_device,
				   encoder->_channel, pic_out->pts);
		pic_out->dts = mpeg_pic.dts;
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d: pic out dts=%ld\n", encoder->_device,
				   encoder->_channel, pic_out->dts);
		pic_out->sliceType = mpeg_pic.sliceType;
		pic_out->pu8Addr = mpeg_pic.pu8Addr;
		pic_out->u32Length = mpeg_pic.u32Length;
		encoder->_esBufSize = encoder->_esBufSize + mpeg_pic.u32Length;
	}
	else
	{
		pic_out->pu8Addr = NULL;
		pic_out->u32Length = 0;
	}

	return 0;
}

int vega_bqb_mpeg_encoder_pushimage(vegaffhandle_t enc, vegaff_picture_t *pic_in)
{
	return vegaff_encoder_pushimage(static_cast<vega_bqb_mpeg_encoder *>(enc), pic_in);
}

int vega_bqb_mpeg_encoder_getes(vegaffhandle_t enc, vegaff_picture_t *pic_in, vegaff_picture_t *pic_out)
{
	if (!enc)
		return -1;

	vega_bqb_mpeg_encoder *encoder = static_cast<vega_bqb_mpeg_encoder *>(enc);
	int                    loop = 0;
	int                    timeout = 1000;

	if (!pic_in)
	{
		if (encoder->_outFrameCnt < encoder->_inFrameCnt || encoder->_lastES == false)
		{
			while (loop < timeout)
			{
				if (!encoder->mp2Queue.empty())
					break;
				else
				{
					loop++;
					SLEEP_MICROSECOND(1000);
				}
			}

			if ((loop == timeout) && (encoder->_lastES == false))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device %d channel %d: waiting for lastES timeout, lastES = %s, input frame = %d, output "
						   "frame = %d\n",
						   encoder->_device, encoder->_channel, ((encoder->_lastES) ? "true" : "false"),
						   encoder->_inFrameCnt, encoder->_outFrameCnt);
				return -1;
			}
		}
	}

	if (!encoder->mp2Queue.empty())
	{
		vegaff_nal_t mpeg_pic = encoder->mp2Queue.pop();
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d from vega: output frame=%d\n", encoder->_device,
				   encoder->_channel, encoder->_outFrameCnt++);
		pic_out->is_last_pic = mpeg_pic.is_last_ES;
		pic_out->pts = mpeg_pic.pts;
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d: pic out pts=%ld\n", encoder->_device,
				   encoder->_channel, pic_out->pts);
		pic_out->dts = mpeg_pic.dts;
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d: pic out dts=%ld\n", encoder->_device,
				   encoder->_channel, pic_out->dts);
		pic_out->sliceType = mpeg_pic.sliceType;
		pic_out->pu8Addr = mpeg_pic.pu8Addr;
		pic_out->u32Length = mpeg_pic.u32Length;
		encoder->_esBufSize = encoder->_esBufSize + mpeg_pic.u32Length;
	}
	else
	{
		pic_out->pu8Addr = NULL;
		pic_out->u32Length = 0;
	}

	return 0;
}

void vega_bqb_mpeg_encoder_close(vegaffhandle_t enc)
{
	if (enc)
	{
		vega_bqb_mpeg_encoder *encoder = static_cast<vega_bqb_mpeg_encoder *>(enc);
		encoder->stop();
		encoder->exit();
		encoder->destroy();
		encoder->flush();
		delete encoder;
	}
}

void vega_bqb_mpeg_encoder_start(vegaffhandle_t enc)
{
	vegaff_encoder_start(enc);
}

void vega_bqb_mpeg_encoder_stop(vegaffhandle_t enc)
{
	vegaff_encoder_stop(enc);
}

void vega_bqb_mpeg_encoder_reset(vegaffhandle_t enc)
{
	vegaff_encoder_reset(enc);
}

int vega_bqb_mpeg_encoder_setbitrate(vegaffhandle_t enc, uint32_t bitrate)
{
	return vegaff_encoder_setbitrate(enc, bitrate);
}

int vega_bqb_mpeg_encoder_setvbr(vegaffhandle_t enc, uint32_t max, uint32_t ave, uint32_t min)
{
	return vegaff_encoder_setvbr(enc, max, ave, min);
}

int vega_bqb_mpeg_encoder_forceidr(vegaffhandle_t enc)
{
	return vegaff_encoder_forceidr(enc);
}

int vega_bqb_mpeg_encoder_forceidrat(vegaffhandle_t enc, uint32_t pic_num)
{
	return vegaff_encoder_forceidrat(enc, pic_num);
}

int vega_bqb_mpeg_encoder_setframerate(vegaffhandle_t enc, uint32_t fps)
{
	return vegaff_encoder_setframerate(enc, fps);
}

int vega_bqb_mpeg_encoder_setframerateat(vegaffhandle_t enc, uint32_t fps, uint32_t pic_num)
{
	return vegaff_encoder_setframerateat(enc, fps, pic_num);
}
