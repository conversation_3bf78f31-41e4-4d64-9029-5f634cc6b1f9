/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#define __STDC_FORMAT_MACROS
#elif _WIN32
#include <Windows.h>
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#define __STDC_FORMAT_MACROS
#endif
#include <inttypes.h>
#include <cmath>
#include "encoder264.h"
#include "../common/common.h"
#include "../common/sei.h"
#include "../common/message_option.h"
#include "../common/frame.h"

// using namespace boost::interprocess;  BBright
/*
	AVC resolution support list:
	320x180
	352x288
	416x240
	480x270
	720x480
	720x576
	960x540
	1280x720
	1920x1080
	2048x1080
	2560x1440
	3840x2160
	4096x2160
*/
extern "C" const stResolution gstRes264[];
const stResolution            gstRes264[] = {
    //  { "320x180", 320, 180, API_VEGA_BQB_RESOLUTION_320x180, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "320x240", 320, 240, API_VEGA_BQB_RESOLUTION_320x240, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "352x288", 352, 288, API_VEGA_BQB_RESOLUTION_352x288, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "384x160", 384, 160, API_VEGA_BQB_RESOLUTION_384x160, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "416x240", 416, 240, API_VEGA_BQB_RESOLUTION_416x240, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "480x200", 480, 200, API_VEGA_BQB_RESOLUTION_480x200, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "480x270", 480, 270, API_VEGA_BQB_RESOLUTION_480x270, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "480x360", 480, 360, API_VEGA_BQB_RESOLUTION_480x360, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "528x480", 528, 480, API_VEGA_BQB_RESOLUTION_528x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "544x480", 544, 480, API_VEGA_BQB_RESOLUTION_544x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "640x360", 640, 360, API_VEGA_BQB_RESOLUTION_640x360, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    //  { "640x480", 640, 480, API_VEGA_BQB_RESOLUTION_640x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    {"720x480", 720, 480, API_VEGA_BQB_RESOLUTION_720x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    //  { "720x540", 720, 540, API_VEGA_BQB_RESOLUTION_720x540, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE },
    {"720x576", 720, 576, API_VEGA_BQB_RESOLUTION_720x576, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    //  { "848x480", 848, 480, API_VEGA_BQB_RESOLUTION_848x480, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING },
    //  { "864x360", 864, 360, API_VEGA_BQB_RESOLUTION_864x360, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING },
    //  { "960x400", 960, 400, API_VEGA_BQB_RESOLUTION_960x400, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING },
    //  { "960x540", 960, 540, API_VEGA_BQB_RESOLUTION_960x540, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING },
    //  { "960x720", 960, 720, API_VEGA_BQB_RESOLUTION_960x720, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING },
    //  { "1280x532", 1280, 532, API_VEGA_BQB_RESOLUTION_1280x532, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //  API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING },
    {"1280x720", 1280, 720, API_VEGA_BQB_RESOLUTION_1280x720, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    //   { "1280x1024", 1280, 1024,API_VEGA_BQB_RESOLUTION_1280x1024, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //   API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING },
    //   { "1280x1080", 1280, 1080, API_VEGA_BQB_RESOLUTION_1280x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //   API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING },
    //   { "1440x1080", 1440, 1080, API_VEGA_BQB_RESOLUTION_1440x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //   API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING },
    //   { "1920x800", 1920, 800, API_VEGA_BQB_RESOLUTION_1920x800,API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
    //   API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING },
    {"1920x1080", 1920, 1080, API_VEGA_BQB_RESOLUTION_1920x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"2048x1080", 2048, 1080, API_VEGA_BQB_RESOLUTION_2048x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    //   { "2160x1620", 2160, 1620, API_VEGA_BQB_RESOLUTION_2160x1620, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
    //   API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K },
    //   { "2400x1000", 2400, 1000, API_VEGA_BQB_RESOLUTION_2400x1000, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
    //   API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K },
    {"2560x1440", 2560, 1440, API_VEGA_BQB_RESOLUTION_2560x1440, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    //   { "2880x2160", 2880, 2160, API_VEGA_BQB_RESOLUTION_2880x2160, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
    //   API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K },
    //   { "3840x1606", 3840, 1606, API_VEGA_BQB_RESOLUTION_3840x1606, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
    //   API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K },
    {"3840x2160", 3840, 2160, API_VEGA_BQB_RESOLUTION_3840x2160, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    {"4096x2160", 4096, 2160, API_VEGA_BQB_RESOLUTION_4096x2160, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    {"user-defined", 0, 0, API_VEGA_BQB_RESOLUTION_4096x2160, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K}};
extern "C" const int gstRes264_count;
const int            gstRes264_count = sizeof(gstRes264) / sizeof(stResolution);

vega_bqb_avc_encoder::vega_bqb_avc_encoder()
{
	gst_res_table = (const stResolution *)gstRes264;
	gst_res_count = gstRes264_count;
}

int vega_bqb_avc_encoder::configure(const vegaff_codec_param *arg_param)
{
	API_VEGA_BQB_RET    bqbret;
	vega_bqb_avc_param *lparam;

	if (_aborted)
		return -1;

	if (arg_param)
	{
		memcpy(_param, arg_param, arg_param->objsize);
	}
	lparam = (vega_bqb_avc_param *)_param;
	_device = (API_VEGA_BQB_DEVICE_E)lparam->device;
	_channel = (API_VEGA_BQB_CHN_E)lparam->channel;
	_apiInitParam->eCodecType = API_VEGA_BQB_CODEC_TYPE_AVC;
	_apiInitParam->eOutputFmt = API_VEGA_BQB_STREAM_OUTPUT_FORMAT_ES;
	//	_apiInitParam->eDbgLevel = (API_VEGA_BQB_DBG_LEVEL_E)p->dbgLevel;

	API_VEGA_BQB_AVC_INIT_PARAM_T *ap = &_apiInitParam->tAvcParam;
	ap->eInputMode = (API_VEGA_BQB_INPUT_MODE_E)lparam->inputMode;
	ap->eInputPort = API_VEGA_BQB_VIF_MODE_INPUT_PORT_DEFAULT;
	ap->eProfile = (API_VEGA_BQB_AVC_PROFILE_E)lparam->profile;
	ap->eLevel = (API_VEGA_BQB_AVC_LEVEL_E)lparam->levelIdc;
	ap->eEntropyCoding = (API_VEGA_BQB_AVC_ENTROPY_CODING_E)(lparam->bCabac ? 0 : 1);
	API_VEGA_BQB_ENCODE_CONFIG_T enc_cfg;
	int                          round = 16;

	memset(&enc_cfg, 0, sizeof(enc_cfg));
	if (lparam->sourceWidth <= 720)
		round = 32;
	ap->eResolution = API_VEGA_BQB_RESOLUTION_INVALID;
	for (int i = 0; i < gst_res_count; i++)
	{
		if (lparam->sourceWidth == gst_res_table[i].width && lparam->sourceHeight == gst_res_table[i].height)
		{
			if ((lparam->fpsNum / lparam->fpsDenom) <= 30)
				enc_cfg.eMode = gst_res_table[i].eMode2;
			else
				enc_cfg.eMode = gst_res_table[i].eMode1;
			ap->eResolution = gst_res_table[i].eRes;
			break;
		}
		else if (gst_res_table[i].width == 0 && gst_res_table[i].height == 0)
		{
			ap->eResolution = API_VEGA_BQB_RESOLUTION_USER_DEFINED;
			ap->u32UserDefinedSourceWidth = ROUND_UP(lparam->sourceWidth, round);
			ap->u32UserDefinedSourceHeight = ROUND_UP(lparam->sourceHeight, round);

			for (int j = 0; j < (int)gst_res_count; j++)
			{
				if (lparam->sourceWidth <= gst_res_table[j].width && lparam->sourceHeight <= gst_res_table[j].height)
				{
					if ((lparam->fpsNum / lparam->fpsDenom) <= 30)
						enc_cfg.eMode = gst_res_table[j].eMode2;
					else
						enc_cfg.eMode = gst_res_table[j].eMode1;
					break;
				}
			}
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "WxH:%ux%u\n", ap->u32UserDefinedSourceWidth,
					   ap->u32UserDefinedSourceHeight);
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "Encode mode:%d\n", enc_cfg.eMode);
		}
	}
	if (ap->eResolution == API_VEGA_BQB_RESOLUTION_INVALID)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "unsupported resolution\n");
		_aborted = true;
		return -2;
	}

	if (lparam->encodeMode != 0) // it is not auto mode
	{
		enc_cfg.eMode = (API_VEGA_BQB_DEVICE_ENC_MODE_E)(lparam->encodeMode - 1);
	}

	bqbret = VEGA_BQB_ENC_ConfigDeviceMode(_device, &enc_cfg);
	if ((int)bqbret != 0)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Failed to change mode (%d)\n", (int)bqbret);
		_aborted = true;
		return -3;
	}
	switch (lparam->internalCsp)
	{
	case VEGA_BQB_CSP_I420:
		switch (lparam->internalBitDepth)
		{
		case 8:
			if (lparam->inputMode) // vif mode, M30 only supports NV12, it cannot support YUV420
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV12;
			else
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I420;
			break;
		case 10:
			ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I0AL;
			break;
		}
		ap->eChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_420;
		break;
	case VEGA_BQB_CSP_NV12:
		ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV12;
		ap->eChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_420;
		break;

	case VEGA_BQB_CSP_I422:
		switch (lparam->internalBitDepth)
		{
		case 8:
			if (lparam->inputMode) // vif mode, M30 only supports NV16, it cannot support YUV420
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV16;
			else
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I420;
			break;
		case 10:
			ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I2AL;
			break;
		}
		ap->eChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_422;
		break;
	case VEGA_BQB_CSP_NV16:
		ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV16;
		ap->eChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_422;
		break;
	}

	if (lparam->inputMode)
	{
		if (!lparam->interlaceMode)
		{
			ap->tChromaConvertInfo.s11C420Coef[0] = -3;
			ap->tChromaConvertInfo.s11C420Coef[1] = -19;
			ap->tChromaConvertInfo.s11C420Coef[2] = 34;
			ap->tChromaConvertInfo.s11C420Coef[3] = 500;
			ap->tChromaConvertInfo.s11C420Coef[4] = 500;
			ap->tChromaConvertInfo.s11C420Coef[5] = 34;
			ap->tChromaConvertInfo.s11C420Coef[6] = -19;
			ap->tChromaConvertInfo.s11C420Coef[7] = -3;
		}
		else
		{
			ap->tChromaConvertInfo.s11C420Coef[0] = -8;
			ap->tChromaConvertInfo.s11C420Coef[1] = -26;
			ap->tChromaConvertInfo.s11C420Coef[2] = 115;
			ap->tChromaConvertInfo.s11C420Coef[3] = 586;
			ap->tChromaConvertInfo.s11C420Coef[4] = 409;
			ap->tChromaConvertInfo.s11C420Coef[5] = -48;
			ap->tChromaConvertInfo.s11C420Coef[6] = -4;
			ap->tChromaConvertInfo.s11C420Coef[7] = 0;
		}
	}

	if (lparam->vui.bEnableOverscanInfoPresentFlag && !lparam->vui.bEnableOverscanAppropriateFlag)
		ap->eOverScan = API_VEGA_BQB_OVERSCAN_INFO_INAPPROPRIATE;
	else if (lparam->vui.bEnableOverscanInfoPresentFlag && lparam->vui.bEnableOverscanAppropriateFlag)
		ap->eOverScan = API_VEGA_BQB_OVERSCAN_INFO_APPROPRIATE;
	else
		ap->eOverScan = API_VEGA_BQB_OVERSCAN_INFO_NOT_PRESENT;

	ap->tVideoSignalType.bPresentFlag = lparam->vui.bEnableVideoSignalTypePresentFlag != 0;
	ap->tVideoSignalType.eVideoFormat = (API_VEGA_BQB_VIDEO_FORMAT_E)lparam->vui.videoFormat;
	ap->tVideoSignalType.bVideoFullRange = lparam->vui.bEnableVideoFullRangeFlag != 0;
	ap->tVideoSignalType.tColorDesc.bPresentFlag = lparam->vui.bEnableColorDescriptionPresentFlag != 0;
	ap->tVideoSignalType.tColorDesc.eColorPrimaries = (API_VEGA_BQB_COLOR_PRIMARY_E)lparam->vui.colorPrimaries;
	ap->tVideoSignalType.tColorDesc.eTransferCharacteristics =
		(API_VEGA_BQB_TRANSFER_CHAR_E)lparam->vui.transferCharacteristics;
	ap->tVideoSignalType.tColorDesc.eMatrixCoeff = (API_VEGA_BQB_MATRIX_COEFFS_E)lparam->vui.matrixCoeffs;

	ap->tChromaLocation.bChromaLoc = lparam->vui.bEnableChromaLocInfoPresentFlag != 0;
	ap->tChromaLocation.eTopFieldLoc = (API_VEGA_BQB_CHROMA_SAMPLE_POSITION_E)lparam->vui.chromaSampleLocTypeTopField;
	ap->tChromaLocation.eBotFieldLoc =
		(API_VEGA_BQB_CHROMA_SAMPLE_POSITION_E)lparam->vui.chromaSampleLocTypeBottomField;

	if (lparam->internalBitDepth == 8)
		ap->eBitDepth = API_VEGA_BQB_BIT_DEPTH_8;
	else if (lparam->internalBitDepth == 10)
		ap->eBitDepth = API_VEGA_BQB_BIT_DEPTH_10;

	ap->bInterlace = (lparam->interlaceMode == 1) ? true : false;
	ap->bAspectRatioInfoPresent = true;

	if (lparam->vui.aspectRatioIdc)
	{
		ap->eAspectRatioIdc = (API_VEGA_BQB_AVC_ASPECT_RATIO_IDC_E)lparam->vui.aspectRatioIdc;
		ap->u32SarWidth = (uint32_t)lparam->vui.sarWidth;
		ap->u32SarHeight = (uint32_t)lparam->vui.sarHeight;
	}
	else
	{
		if (lparam->sourceWidth == 720)
		{
			if (lparam->sourceHeight == 576)
			{
				ap->eAspectRatioIdc = API_VEGA_BQB_AVC_ASPECT_RATIO_IDC_2;
			}
			else if (lparam->sourceHeight == 480)
			{
				ap->eAspectRatioIdc = API_VEGA_BQB_AVC_ASPECT_RATIO_IDC_3;
			}
			else
			{
				ap->eAspectRatioIdc = API_VEGA_BQB_AVC_ASPECT_RATIO_IDC_1;
			}
		}
		else if (lparam->sourceWidth == 416 || lparam->sourceWidth == 352)
		{
			ap->eAspectRatioIdc = API_VEGA_BQB_AVC_ASPECT_RATIO_IDC_0;
		}
		else
		{
			ap->eAspectRatioIdc = API_VEGA_BQB_AVC_ASPECT_RATIO_IDC_1;
		}
	}

	ap->tCrop.u32CropLeft = lparam->vui.defDispWinLeftOffset;
	ap->tCrop.u32CropRight = lparam->vui.defDispWinRightOffset;
	ap->tCrop.u32CropTop = lparam->vui.defDispWinTopOffset;
	ap->tCrop.u32CropBottom = lparam->vui.defDispWinBottomOffset;

	if (!lparam->vui.defDispWinLeftOffset && !lparam->vui.defDispWinRightOffset && !lparam->vui.defDispWinTopOffset &&
		!lparam->vui.defDispWinBottomOffset && ((lparam->sourceWidth == 1920) || (lparam->sourceWidth == 2048)) &&
		(lparam->sourceHeight == 1080))
	{
		ap->tCrop.u32CropLeft = 0;
		ap->tCrop.u32CropRight = 0;
		ap->tCrop.u32CropTop = 0;
		ap->tCrop.u32CropBottom = 8;
	}
	if (ap->eResolution == API_VEGA_BQB_RESOLUTION_USER_DEFINED)
	{
		ap->tCrop.u32CropLeft = 0;
		ap->tCrop.u32CropRight = ROUND_UP(lparam->sourceWidth, round) - lparam->sourceWidth;
		ap->tCrop.u32CropTop = 0;
		ap->tCrop.u32CropBottom = ROUND_UP(lparam->sourceHeight, round) - lparam->sourceHeight;
	}

	switch (lparam->bOpenGOP)
	{
	case 0:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_ALL;
		break;

	case 1:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_FIRST;
		break;

	case 2:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_TWO;
		break;

	case 3:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_100;
		break;
	}

	if (lparam->inputMode == API_VEGA_BQB_INPUT_MODE_DATA)
		ap->ePtsMode = API_VEGA_BQB_PTS_MODE_USER;
	else if (lparam->inputMode == API_VEGA_BQB_INPUT_MODE_VIF_SQUARE ||
			 lparam->inputMode == API_VEGA_BQB_INPUT_MODE_VIF_2_SAMPLE_INTERLEAVE)
		ap->ePtsMode = API_VEGA_BQB_PTS_MODE_VANC;

	uint32_t fps = lparam->fpsNum / lparam->fpsDenom;

	if (lparam->interlaceMode && fps <= 30) // AVC interlace mode, fps must be 60/59.94/50/(48?)
	{
		if (fps == 30 || fps == 29)
		{
			fps += 30;
		}
		else
		{
			fps = fps * 2;
		}
	}
	if (fps != 15 && fps != 23 && fps != 24 && fps != 25 && fps != 29 && fps != 30 && fps != 50 && fps != 59 &&
		fps != 60)
	{
		ap->eTargetFrameRate = API_VEGA_BQB_FPS_CUSTOMED;
		ap->tCustomedFrameRateInfo.u32TimeScale = lparam->fpsNum;
		ap->tCustomedFrameRateInfo.u32NumUnitsInTicks = lparam->fpsDenom;
	}
	else
		ap->eTargetFrameRate = (API_VEGA_BQB_FPS_E)fps;

	ap->eGopType = (API_VEGA_BQB_GOP_TYPE_E)lparam->gopType;
	ap->eGopHierarchy = API_VEGA_BQB_GOP_HIERARCHY;
	ap->eGopSize = (API_VEGA_BQB_GOP_SIZE_E)lparam->keyframeMax;
	ap->eBFrameNum = (API_VEGA_BQB_B_FRAME_NUM_E)lparam->bframes;
	ap->bDisableTemporalId = false;
	ap->bDisableSceneChangeDetect = (lparam->scenecutThreshold == 1) ? false : true;
	if (lparam->rc.bStrictCbr)
		lparam->rc.rateControlMode = VEGA_BQB_RC_CAPPED_VBR;
	ap->eRateCtrlAlgo = (API_VEGA_BQB_RATE_CTRL_ALGO_E)lparam->rc.rateControlMode;
	ap->u32FillerTriggerLevel = (uint32_t)lparam->rc.fillerrate;
	ap->u32Bitrate = (uint32_t)lparam->rc.bitrate;
	ap->u32MaxVBR = (uint32_t)lparam->rc.vbrMaxBitrate;
	ap->u32AveVBR = (uint32_t)lparam->rc.vbrAveBitrate;
	ap->u32MinVBR = (uint32_t)lparam->rc.vbrMinBitrate;
	ap->u32CpbDelay = (uint32_t)(lparam->cpbDelay * 90000);
	ap->tCoding.bDisableDeblocking = (lparam->bEnableLoopFilter == 1) ? false : true;
	ap->tCoding.eWeightedPrediction =
		(API_VEGA_BQB_WEIGHTED_PREDICTION_E)(lparam->bEnableWeightedBiPred && lparam->bEnableWeightedPred);
	ap->eRobustMode = (API_VEGA_BQB_VIF_ROBUST_MODE_E)(lparam->robustMode);
#if (HAS_AdvancedFeature != 0)
	ap->bIDRDisplayOrderFirst = (lparam->iDRDisplayOrderFirst == 1) ? true : false;
#endif
	return 0;
}

void vega_bqb_avc_encoder::init()
{
	vega_bqb_avc_param *  p = (vega_bqb_avc_param *)_param;
	API_VEGA_BQB_STATUS_E st;

	if (_aborted)
		return;

	st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_OFF)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Encoder is not off.\n");
		return;
	}

	VEGA_BQB_ENC_SetDbgMsgLevel(_device, _channel, (API_VEGA_BQB_DBG_LEVEL_E)p->dbgLevel);

	if ((p->inputHeight != p->sourceHeight || p->inputWidth != p->sourceWidth) && !p->interlaceMode)
	{
		API_VEGA_BQB_XC_INIT_PARAM_T   api_xc_init_param;
		API_VEGA_BQB_AVC_INIT_PARAM_T *ap = &_apiInitParam->tAvcParam;
		API_VEGA_BQB_RET               api_ret;
		memset(&api_xc_init_param, 0, sizeof(API_VEGA_BQB_XC_INIT_PARAM_T));

		api_xc_init_param.eInputSource = API_VEGA_BQB_XC_INPUT_SOURCE_PCIE;
		api_xc_init_param.u32SourceId = _channel;
		api_xc_init_param.eInputResolution = getResolution(p->inputWidth, p->inputHeight);
		api_xc_init_param.eOutputResolution = getResolution(p->sourceWidth, p->sourceHeight);
		api_xc_init_param.eChromaFmt = (API_VEGA_BQB_CHROMA_FORMAT_E)ap->eChromaFmt;
		api_xc_init_param.eBitDepth = (API_VEGA_BQB_BIT_DEPTH_E)ap->eBitDepth;

		api_ret = VEGA_BQB_XC_Init((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel, &api_xc_init_param);

		if (api_ret != API_VEGA_BQB_RET_SUCCESS)
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to init VEGA_BQB_AVC scaler \n", _device,
					   _channel);
			_aborted = true;
			return;
		}
		else
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "Init XC %dx%d -> %dx%d , chroma:%d BitDepth:%d\n", p->inputWidth,
					   p->inputHeight, p->sourceWidth, p->sourceHeight, ap->eChromaFmt, ap->eBitDepth);
	}

	API_VEGA_BQB_INIT_PARAM_T t_init_param;
	memset(&t_init_param, 0, sizeof(API_VEGA_BQB_INIT_PARAM_T));
	t_init_param.eCodecType = (API_VEGA_BQB_CODEC_TYPE_E)_apiInitParam->eCodecType;
	t_init_param.eOutputFmt = (API_VEGA_BQB_STREAM_OUTPUT_FORMAT_E)_apiInitParam->eOutputFmt;

	API_VEGA_BQB_AVC_INIT_PARAM_T *p_avc_param = &t_init_param.tAvcParam;
	p_avc_param->eInputMode = (API_VEGA_BQB_INPUT_MODE_E)_apiInitParam->tAvcParam.eInputMode;
	p_avc_param->eInputPort = (API_VEGA_BQB_VIF_MODE_INPUT_PORT_E)_apiInitParam->tAvcParam.eInputPort;
	p_avc_param->eRobustMode = (API_VEGA_BQB_VIF_ROBUST_MODE_E)_apiInitParam->tAvcParam.eRobustMode;
	p_avc_param->eProfile = (API_VEGA_BQB_AVC_PROFILE_E)_apiInitParam->tAvcParam.eProfile;
	p_avc_param->eLevel = (API_VEGA_BQB_AVC_LEVEL_E)_apiInitParam->tAvcParam.eLevel;
	p_avc_param->eEntropyCoding = (API_VEGA_BQB_AVC_ENTROPY_CODING_E)_apiInitParam->tAvcParam.eEntropyCoding;
	p_avc_param->eResolution = (API_VEGA_BQB_RESOLUTION_E)_apiInitParam->tAvcParam.eResolution;
	if (p_avc_param->eResolution == API_VEGA_BQB_RESOLUTION_USER_DEFINED)
	{
		p_avc_param->u32UserDefinedSourceWidth = _apiInitParam->tAvcParam.u32UserDefinedSourceWidth;
		p_avc_param->u32UserDefinedSourceHeight = _apiInitParam->tAvcParam.u32UserDefinedSourceHeight;
	}
	p_avc_param->bAspectRatioInfoPresent = _apiInitParam->tAvcParam.bAspectRatioInfoPresent;
	p_avc_param->eAspectRatioIdc = (API_VEGA_BQB_AVC_ASPECT_RATIO_IDC_E)_apiInitParam->tAvcParam.eAspectRatioIdc;
	p_avc_param->u32SarWidth = _apiInitParam->tAvcParam.u32SarWidth;
	p_avc_param->u32SarHeight = _apiInitParam->tAvcParam.u32SarHeight;
	p_avc_param->bDisableTimingInfoPresent = _apiInitParam->tAvcParam.bDisableTimingInfoPresent;
	p_avc_param->eFormat = (API_VEGA_BQB_IMAGE_FORMAT_E)_apiInitParam->tAvcParam.eFormat;
	p_avc_param->eChromaFmt = (API_VEGA_BQB_CHROMA_FORMAT_E)_apiInitParam->tAvcParam.eChromaFmt;
	memcpy(&p_avc_param->tChromaConvertInfo, &_apiInitParam->tAvcParam.tChromaConvertInfo,
		   sizeof(API_VEGA_BQB_CHROMA_CONVERT_INFO_T));
	p_avc_param->eOverScan = (API_VEGA_BQB_OVERSCAN_INFO_E)_apiInitParam->tAvcParam.eOverScan;
	memcpy(&p_avc_param->tVideoSignalType, &_apiInitParam->tAvcParam.tVideoSignalType,
		   sizeof(API_VEGA_BQB_VIDEO_SIGNAL_TYPE_T));
	memcpy(&p_avc_param->tChromaLocation, &_apiInitParam->tAvcParam.tChromaLocation,
		   sizeof(API_VEGA_BQB_CHROMA_LOC_INFO_T));
	p_avc_param->eBitDepth = (API_VEGA_BQB_BIT_DEPTH_E)_apiInitParam->tAvcParam.eBitDepth;
	p_avc_param->bInterlace = _apiInitParam->tAvcParam.bInterlace;
	p_avc_param->bDisableSceneChangeDetect = _apiInitParam->tAvcParam.bDisableSceneChangeDetect;
	p_avc_param->eScPicType = (API_VEGA_BQB_SC_PICTYPE_E)_apiInitParam->tAvcParam.eScPicType;
	memcpy(&p_avc_param->tCrop, &_apiInitParam->tAvcParam.tCrop, sizeof(API_VEGA_BQB_CROP_INFO_T));
	p_avc_param->eTargetFrameRate = (API_VEGA_BQB_FPS_E)_apiInitParam->tAvcParam.eTargetFrameRate;
	memcpy(&p_avc_param->tCustomedFrameRateInfo, &_apiInitParam->tAvcParam.tCustomedFrameRateInfo,
		   sizeof(API_VEGA_BQB_FPS_CUSTOMED_T));
	p_avc_param->ePtsMode = (API_VEGA_BQB_PTS_MODE_E)_apiInitParam->tAvcParam.ePtsMode;
	p_avc_param->eIDRFrameNum = (API_VEGA_BQB_IDR_FRAME_NUM_E)_apiInitParam->tAvcParam.eIDRFrameNum;
#if (HAS_AdvancedFeature != 0)
	p_avc_param->bIDRDisplayOrderFirst = _apiInitParam->tAvcParam.bIDRDisplayOrderFirst;
#endif
	p_avc_param->eGopType = (API_VEGA_BQB_GOP_TYPE_E)_apiInitParam->tAvcParam.eGopType;
	p_avc_param->eGopHierarchy = (API_VEGA_BQB_GOP_HIERARCHY_E)_apiInitParam->tAvcParam.eGopHierarchy;
	p_avc_param->eGopSize = (API_VEGA_BQB_GOP_SIZE_E)_apiInitParam->tAvcParam.eGopSize;
	p_avc_param->eBFrameNum = (API_VEGA_BQB_B_FRAME_NUM_E)_apiInitParam->tAvcParam.eBFrameNum;
	p_avc_param->bDisableTemporalId = _apiInitParam->tAvcParam.bDisableTemporalId;
	p_avc_param->eRateCtrlAlgo = (API_VEGA_BQB_RATE_CTRL_ALGO_E)_apiInitParam->tAvcParam.eRateCtrlAlgo;
	p_avc_param->u32FillerTriggerLevel = _apiInitParam->tAvcParam.u32FillerTriggerLevel;
	p_avc_param->u32Bitrate = _apiInitParam->tAvcParam.u32Bitrate;
	p_avc_param->u32MaxVBR = _apiInitParam->tAvcParam.u32MaxVBR;
	p_avc_param->u32AveVBR = _apiInitParam->tAvcParam.u32AveVBR;
	p_avc_param->u32MinVBR = _apiInitParam->tAvcParam.u32MinVBR;
	p_avc_param->u32CpbDelay = _apiInitParam->tAvcParam.u32CpbDelay;
	p_avc_param->tCoding.bDisableDeblocking = _apiInitParam->tAvcParam.tCoding.bDisableDeblocking;
	p_avc_param->tCoding.eWeightedPrediction =
		(API_VEGA_BQB_WEIGHTED_PREDICTION_E)_apiInitParam->tAvcParam.tCoding.eWeightedPrediction;
	p_avc_param->tCoding.bDisableMinQpCtrl = _apiInitParam->tAvcParam.tCoding.bDisableMinQpCtrl;
#if (HAS_AdvancedFeature != 0)
	p_avc_param->tCoding.eNumSliceInPic = (API_VEGA_BQB_SLICE_NUM_E)_apiInitParam->tAvcParam.tCoding.eNumSliceInPic;
#endif
	if (VEGA_BQB_ENC_Init((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel, &t_init_param))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to init VEGA_BQB_AVC encoder\n", _device,
				   _channel);
		_aborted = true;
		return;
	}
	VEGA_BQB_ENC_SetTimeStampUnit(_device, _channel, API_VEGA_BQB_TIMEBASE_90KHZ);
}

void vega_bqb_avc_encoder::registEsPopCallback(void *pfunc_callback)
{
	if (_aborted)
		return;

	API_VEGA_BQB_STATUS_E st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_STANDBY)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Encoder isn't in standby mode\n");
		return;
	}

	if (VEGA_BQB_ENC_RegisterAvcCallback((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
										 (API_VEGA_BQB_AVC_CALLBACK)pfunc_callback, (void *)this))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to register pop-es callback function\n",
				   _device, _channel);
		_aborted = true;
		return;
	}
}

int vega_bqb_avc_encoder::encode(const vegaff_picture_t *pic_in)
{
	if (_aborted)
		return -1;

	vegaff_codec_param *p = _param;
	API_VEGA_BQB_IMG_T  img, last_img;

	if (p->inputMode)
	{ /* operates at VIF mode */
		if (!pic_in)
		{
			deregistVideoCaptureStartCallback();
			stop();
			TRACE();
		}
		return 0;
	}

	if (pic_in && !_pendingFrame)
	{
		_picInSize = _inFrame->getPictureSize(pic_in);
		_picInPts = pic_in->pts;
		_picInFmt = (API_VEGA_BQB_IMAGE_FORMAT_E)pic_in->imgFormat;
		_inFrame->copyFromPicture(this, pic_in);
		_inFrame->contextReinit(pic_in);
		_pendingFrame = true;
	}
	else if (pic_in && _pendingFrame)
	{
		if (_inFrame->_sizeChanged)
		{
			uint8_t *buf = (uint8_t *)vegaff_malloc(_picInSize);
			if (!buf)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device:%d channel:%d image backup allocation for context change failure, aborting\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}

			memcpy(buf, _vrawBuf, _picInSize);
			memset(_vrawBuf, 0, MAX_VRAW_BUF_SIZE);

			VEGA_BQB_ENC_Rescale(_vrawBuf, buf, _inFrame->_width0, _inFrame->_height0, _inFrame->_width1,
								 _inFrame->_height1);

			vegaff_free(buf);
		}

		img.pu8Addr = (uint8_t *)_vrawBuf;
		img.u32Size = _inFrame->_maxPictureSize;
		img.pts = (uint64_t)getPicInPts();
		img.eTimeBase = API_VEGA_BQB_TIMEBASE_90KHZ;
		img.bLastFrame = false;
		if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I0AL)
		{
			img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP01;
		}
		else if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I2AL)
		{
			img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP21;
		}
		else
			img.eFormat = _picInFmt;

		img.u32SeiNum = 0;
		img.bSeiPassThrough = false;

		if (_inFrame->_user_data_registered_itu_t_t35)
		{
			img.u32SeiNum = 1;
			img.bSeiPassThrough = false;

			if (writeUserDataRegisteredSEI(img.tSeiParam) < 0)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to write UserDataRegisteredSEI\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}
		}

		if (VEGA_BQB_ENC_PushImage(_device, _channel, &img))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
			_aborted = true;
			return -1;
		}

		_picInSize = _inFrame->getPictureSize(pic_in);
		_picInPts = pic_in->pts;
		_picInFmt = (API_VEGA_BQB_IMAGE_FORMAT_E)pic_in->imgFormat;
		_inFrame->copyFromPicture(this, pic_in);

		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega input frame=%d\n", _device, _channel,
				   _inFrameCnt++);
		_inFrame->contextReinit(pic_in);
		_pendingFrame = true;
	}
	else if (!pic_in && _pendingFrame)
	{
		if (_inFrame->_sizeChanged)
		{
			uint8_t *buf = (uint8_t *)vegaff_malloc(_picInSize);
			if (!buf)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device:%d channel:%d image backup allocation for context change failure, aborting\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}

			memcpy(buf, _vrawBuf, _picInSize);
			memset(_vrawBuf, 0, MAX_VRAW_BUF_SIZE);

			VEGA_BQB_ENC_Rescale(_vrawBuf, buf, _inFrame->_width0, _inFrame->_height0, _inFrame->_width1,
								 _inFrame->_height1);

			vegaff_free(buf);
		}

		last_img.pu8Addr = (uint8_t *)_vrawBuf;
		last_img.u32Size = _inFrame->_maxPictureSize;
		last_img.pts = (uint64_t)getPicInPts();
		last_img.eTimeBase = API_VEGA_BQB_TIMEBASE_90KHZ;
		last_img.bLastFrame = true;

		if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I0AL)
		{
			last_img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP01;
		}
		else if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I2AL)
		{
			last_img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP21;
		}
		else
			last_img.eFormat = _picInFmt;

		last_img.u32SeiNum = 0;
		last_img.bSeiPassThrough = false;

		if (_inFrame->_user_data_registered_itu_t_t35)
		{
			last_img.u32SeiNum = 1;
			last_img.bSeiPassThrough = false;

			if (writeUserDataRegisteredSEI(last_img.tSeiParam) < 0)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to write UserDataRegisteredSEI\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}
		}

		if (p->interlaceMode && (_inFrameCnt % 2 == 0))
		{
			last_img.bLastFrame = false;
			if (VEGA_BQB_ENC_PushImage((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
									   (API_VEGA_BQB_IMG_T *)&last_img))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
				_aborted = true;
				return -1;
			}
			vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega input frame=%d\n", _device, _channel,
					   _inFrameCnt++);
			_picInPts += 1;
			last_img.pts = (uint64_t)getPicInPts();
			last_img.bLastFrame = true;
			if (VEGA_BQB_ENC_PushImage((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
									   (API_VEGA_BQB_IMG_T *)&last_img))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
				_aborted = true;
				return -1;
			}
			vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega last_frame, input frame=%d\n", _device,
					   _channel, _inFrameCnt++);
			_pendingFrame = false;
			return 0;
		}

		if (VEGA_BQB_ENC_PushImage((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
								   (API_VEGA_BQB_IMG_T *)&last_img))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
			_aborted = true;
			return -1;
		}

		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega last_frame, input frame=%d\n", _device,
				   _channel, _inFrameCnt++);

		_pendingFrame = false;
	}
	else if (!pic_in && !_pendingFrame && !_inFrameCnt && !_outFrameCnt)
	{
		TRACE();
		_aborted = true;
		return -1;
	}

	return 0;
}

void vega_bqb_avc_encoder::flush()
{
	while (!_esQueue.empty())
	{
		Queue<vegaff_nal_t> tempQueue = _esQueue.pop();

		while (!tempQueue.empty())
		{
			tempQueue.pop();
		}
	}
}

int64_t vega_bqb_encoder::getPicOutDts(int64_t dts_90khz)
{
	int64_t dts;
	dts = ts_extent_incrstep(&dts_64b, dts_90khz);
	return dts;
}
