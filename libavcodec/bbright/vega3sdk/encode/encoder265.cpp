/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#define __STDC_FORMAT_MACROS
#elif _WIN32
#include <Windows.h>
#include <libvega_bqb_api/LIB_MISC_WIN32.h>
#define __STDC_FORMAT_MACROS
#else
#endif

#include <inttypes.h>

#include "encoder265.h"
#include "../common/common.h"
#include "../common/sei.h"
#include "../common/frame.h"
#include "../common/message_option.h"

#include <libvega_bqb_api/VEGA_BQB_types.h>
#include <libvega_bqb_api/VEGA_BQB_encoder.h>

using namespace boost::interprocess;

extern "C" const stResolution gstRes265[];
const stResolution            gstRes265[] = {
    {"320x180", 320, 180, API_VEGA_BQB_RESOLUTION_320x180, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"320x240", 320, 240, API_VEGA_BQB_RESOLUTION_320x240, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"352x288", 352, 288, API_VEGA_BQB_RESOLUTION_352x288, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"384x160", 384, 160, API_VEGA_BQB_RESOLUTION_384x160, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"416x240", 416, 240, API_VEGA_BQB_RESOLUTION_416x240, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"480x200", 480, 200, API_VEGA_BQB_RESOLUTION_480x200, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"480x270", 480, 270, API_VEGA_BQB_RESOLUTION_480x270, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"480x360", 480, 360, API_VEGA_BQB_RESOLUTION_480x360, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"528x480", 528, 480, API_VEGA_BQB_RESOLUTION_528x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"544x480", 544, 480, API_VEGA_BQB_RESOLUTION_544x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"640x360", 640, 360, API_VEGA_BQB_RESOLUTION_640x360, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"640x480", 640, 480, API_VEGA_BQB_RESOLUTION_640x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"720x480", 720, 480, API_VEGA_BQB_RESOLUTION_720x480, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"720x540", 720, 540, API_VEGA_BQB_RESOLUTION_720x540, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"720x576", 720, 576, API_VEGA_BQB_RESOLUTION_720x576, API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_SEQUENCE},
    {"848x480", 848, 480, API_VEGA_BQB_RESOLUTION_848x480, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"864x360", 864, 360, API_VEGA_BQB_RESOLUTION_864x360, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"960x400", 960, 400, API_VEGA_BQB_RESOLUTION_960x400, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"960x540", 960, 540, API_VEGA_BQB_RESOLUTION_960x540, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"960x720", 960, 720, API_VEGA_BQB_RESOLUTION_960x720, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"1280x532", 1280, 532, API_VEGA_BQB_RESOLUTION_1280x532, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"1280x720", 1280, 720, API_VEGA_BQB_RESOLUTION_1280x720, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_16CH_720P_MIXING},
    {"1280x1024", 1280, 1024, API_VEGA_BQB_RESOLUTION_1280x1024, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"1280x1080", 1280, 1080, API_VEGA_BQB_RESOLUTION_1280x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"1440x1080", 1440, 1080, API_VEGA_BQB_RESOLUTION_1440x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"1920x800", 1920, 800, API_VEGA_BQB_RESOLUTION_1920x800, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"1920x1080", 1920, 1080, API_VEGA_BQB_RESOLUTION_1920x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"2048x1080", 2048, 1080, API_VEGA_BQB_RESOLUTION_2048x1080, API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING,
     API_VEGA_BQB_DEVICE_ENC_MODE_8CH_MIXING},
    {"2160x1620", 2160, 1620, API_VEGA_BQB_RESOLUTION_2160x1620, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    {"2400x1000", 2400, 1000, API_VEGA_BQB_RESOLUTION_2400x1000, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    {"2560x2160", 2560, 2160, API_VEGA_BQB_RESOLUTION_2560x1440, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    {"2880x2160", 2880, 2160, API_VEGA_BQB_RESOLUTION_2880x2160, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    {"3840x1606", 3840, 1606, API_VEGA_BQB_RESOLUTION_3840x1606, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    {"3840x2160", 3840, 2160, API_VEGA_BQB_RESOLUTION_3840x2160, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    {"4096x2160", 4096, 2160, API_VEGA_BQB_RESOLUTION_4096x2160, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K},
    {"user-defined", 0, 0, API_VEGA_BQB_RESOLUTION_4096x2160, API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K,
     API_VEGA_BQB_DEVICE_ENC_MODE_1CH_4K2K}};
extern "C" const int gstRes265_count;
const int            gstRes265_count = sizeof(gstRes265) / sizeof(stResolution);

vega_bqb_hevc_encoder::vega_bqb_hevc_encoder()
{
	gst_res_table = (const stResolution *)gstRes265;
	gst_res_count = gstRes265_count;
}

int vega_bqb_hevc_encoder::configure(const vegaff_codec_param *arg_param)
{
	vega_bqb_hevc_param *lparam;
	API_VEGA_BQB_RET     bqbret;

	if (_aborted)
		return -1;
	if (arg_param)
	{
		vegaff_copyT(_param, arg_param);
	}
	lparam = (vega_bqb_hevc_param *)_param;
	_device = (API_VEGA_BQB_DEVICE_E)lparam->device;
	_channel = (API_VEGA_BQB_CHN_E)lparam->channel;
	_apiInitParam->eCodecType = API_VEGA_BQB_CODEC_TYPE_HEVC;
	_apiInitParam->eOutputFmt = API_VEGA_BQB_STREAM_OUTPUT_FORMAT_ES;
	//  _apiInitParam->eDbgLevel = (API_VEGA_BQB_DBG_LEVEL_E)p->dbgLevel;

	API_VEGA_BQB_HEVC_INIT_PARAM_T *ap = &_apiInitParam->tHevcParam;
	ap->eInputMode = (API_VEGA_BQB_INPUT_MODE_E)lparam->inputMode;
	ap->eInputPort = API_VEGA_BQB_VIF_MODE_INPUT_PORT_DEFAULT;
	ap->eProfile = (API_VEGA_BQB_HEVC_PROFILE_E)lparam->profile;
	ap->eLevel = (API_VEGA_BQB_HEVC_LEVEL_E)lparam->levelIdc;
	ap->eTier = (API_VEGA_BQB_HEVC_TIER_E)lparam->bHighTier;

	API_VEGA_BQB_ENCODE_CONFIG_T enc_cfg;
	int                          round = 16;

	memset(&enc_cfg, 0, sizeof(enc_cfg));
	if (lparam->sourceWidth <= 720)
		round = 32;
	ap->eResolution = API_VEGA_BQB_RESOLUTION_INVALID;
	for (int i = 0; i < gst_res_count; i++)
	{
		if (lparam->sourceWidth == gst_res_table[i].width && lparam->sourceHeight == gst_res_table[i].height)
		{
			if ((lparam->fpsNum / lparam->fpsDenom) <= 30)
				enc_cfg.eMode = gst_res_table[i].eMode2;
			else
				enc_cfg.eMode = gst_res_table[i].eMode1;
			ap->eResolution = gst_res_table[i].eRes;
			break;
		}
		else if (gst_res_table[i].width == 0 && gst_res_table[i].height == 0)
		{
			ap->eResolution = API_VEGA_BQB_RESOLUTION_USER_DEFINED;
			ap->u32UserDefinedSourceWidth = ROUND_UP(lparam->sourceWidth, round);
			ap->u32UserDefinedSourceHeight = ROUND_UP(lparam->sourceHeight, round);

			for (int j = 0; j < (int)gst_res_count; j++)
			{
				if (lparam->sourceWidth <= gst_res_table[j].width && lparam->sourceHeight <= gst_res_table[j].height)
				{
					if ((lparam->fpsNum / lparam->fpsDenom) <= 30)
						enc_cfg.eMode = gst_res_table[j].eMode2;
					else
						enc_cfg.eMode = gst_res_table[j].eMode1;
					break;
				}
			}
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "WxH:%ux%u\n", ap->u32UserDefinedSourceWidth,
					   ap->u32UserDefinedSourceHeight);
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "Encode mode:%d\n", enc_cfg.eMode);
		}
	}
	if (ap->eResolution == API_VEGA_BQB_RESOLUTION_INVALID)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Unsupported resolution.\n");
		_aborted = true;
		return -2;
	}

	if (lparam->encodeMode != 0) // it is not auto mode
	{
		enc_cfg.eMode = (API_VEGA_BQB_DEVICE_ENC_MODE_E)(lparam->encodeMode - 1);
	}

	bqbret = VEGA_BQB_ENC_ConfigDeviceMode(_device, &enc_cfg);
	if ((int)bqbret != 0)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Failed to change mode (%d)\n", (int)bqbret);
		_aborted = true;
		return -3;
	}

	switch (lparam->internalCsp)
	{
	case VEGA_BQB_CSP_I420:
		switch (lparam->internalBitDepth)
		{
		case 8:
			if (lparam->inputMode) // vif mode, M30 only supports NV12, it cannot support YUV420
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV12;
			else
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I420;
			break;
		case 10:
			ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I0AL;
			break;
		}
		ap->eChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_420;
		break;
	case VEGA_BQB_CSP_NV12:
		ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV12;
		ap->eChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_420;
		break;

	case VEGA_BQB_CSP_I422:
		switch (lparam->internalBitDepth)
		{
		case 8:
			if (lparam->inputMode) // vif mode, M30 only supports NV16, it cannot support YUV420
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV16;
			else
				ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I420;
			break;
		case 10:
			ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_I2AL;
			break;
		}
		ap->eChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_422;
		break;
	case VEGA_BQB_CSP_NV16:
		ap->eFormat = API_VEGA_BQB_IMAGE_FORMAT_NV16;
		ap->eChromaFmt = API_VEGA_BQB_CHROMA_FORMAT_422;
		break;
	}

	if (lparam->inputMode)
	{
		if (!lparam->interlaceMode)
		{
			ap->tChromaConvertInfo.s11C420Coef[0] = -3;
			ap->tChromaConvertInfo.s11C420Coef[1] = -19;
			ap->tChromaConvertInfo.s11C420Coef[2] = 34;
			ap->tChromaConvertInfo.s11C420Coef[3] = 500;
			ap->tChromaConvertInfo.s11C420Coef[4] = 500;
			ap->tChromaConvertInfo.s11C420Coef[5] = 34;
			ap->tChromaConvertInfo.s11C420Coef[6] = -19;
			ap->tChromaConvertInfo.s11C420Coef[7] = -3;
		}
		else
		{
			ap->tChromaConvertInfo.s11C420Coef[0] = -8;
			ap->tChromaConvertInfo.s11C420Coef[1] = -26;
			ap->tChromaConvertInfo.s11C420Coef[2] = 115;
			ap->tChromaConvertInfo.s11C420Coef[3] = 586;
			ap->tChromaConvertInfo.s11C420Coef[4] = 409;
			ap->tChromaConvertInfo.s11C420Coef[5] = -48;
			ap->tChromaConvertInfo.s11C420Coef[6] = -4;
			ap->tChromaConvertInfo.s11C420Coef[7] = 0;
		}
	}

	if (lparam->vui.bEnableOverscanInfoPresentFlag && !lparam->vui.bEnableOverscanAppropriateFlag)
		ap->eOverScan = API_VEGA_BQB_OVERSCAN_INFO_INAPPROPRIATE;
	else if (lparam->vui.bEnableOverscanInfoPresentFlag && lparam->vui.bEnableOverscanAppropriateFlag)
		ap->eOverScan = API_VEGA_BQB_OVERSCAN_INFO_APPROPRIATE;
	else
		ap->eOverScan = API_VEGA_BQB_OVERSCAN_INFO_NOT_PRESENT;

	ap->tVideoSignalType.bPresentFlag = lparam->vui.bEnableVideoSignalTypePresentFlag != 0;
	ap->tVideoSignalType.eVideoFormat = (API_VEGA_BQB_VIDEO_FORMAT_E)lparam->vui.videoFormat;
	ap->tVideoSignalType.bVideoFullRange = lparam->vui.bEnableVideoFullRangeFlag != 0;
	ap->tVideoSignalType.tColorDesc.bPresentFlag = lparam->vui.bEnableColorDescriptionPresentFlag != 0;
	ap->tVideoSignalType.tColorDesc.eColorPrimaries = (API_VEGA_BQB_COLOR_PRIMARY_E)lparam->vui.colorPrimaries;
	ap->tVideoSignalType.tColorDesc.eTransferCharacteristics =
		(API_VEGA_BQB_TRANSFER_CHAR_E)lparam->vui.transferCharacteristics;
	ap->tVideoSignalType.tColorDesc.eMatrixCoeff = (API_VEGA_BQB_MATRIX_COEFFS_E)lparam->vui.matrixCoeffs;

	ap->tChromaLocation.bChromaLoc = lparam->vui.bEnableChromaLocInfoPresentFlag != 0;
	ap->tChromaLocation.eTopFieldLoc = (API_VEGA_BQB_CHROMA_SAMPLE_POSITION_E)lparam->vui.chromaSampleLocTypeTopField;
	ap->tChromaLocation.eBotFieldLoc =
		(API_VEGA_BQB_CHROMA_SAMPLE_POSITION_E)lparam->vui.chromaSampleLocTypeBottomField;

	if (lparam->maxCLL || lparam->maxFALL)
	{
		SEIContentLightLevel cllsei;
		cllsei.max_content_light_level = lparam->maxCLL;
		cllsei.max_pic_average_light_level = lparam->maxFALL;
		cllsei.write(this);
	}

	if (lparam->masteringDisplayColorVolume)
	{
		SEIMasteringDisplayColorVolume mdsei;
		if (mdsei.parse(lparam->masteringDisplayColorVolume))
			mdsei.write(this);
	}

	if (lparam->internalBitDepth == 8)
		ap->eBitDepth = API_VEGA_BQB_BIT_DEPTH_8;
	else if (lparam->internalBitDepth == 10)
		ap->eBitDepth = API_VEGA_BQB_BIT_DEPTH_10;

	ap->bInterlace = (lparam->interlaceMode == 1) ? true : false;
	ap->bDisableVpsTimingInfoPresent = true;

	if (lparam->vui.aspectRatioIdc)
	{
		ap->eAspectRatioIdc = (API_VEGA_BQB_HEVC_ASPECT_RATIO_IDC_E)lparam->vui.aspectRatioIdc;
		ap->u32SarWidth = (uint32_t)lparam->vui.sarWidth;
		ap->u32SarHeight = (uint32_t)lparam->vui.sarHeight;
	}
	else
	{
		if (lparam->sourceWidth == 720)
		{
			if (lparam->sourceHeight == 576)
			{
				ap->eAspectRatioIdc = API_VEGA_BQB_HEVC_ASPECT_RATIO_IDC_2;
			}
			else if (lparam->sourceHeight == 480)
			{
				ap->eAspectRatioIdc = API_VEGA_BQB_HEVC_ASPECT_RATIO_IDC_3;
			}
			else
			{
				ap->eAspectRatioIdc = API_VEGA_BQB_HEVC_ASPECT_RATIO_IDC_1;
			}
		}
		else if (lparam->sourceWidth == 416 || lparam->sourceWidth == 352)
		{
			ap->eAspectRatioIdc = API_VEGA_BQB_HEVC_ASPECT_RATIO_IDC_0;
		}
		else
		{
			ap->eAspectRatioIdc = API_VEGA_BQB_HEVC_ASPECT_RATIO_IDC_1;
		}
	}

	ap->tCrop.u32CropLeft = lparam->vui.defDispWinLeftOffset;
	ap->tCrop.u32CropRight = lparam->vui.defDispWinRightOffset;
	ap->tCrop.u32CropTop = lparam->vui.defDispWinTopOffset;
	ap->tCrop.u32CropBottom = lparam->vui.defDispWinBottomOffset;

	if (!lparam->vui.defDispWinLeftOffset && !lparam->vui.defDispWinRightOffset && !lparam->vui.defDispWinTopOffset &&
		!lparam->vui.defDispWinBottomOffset && ((lparam->sourceWidth == 1920) || (lparam->sourceWidth == 2048)) &&
		(lparam->sourceHeight == 1080))
	{
		ap->tCrop.u32CropLeft = 0;
		ap->tCrop.u32CropRight = 0;
		ap->tCrop.u32CropTop = 0;
		ap->tCrop.u32CropBottom = 8;
	}
	if (ap->eResolution == API_VEGA_BQB_RESOLUTION_USER_DEFINED)
	{
		ap->tCrop.u32CropLeft = 0;
		ap->tCrop.u32CropRight = ROUND_UP(lparam->sourceWidth, round) - lparam->sourceWidth;
		ap->tCrop.u32CropTop = 0;
		ap->tCrop.u32CropBottom = ROUND_UP(lparam->sourceHeight, round) - lparam->sourceHeight;
	}

	switch (lparam->bOpenGOP)
	{
	case 0:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_ALL;
		break;

	case 1:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_FIRST;
		break;

	case 2:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_TWO;
		break;

	case 3:
		ap->eIDRFrameNum = API_VEGA_BQB_IDR_FRAME_100;
		break;
	}

	if (lparam->inputMode == API_VEGA_BQB_INPUT_MODE_DATA)
		ap->ePtsMode = API_VEGA_BQB_PTS_MODE_USER;
	else if (lparam->inputMode == API_VEGA_BQB_INPUT_MODE_VIF_SQUARE ||
			 lparam->inputMode == API_VEGA_BQB_INPUT_MODE_VIF_2_SAMPLE_INTERLEAVE)
		ap->ePtsMode = API_VEGA_BQB_PTS_MODE_VANC;

	uint32_t fps = lparam->fpsNum / lparam->fpsDenom;

	if (fps != 15 && fps != 23 && fps != 24 && fps != 25 && fps != 29 && fps != 30 && fps != 50 && fps != 59 &&
		fps != 60)
	{
		ap->eTargetFrameRate = API_VEGA_BQB_FPS_CUSTOMED;
		ap->tCustomedFrameRateInfo.u32TimeScale = lparam->fpsNum;
		ap->tCustomedFrameRateInfo.u32NumUnitsInTicks = lparam->fpsDenom;
	}
	else
		ap->eTargetFrameRate = (API_VEGA_BQB_FPS_E)fps;

	ap->eGopType = (API_VEGA_BQB_GOP_TYPE_E)lparam->gopType;
	ap->eGopSize = (API_VEGA_BQB_GOP_SIZE_E)lparam->keyframeMax;

	// Activate LDB if P+B number is not 1,2,4,8
	int p_b = lparam->bframes;
	if (ap->eGopType == API_VEGA_BQB_GOP_IPB)
	{
		p_b++;
	}
	if (p_b != 1 && p_b != 2 && p_b != 4 && p_b != 8)
	{
		ap->eGopHierarchy = API_VEGA_BQB_GOP_HIERARCHY_NONE;
	}
	ap->eBFrameNum = (API_VEGA_BQB_B_FRAME_NUM_E)lparam->bframes;

	ap->bDisableSceneChangeDetect = (lparam->scenecutThreshold == 1) ? false : true;
	if (lparam->rc.bStrictCbr)
		lparam->rc.rateControlMode = VEGA_BQB_RC_CAPPED_VBR;
	ap->eRateCtrlAlgo = (API_VEGA_BQB_RATE_CTRL_ALGO_E)lparam->rc.rateControlMode;
	ap->u32FillerTriggerLevel = (uint32_t)lparam->rc.fillerrate;
	ap->u32Bitrate = (uint32_t)lparam->rc.bitrate;
	ap->u32MaxVBR = (uint32_t)lparam->rc.vbrMaxBitrate;
	ap->u32AveVBR = (uint32_t)lparam->rc.vbrAveBitrate;
	ap->u32MinVBR = (uint32_t)lparam->rc.vbrMinBitrate;
	ap->u32CpbDelay = (uint32_t)(lparam->cpbDelay * 90000);
	ap->tCoding.bEnableAdaptiveQuantization = false;
	//#if (API_CHIP_TYPE == API_CHIP_M30)
	if (lparam->chip_type == 0) // CHIP_M30 //
	{
		ap->tCoding.u32Qmin = lparam->coding[0];
		ap->tCoding.u32Qmax = lparam->coding[1];
	}
	//#endif
	ap->tCoding.u32CtuSize = lparam->maxCUSize;
	ap->tCoding.u32MinCuSize = lparam->minCUSize;
	ap->tCoding.u32MaxTuSize = lparam->maxTUSize;
	ap->tCoding.u32MinTuSize = lparam->minTUSize;
	ap->tCoding.u32MaxTuDepthInter = lparam->tuQTMaxInterDepth;
	ap->tCoding.u32MaxTuDepthIntra = lparam->tuQTMaxIntraDepth;
	ap->tCoding.bDisableDeblocking = (lparam->bEnableLoopFilter == 1) ? false : true;
	ap->tCoding.bDisableAMP = (lparam->bEnableAMP == 1) ? false : true;
	//#if (API_CHIP_TYPE == API_CHIP_M30)
	if (lparam->chip_type == 0) // CHIP_M30 //
	{
		ap->tCoding.bDisableSAO = (lparam->bEnableSAO == 1) ? false : true;
	}
	//#endif
	ap->tCoding.eWeightedPrediction =
		(API_VEGA_BQB_WEIGHTED_PREDICTION_E)(lparam->bEnableWeightedBiPred && lparam->bEnableWeightedPred);
	ap->bEnableUltraLowLatency = (lparam->ultraLowLatency == 1) ? true : false;

	ap->tCoding.eScalingListMode =
		(lparam->scalingListMode == 1) ? API_VEGA_BQB_SCALING_LIST_MODE_STANDARD : API_VEGA_BQB_SCALING_LIST_MODE_SNI;

	if (ap->bEnableUltraLowLatency)
	{
		VEGA_BQB_ENC_MakeULLInitParam(
			(API_VEGA_BQB_INIT_PARAM_T *)_apiInitParam, (API_VEGA_BQB_HEVC_PROFILE_E)ap->eProfile,
			(API_VEGA_BQB_HEVC_LEVEL_E)ap->eLevel, (API_VEGA_BQB_HEVC_TIER_E)ap->eTier,
			(API_VEGA_BQB_RESOLUTION_E)ap->eResolution, (API_VEGA_BQB_CHROMA_FORMAT_E)ap->eChromaFmt,
			(API_VEGA_BQB_BIT_DEPTH_E)ap->eBitDepth, (API_VEGA_BQB_FPS_E)ap->eTargetFrameRate, ap->u32Bitrate);
	}
	ap->eRobustMode = (API_VEGA_BQB_VIF_ROBUST_MODE_E)(lparam->robustMode);
#if (HAS_AdvancedFeature != 0)
	ap->tCoding.u32MaxFrameSize = lparam->maxFrameSize;
#endif
	return 0;
}

void vega_bqb_hevc_encoder::init()
{
	vega_bqb_hevc_param * p = (vega_bqb_hevc_param *)_param;
	API_VEGA_BQB_STATUS_E st;

	if (_aborted)
		return;

	st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_OFF)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Encoder is not off.\n");
		return;
	}

	VEGA_BQB_ENC_SetDbgMsgLevel(_device, _channel, (API_VEGA_BQB_DBG_LEVEL_E)p->dbgLevel);

	if ((p->inputHeight != p->sourceHeight || p->inputWidth != p->sourceWidth) && !p->interlaceMode)
	{
		API_VEGA_BQB_RET             api_ret;
		API_VEGA_BQB_XC_INIT_PARAM_T api_xc_init_param;
		memset(&api_xc_init_param, 0, sizeof(API_VEGA_BQB_XC_INIT_PARAM_T));

		api_xc_init_param.eInputSource = API_VEGA_BQB_XC_INPUT_SOURCE_PCIE;
		api_xc_init_param.u32SourceId = _channel;
		api_xc_init_param.eInputResolution = getResolution(p->inputWidth, p->inputHeight);
		api_xc_init_param.eOutputResolution = getResolution(p->sourceWidth, p->sourceHeight);
		api_xc_init_param.eChromaFmt = (API_VEGA_BQB_CHROMA_FORMAT_E)_apiInitParam->tHevcParam.eChromaFmt;
		api_xc_init_param.eBitDepth = (API_VEGA_BQB_BIT_DEPTH_E)_apiInitParam->tHevcParam.eBitDepth;

		api_ret = VEGA_BQB_XC_Init((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel, &api_xc_init_param);
		if (api_ret != API_VEGA_BQB_RET_SUCCESS)
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "(%d:%d) unable to init VEGA_BQB_HEVC scaler \n", _device, _channel);
			_aborted = true;
			return;
		}
		else
		{
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "Init XC %dx%d -> %dx%d , chroma:%d BitDepth:%d\n", p->inputWidth,
					   p->inputHeight, p->sourceWidth, p->sourceHeight, _apiInitParam->tHevcParam.eChromaFmt,
					   _apiInitParam->tHevcParam.eBitDepth);
		}
	}

	API_VEGA_BQB_INIT_PARAM_T t_init_param;
	memset(&t_init_param, 0, sizeof(API_VEGA_BQB_INIT_PARAM_T));
	t_init_param.eCodecType = (API_VEGA_BQB_CODEC_TYPE_E)_apiInitParam->eCodecType;
	t_init_param.eOutputFmt = (API_VEGA_BQB_STREAM_OUTPUT_FORMAT_E)_apiInitParam->eOutputFmt;

	API_VEGA_BQB_HEVC_INIT_PARAM_T *p_hevc_param = &t_init_param.tHevcParam;
	p_hevc_param->eInputMode = (API_VEGA_BQB_INPUT_MODE_E)_apiInitParam->tHevcParam.eInputMode;
	p_hevc_param->eInputPort = (API_VEGA_BQB_VIF_MODE_INPUT_PORT_E)_apiInitParam->tHevcParam.eInputPort;
	p_hevc_param->eRobustMode = (API_VEGA_BQB_VIF_ROBUST_MODE_E)_apiInitParam->tHevcParam.eRobustMode;
	p_hevc_param->eProfile = (API_VEGA_BQB_HEVC_PROFILE_E)_apiInitParam->tHevcParam.eProfile;
	p_hevc_param->eLevel = (API_VEGA_BQB_HEVC_LEVEL_E)_apiInitParam->tHevcParam.eLevel;
	p_hevc_param->eTier = (API_VEGA_BQB_HEVC_TIER_E)_apiInitParam->tHevcParam.eTier;
	p_hevc_param->eResolution = (API_VEGA_BQB_RESOLUTION_E)_apiInitParam->tHevcParam.eResolution;
	if (p_hevc_param->eResolution == API_VEGA_BQB_RESOLUTION_USER_DEFINED)
	{
		p_hevc_param->u32UserDefinedSourceWidth = _apiInitParam->tHevcParam.u32UserDefinedSourceWidth;
		p_hevc_param->u32UserDefinedSourceHeight = _apiInitParam->tHevcParam.u32UserDefinedSourceHeight;
	}

	p_hevc_param->eAspectRatioIdc = (API_VEGA_BQB_HEVC_ASPECT_RATIO_IDC_E)_apiInitParam->tHevcParam.eAspectRatioIdc;
	p_hevc_param->u32SarWidth = _apiInitParam->tHevcParam.u32SarWidth;
	p_hevc_param->u32SarHeight = _apiInitParam->tHevcParam.u32SarHeight;
	p_hevc_param->bDisableVpsTimingInfoPresent = _apiInitParam->tHevcParam.bDisableVpsTimingInfoPresent;
	p_hevc_param->eFormat = (API_VEGA_BQB_IMAGE_FORMAT_E)_apiInitParam->tHevcParam.eFormat;
	p_hevc_param->eChromaFmt = (API_VEGA_BQB_CHROMA_FORMAT_E)_apiInitParam->tHevcParam.eChromaFmt;
	memcpy(&p_hevc_param->tChromaConvertInfo, &_apiInitParam->tHevcParam.tChromaConvertInfo,
		   sizeof(API_VEGA_BQB_CHROMA_CONVERT_INFO_T));
	p_hevc_param->eOverScan = (API_VEGA_BQB_OVERSCAN_INFO_E)_apiInitParam->tHevcParam.eOverScan;
	memcpy(&p_hevc_param->tVideoSignalType, &_apiInitParam->tHevcParam.tVideoSignalType,
		   sizeof(API_VEGA_BQB_VIDEO_SIGNAL_TYPE_T));
	memcpy(&p_hevc_param->tChromaLocation, &_apiInitParam->tHevcParam.tChromaLocation,
		   sizeof(API_VEGA_BQB_CHROMA_LOC_INFO_T));
	p_hevc_param->eBitDepth = (API_VEGA_BQB_BIT_DEPTH_E)_apiInitParam->tHevcParam.eBitDepth;
	p_hevc_param->bInterlace = _apiInitParam->tHevcParam.bInterlace;
	p_hevc_param->bDisableSceneChangeDetect = _apiInitParam->tHevcParam.bDisableSceneChangeDetect;
	memcpy(&p_hevc_param->tCrop, &_apiInitParam->tHevcParam.tCrop, sizeof(API_VEGA_BQB_CROP_INFO_T));
	p_hevc_param->eTargetFrameRate = (API_VEGA_BQB_FPS_E)_apiInitParam->tHevcParam.eTargetFrameRate;
	memcpy(&p_hevc_param->tCustomedFrameRateInfo, &_apiInitParam->tHevcParam.tCustomedFrameRateInfo,
		   sizeof(API_VEGA_BQB_FPS_CUSTOMED_T));
	p_hevc_param->ePtsMode = (API_VEGA_BQB_PTS_MODE_E)_apiInitParam->tHevcParam.ePtsMode;
	p_hevc_param->eIDRFrameNum = (API_VEGA_BQB_IDR_FRAME_NUM_E)_apiInitParam->tHevcParam.eIDRFrameNum;
	p_hevc_param->eIDRType = (API_VEGA_BQB_HEVC_IDR_TYPE_E)_apiInitParam->tHevcParam.eIDRType;
	p_hevc_param->eGopType = (API_VEGA_BQB_GOP_TYPE_E)_apiInitParam->tHevcParam.eGopType;
	p_hevc_param->eGopHierarchy = (API_VEGA_BQB_GOP_HIERARCHY_E)_apiInitParam->tHevcParam.eGopHierarchy;
	p_hevc_param->eGopSize = (API_VEGA_BQB_GOP_SIZE_E)_apiInitParam->tHevcParam.eGopSize;
	p_hevc_param->eBFrameNum = (API_VEGA_BQB_B_FRAME_NUM_E)_apiInitParam->tHevcParam.eBFrameNum;
#if (HAS_AdvancedFeature != 0)
	p_hevc_param->eRefPic = (API_VEGA_BQB_REF_PIC_NUM_E)_apiInitParam->tHevcParam.eRefPic;
#endif
	p_hevc_param->bDisableTemporalId = _apiInitParam->tHevcParam.bDisableTemporalId;
	p_hevc_param->eRateCtrlAlgo = (API_VEGA_BQB_RATE_CTRL_ALGO_E)_apiInitParam->tHevcParam.eRateCtrlAlgo;
	p_hevc_param->u32FillerTriggerLevel = _apiInitParam->tHevcParam.u32FillerTriggerLevel;
	p_hevc_param->u32Bitrate = _apiInitParam->tHevcParam.u32Bitrate;
	p_hevc_param->u32MaxVBR = _apiInitParam->tHevcParam.u32MaxVBR;
	p_hevc_param->u32AveVBR = _apiInitParam->tHevcParam.u32AveVBR;
	p_hevc_param->u32MinVBR = _apiInitParam->tHevcParam.u32MinVBR;
	p_hevc_param->u32CpbDelay = _apiInitParam->tHevcParam.u32CpbDelay;
	p_hevc_param->tCoding.bEnableAdaptiveQuantization = _apiInitParam->tHevcParam.tCoding.bEnableAdaptiveQuantization;
	p_hevc_param->tCoding.u32Qmin = _apiInitParam->tHevcParam.tCoding.u32Qmin;
	p_hevc_param->tCoding.u32Qmax = _apiInitParam->tHevcParam.tCoding.u32Qmax;
	p_hevc_param->tCoding.bDisableMinQpCtrl = _apiInitParam->tHevcParam.tCoding.bDisableMinQpCtrl;
	p_hevc_param->tCoding.eScalingListMode =
		(API_VEGA_BQB_SCALING_LIST_MODE_E)_apiInitParam->tHevcParam.tCoding.eScalingListMode;
	p_hevc_param->tCoding.u32CtuSize = _apiInitParam->tHevcParam.tCoding.u32CtuSize;
	p_hevc_param->tCoding.u32MinCuSize = _apiInitParam->tHevcParam.tCoding.u32MinCuSize;
	p_hevc_param->tCoding.u32MaxTuSize = _apiInitParam->tHevcParam.tCoding.u32MaxTuSize;
	p_hevc_param->tCoding.u32MinTuSize = _apiInitParam->tHevcParam.tCoding.u32MinTuSize;
	p_hevc_param->tCoding.u32MaxTuDepthIntra = _apiInitParam->tHevcParam.tCoding.u32MaxTuDepthIntra;
	p_hevc_param->tCoding.u32MaxTuDepthInter = _apiInitParam->tHevcParam.tCoding.u32MaxTuDepthInter;
	p_hevc_param->tCoding.bDisableDeblocking = _apiInitParam->tHevcParam.tCoding.bDisableDeblocking;
	p_hevc_param->tCoding.bDisableAMP = _apiInitParam->tHevcParam.tCoding.bDisableAMP;
	p_hevc_param->tCoding.eWeightedPrediction =
		(API_VEGA_BQB_WEIGHTED_PREDICTION_E)_apiInitParam->tHevcParam.tCoding.eWeightedPrediction;
	p_hevc_param->tCoding.bDisableSAO = _apiInitParam->tHevcParam.tCoding.bDisableSAO;
	p_hevc_param->tCoding.bWPP = _apiInitParam->tHevcParam.tCoding.bWPP;
	p_hevc_param->tCoding.u32NumSliceSegInSlice = _apiInitParam->tHevcParam.tCoding.u32NumSliceSegInSlice;
#if (HAS_AdvancedFeature != 0)
	p_hevc_param->tCoding.u32MaxFrameSize = _apiInitParam->tHevcParam.tCoding.u32MaxFrameSize;
#endif
	memcpy(&p_hevc_param->tHdrConfig, &_apiInitParam->tHevcParam.tHdrConfig, sizeof(API_VEGA_BQB_HDR_PARAM_T));
	p_hevc_param->bEnableUltraLowLatency = _apiInitParam->tHevcParam.bEnableUltraLowLatency;

	if (VEGA_BQB_ENC_Init((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel, &t_init_param))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to init VEGA_BQB_HEVC encoder\n", _device,
				   _channel);
		_aborted = true;
		return;
	}
	VEGA_BQB_ENC_SetTimeStampUnit(_device, _channel, API_VEGA_BQB_TIMEBASE_90KHZ);
}

void vega_bqb_hevc_encoder::registEsPopCallback(void *pfunc_callback)
{
	if (_aborted)
		return;

	API_VEGA_BQB_STATUS_E st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_STANDBY)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Encoder isn't in standby mode\n");
		return;
	}

	if (VEGA_BQB_ENC_RegisterCallback((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
									  (API_VEGA_BQB_CALLBACK)pfunc_callback, (void *)this))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to register pop-es callback function\n",
				   _device, _channel);
		_aborted = true;
		return;
	}
}

void vega_bqb_hevc_encoder::registPictureInfoCallback(void *pfunc_callback)
{
	if (_aborted)
		return;

	vega_bqb_hevc_param *p = (vega_bqb_hevc_param *)_param;

	if (!p->inputMode)
		return;

	if (_apiInitParam->tHevcParam.tHdrConfig.bEnable == false)
		return;

	API_VEGA_BQB_STATUS_E st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_STANDBY)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Encoder isn't in standby mode\n");
		return;
	}

	if (VEGA_BQB_ENC_RegisterPictureInfoCallback((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
												 (API_VEGA_BQB_PICT_INFO_CALLBACK)pfunc_callback,
												 &(_apiInitParam->tHevcParam.tHdrConfig)))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "board:%d channel:%d unable to register picture info callback function\n",
				   _device, _channel);
		_aborted = true;
		return;
	}
}
void vega_bqb_hevc_encoder::deregistPictureInfoCallback()
{
	if (_aborted)
		return;

	vega_bqb_hevc_param *p = (vega_bqb_hevc_param *)_param;

	if (!p->inputMode)
		return;

	if (_apiInitParam->tHevcParam.tHdrConfig.bEnable == false)
		return;

	API_VEGA_BQB_STATUS_E st =
		(API_VEGA_BQB_STATUS_E)VEGA_BQB_ENC_GetStatus((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel);

	if (st != API_VEGA_BQB_STATUS_ENCODING)
		return;

	if (VEGA_BQB_ENC_RegisterPictureInfoCallback((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel, NULL,
												 NULL))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
				   "board:%d channel:%d unable to deregister picture info callback function\n", _device, _channel);
		_aborted = true;
		return;
	}
}

int vega_bqb_hevc_encoder::encode(const vegaff_picture_t *pic_in)
{
	if (_aborted)
		return -1;

	vega_bqb_hevc_param *p = (vega_bqb_hevc_param *)_param;
	API_VEGA_BQB_IMG_T   img, last_img;

	if (p->inputMode)
	{
		if (!pic_in)
		{
			deregistVideoCaptureStartCallback();
			stop();
			TRACE();
		}
		return 0;
	}

	if (pic_in && !_pendingFrame)
	{
		_picInSize = _inFrame->getPictureSize(pic_in);
		_picInPts = pic_in->pts;
		_picInFmt = (API_VEGA_BQB_IMAGE_FORMAT_E)pic_in->imgFormat;
		_inFrame->copyFromPicture(this, pic_in);
		_inFrame->contextReinit(pic_in);
		_pendingFrame = true;
	}
	else if (pic_in && _pendingFrame)
	{
		if (_inFrame->_sizeChanged)
		{
			uint8_t *buf = (uint8_t *)vegaff_malloc(_picInSize);
			if (!buf)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device:%d channel:%d image backup allocation for context change failure, aborting\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}

			memcpy(buf, _vrawBuf, _picInSize);
			memset(_vrawBuf, 0, MAX_VRAW_BUF_SIZE);

			VEGA_BQB_ENC_Rescale(_vrawBuf, buf, _inFrame->_width0, _inFrame->_height0, _inFrame->_width1,
								 _inFrame->_height1);

			vegaff_free(buf);
		}

		img.pu8Addr = (uint8_t *)_vrawBuf;
		img.u32Size = _inFrame->_maxPictureSize;
		img.pts = (uint64_t)getPicInPts();
		img.eTimeBase = API_VEGA_BQB_TIMEBASE_90KHZ;
		img.bLastFrame = false;
		if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I0AL)
		{
			img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP01;
		}
		else if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I2AL)
		{
			img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP21;
		}
		else
			img.eFormat = _picInFmt;

		img.u32SeiNum = 0;
		img.bSeiPassThrough = false;

		if (_inFrame->_user_data_registered_itu_t_t35)
		{
			img.u32SeiNum = 1;
			img.bSeiPassThrough = false;

			if (writeUserDataRegisteredSEI(img.tSeiParam) < 0)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to write UserDataRegisteredSEI\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}
		}

		if (p->bEmitInfoSEI)
		{
			img.u32SeiNum = 2;
			img.bSeiPassThrough = false;

			if (writeUserDataUnregisteredSEI(img.tSeiParam) < 1)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to write UserDataUnregisteredSEI\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}
		}

		if (VEGA_BQB_ENC_PushImage(_device, _channel, &img))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
			_aborted = true;
			return -1;
		}

		_picInSize = _inFrame->getPictureSize(pic_in);
		_picInPts = pic_in->pts;
		_picInFmt = (API_VEGA_BQB_IMAGE_FORMAT_E)pic_in->imgFormat;
		_inFrame->copyFromPicture(this, pic_in);

		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega input frame=%d\n", _device, _channel,
				   _inFrameCnt++);
		_inFrame->contextReinit(pic_in);
		_pendingFrame = true;
	}
	else if (!pic_in && _pendingFrame)
	{
		if (_inFrame->_sizeChanged)
		{
			uint8_t *buf = (uint8_t *)vegaff_malloc(_picInSize);
			if (!buf)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device:%d channel:%d image backup allocation for context change failure, aborting\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}

			memcpy(buf, _vrawBuf, _picInSize);
			memset(_vrawBuf, 0, MAX_VRAW_BUF_SIZE);

			VEGA_BQB_ENC_Rescale(_vrawBuf, buf, _inFrame->_width0, _inFrame->_height0, _inFrame->_width1,
								 _inFrame->_height1);

			vegaff_free(buf);
		}

		last_img.pu8Addr = (uint8_t *)_vrawBuf;
		last_img.u32Size = _inFrame->_maxPictureSize;
		last_img.pts = (uint64_t)getPicInPts();
		last_img.eTimeBase = API_VEGA_BQB_TIMEBASE_90KHZ;
		last_img.bLastFrame = true;

		if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I0AL)
		{
			last_img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP01;
		}
		else if (_picInFmt == API_VEGA_BQB_IMAGE_FORMAT_I2AL)
		{
			last_img.eFormat = API_VEGA_BQB_IMAGE_FORMAT_PP21;
		}
		else
			last_img.eFormat = _picInFmt;

		last_img.u32SeiNum = 0;
		last_img.bSeiPassThrough = false;

		if (_inFrame->_user_data_registered_itu_t_t35)
		{
			last_img.u32SeiNum = 1;
			last_img.bSeiPassThrough = false;

			if (writeUserDataRegisteredSEI(last_img.tSeiParam) < 0)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to write UserDataRegisteredSEI\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}
		}

		if (p->bEmitInfoSEI)
		{
			last_img.u32SeiNum = 2;
			last_img.bSeiPassThrough = false;

			if (writeUserDataUnregisteredSEI(last_img.tSeiParam) < 0)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to write UserDataUnregisteredSEI\n",
						   _device, _channel);
				_aborted = true;
				return -1;
			}
		}

		if (p->interlaceMode && (_inFrameCnt % 2 == 0))
		{
			last_img.bLastFrame = false;
			if (VEGA_BQB_ENC_PushImage((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
									   (API_VEGA_BQB_IMG_T *)&last_img))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
				_aborted = true;
				return -1;
			}
			vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega input frame=%d\n", _device, _channel,
					   _inFrameCnt++);
			_picInPts += 1;
			last_img.pts = (uint64_t)getPicInPts();
			last_img.bLastFrame = true;
			if (VEGA_BQB_ENC_PushImage((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
									   (API_VEGA_BQB_IMG_T *)&last_img))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
				_aborted = true;
				return -1;
			}
			vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega last_frame, input frame=%d\n", _device,
					   _channel, _inFrameCnt++);
			_pendingFrame = false;
			return 0;
		}

		if (VEGA_BQB_ENC_PushImage((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
								   (API_VEGA_BQB_IMG_T *)&last_img))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to encode\n", _device, _channel);
			_aborted = true;
			return -1;
		}

		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device:%d channel:%d to vega last_frame, input frame=%d\n", _device,
				   _channel, _inFrameCnt++);

		_pendingFrame = false;
	}
	else if (!pic_in && !_pendingFrame && !_inFrameCnt && !_outFrameCnt)
	{
		_aborted = true;
		return -1;
	}

	return 0;
}

void vega_bqb_hevc_encoder::flush()
{
	while (!_esQueue.empty())
	{
		Queue<vegaff_nal_t> tempQueue = _esQueue.pop();

		while (!tempQueue.empty())
		{
			tempQueue.pop();
		}
	}
}

int64_t vega_bqb_hevc_encoder::getPicOutDts(int64_t dts_90khz)
{
	int64_t dts;

	dts = ts_extent_incrstep(&dts_64b, dts_90khz);
	return dts;
}
