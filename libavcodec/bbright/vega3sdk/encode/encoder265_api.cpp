/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "param265.h"
#include "encoder265.h"
#include "encodeapi_tpl.h"

static void vega_bqb_hevc_process_coded_frame(API_VEGA_BQB_HEVC_CODED_PICT_T *p_pict, void *args)
{
	vega_bqb_hevc_encoder *encoder = static_cast<vega_bqb_hevc_encoder *>(args);
	int                    occupancy = 0;
	static bool            fBufferOFPrint = true;

	TRACE();
	vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE,
			   "bd=%d ch=%d pts=%ld dts=%ld itc=%ld itc_ext=%u tid=%d tb=%d cpb=%u type=%s last=%d\n", encoder->_device,
			   encoder->_channel, p_pict->pts, p_pict->dts, p_pict->u64ItcBase, p_pict->u32ItcExt, p_pict->u32Tid,
			   p_pict->eTimeBase, p_pict->u32CpbBoc, vega_bqb_hevc_slice_types[p_pict->eFrameType],
			   (p_pict->bLastES) ? 1 : 0);

	Queue<vegaff_nal_t> tempQueue;

	for (uint32_t i = 0; i < p_pict->u32NalNum; i++)
		occupancy += p_pict->tNalInfo[i].u32Length;

	if (((ES_BUF_SIZE / 5 > encoder->_esBufSize) || ((uint32_t)occupancy > encoder->_esBufSize)) && fBufferOFPrint)
	{
		char       local_time[256];
		time_t     timep;
		struct tm *ptime;
		time(&timep);
		ptime = localtime(&timep);
		sprintf(local_time, "%d-%d-%d %d:%d:%d", (1900 + ptime->tm_year), (1 + ptime->tm_mon), ptime->tm_mday,
				ptime->tm_hour, ptime->tm_min, ptime->tm_sec);
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "[%s]Encoder Buffer(%d) is full, this ES(%d) will be dropped\n",
				   local_time, encoder->_esBufSize, occupancy);
		fBufferOFPrint = false;

		return;
	}

	fBufferOFPrint = true;
	if ((encoder->_esBufEnd - encoder->_esBufPtr) < occupancy)
		encoder->_esBufPtr = encoder->_esBuf;

	for (uint32_t i = 0; i < p_pict->u32NalNum; i++)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "nal%u len=%u type=%u\n", i, p_pict->tNalInfo[i].u32Length,
				   p_pict->tNalInfo[i].eNalType);
		vegaff_nal_t tempNal;
		memcpy(encoder->_esBufPtr, p_pict->tNalInfo[i].pu8Addr, p_pict->tNalInfo[i].u32Length);
		tempNal.pu8Addr = encoder->_esBufPtr;
		tempNal.u32Length = p_pict->tNalInfo[i].u32Length;
		tempNal.type = p_pict->tNalInfo[i].eNalType;
		tempNal.pts = ((i == 0) ? encoder->getPicOutPts((int64_t)p_pict->pts) : 0);
		tempNal.dts = ((i == 0) ? encoder->getPicOutDts(p_pict->dts) : 0);
		tempNal.sliceType = (int)p_pict->eFrameType;
		tempQueue.push(tempNal);
		encoder->_esBufPtr += p_pict->tNalInfo[i].u32Length;
	}
	tempQueue.is_last_ES = (p_pict->bLastES) ? true : false;
	encoder->_esBufSize -= occupancy;
	encoder->_esQueue.push(tempQueue);
	encoder->_lastES = (p_pict->bLastES) ? true : false;
}

static void vega_bqb_hevc_signal_capture_start(const int capture_counter, void *args)
{
	vegaff_signal_capture_start(capture_counter, (vega_bqb_encoder *)args);
}

static void vega_bqb_hevc_process_capture(API_VEGA_BQB_PICT_INFO_T *                     pPicInfo,
										  const API_VEGA_BQB_PICT_INFO_CALLBACK_PARAM_T *args)
{
	API_VEGA_BQB_SEI_PARAM_T *p_sei = NULL;
	API_VEGA_BQB_HDR_PARAM_T *p_hdr = (API_VEGA_BQB_HDR_PARAM_T *)args->args;
	TRACE();
	pPicInfo->u32PictureNumber = args->u32CaptureCounter;

	p_sei = &pPicInfo->tSeiParam[pPicInfo->u32SeiNum];
	uint16_t u16MaxContentLightLevel = p_hdr->u16MaxContentLightLevel;
	uint16_t u16MaxPictureAvgLightLevel = p_hdr->u16MaxPictureAvgLightLevel;
	VEGA_BQB_ENC_MakeContentLightInfoSei(p_sei, API_VEGA_BQB_SEI_PAYLOAD_LOC_GOP, u16MaxContentLightLevel,
										 u16MaxPictureAvgLightLevel);
	pPicInfo->u32SeiNum++;

	p_sei = &pPicInfo->tSeiParam[pPicInfo->u32SeiNum];
	uint8_t u8AlternativeTransferCharacteristics = (uint8_t)p_hdr->eAlternativeTransferCharacteristics;
	VEGA_BQB_ENC_MakeSeiParam(p_sei, API_VEGA_BQB_SEI_PAYLOAD_LOC_GOP,
							  API_VEGA_BQB_SEI_PAYLOAD_TYPE_ALTERNATIVE_TRANSFER_CHARACTERISTICS,
							  sizeof(u8AlternativeTransferCharacteristics), &u8AlternativeTransferCharacteristics);
	pPicInfo->u32SeiNum++;

	p_sei = &pPicInfo->tSeiParam[pPicInfo->u32SeiNum];
	uint16_t u16DisplayPrimariesX0 = p_hdr->u16DisplayPrimariesX0;
	uint16_t u16DisplayPrimariesY0 = p_hdr->u16DisplayPrimariesY0;
	uint16_t u16DisplayPrimariesX1 = p_hdr->u16DisplayPrimariesX1;
	uint16_t u16DisplayPrimariesY1 = p_hdr->u16DisplayPrimariesY1;
	uint16_t u16DisplayPrimariesX2 = p_hdr->u16DisplayPrimariesX2;
	uint16_t u16DisplayPrimariesY2 = p_hdr->u16DisplayPrimariesY2;
	uint16_t u16WhitePointX = p_hdr->u16WhitePointX;
	uint16_t u16WhitePointY = p_hdr->u16WhitePointY;
	uint32_t u32MaxDisplayMasteringLuminance = p_hdr->u32MaxDisplayMasteringLuminance;
	uint32_t u32MinDisplayMasteringLuminance = p_hdr->u32MinDisplayMasteringLuminance;
	VEGA_BQB_ENC_MakeMasteringDisplayColourVolumeSei(
		p_sei, u16DisplayPrimariesX0, u16DisplayPrimariesY0, u16DisplayPrimariesX1, u16DisplayPrimariesY1,
		u16DisplayPrimariesX2, u16DisplayPrimariesY2, u16WhitePointX, u16WhitePointY, u32MaxDisplayMasteringLuminance,
		u32MinDisplayMasteringLuminance);
	pPicInfo->u32SeiNum++;
	TRACE();
}

static int vega_bqb_hevc_get_stream_headers(vegaffhandle_t enc)
{
	TRACE();
	if (!enc)
		return -1;

	vega_bqb_hevc_encoder *encoder = static_cast<vega_bqb_hevc_encoder *>(enc);
	vega_bqb_hevc_param *  p = (vega_bqb_hevc_param *)encoder->_param;
	encoder->getStreamHeaders();

	if (encoder->_aborted)
		return -1;

	while (encoder->_esQueue.empty())
	{
		SLEEP_MICROSECOND(100000);
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "%s: Waiting for stream headers\n", __FUNCTION__MACRO__WRAPPER__);
	}

	if (!encoder->_esQueue.empty())
	{
		Queue<vegaff_nal_t> tempQueue = encoder->_esQueue.pop();
		encoder->_nalList._numNal = (uint32_t)tempQueue.size();
		int i = 0;

		while (!tempQueue.empty())
		{
			vegaff_nal_t    tempNal = tempQueue.pop();
			HEVCNalUnitType nalUnitType = (HEVCNalUnitType)tempNal.type;

			if (nalUnitType == NAL_UNIT_VPS || nalUnitType == NAL_UNIT_SPS || nalUnitType == NAL_UNIT_PPS)
			{
				encoder->_nalList._occupancy += tempNal.u32Length;
				encoder->_nalList._nal[i++] = tempNal;
			}
		}

		if (p->interlaceMode && !encoder->_esQueue.empty())
		{
			Queue<vegaff_nal_t> tempQueue1 = encoder->_esQueue.pop();

			while (!tempQueue1.empty())
			{
				tempQueue1.pop();
			}
		}
	}
	TRACE();
	return 0;
}

vegaffhandle_t vega_bqb_hevc_encoder_open(const vega_bqb_hevc_param *arg_param)
{
	vega_bqb_hevc_encoder *encoder = NULL;

	if (!arg_param)
		return NULL;

	if ( arg_param->objsize == 0 )
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Internal variable fault\n");
		abort();
	}

	TRACE();
	if (vega_bqb_hevc_check_params(arg_param) != 0)
	{
		TRACE();
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Improper parameters.\n");
		goto func_error;
	}

	vega_bqb_hevc_print_params(arg_param);

	encoder = new vega_bqb_hevc_encoder;
	if (!encoder)
		goto func_error;
	if (!encoder->create((const vegaff_codec_param *)arg_param))
	{
		TRACE();
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Faile to create encoder.\n");
		goto func_error;
	}

	encoder->configure((const vegaff_codec_param *)arg_param);
	encoder->init();
	encoder->registEsPopCallback((void *)vega_bqb_hevc_process_coded_frame);
	encoder->registVideoCaptureStartCallback((void *)vega_bqb_hevc_signal_capture_start);
	encoder->registPictureInfoCallback((void *)vega_bqb_hevc_process_capture);
	encoder->start();
	if (encoder->_aborted)
	{
		TRACE();
		goto func_error;
	}

	return encoder;

func_error:
	if (encoder)
	{
		encoder->destroy();
		delete encoder;
	}
	TRACE();
	return NULL;
}

int vega_bqb_hevc_encoder_headers(vegaffhandle_t enc, vegaff_nal_t **pp_nal, uint32_t *pi_nal)
{
	TRACE();
	if (pp_nal && enc)
	{
		vega_bqb_hevc_encoder *encoder = static_cast<vega_bqb_hevc_encoder *>(enc);

		if (vega_bqb_hevc_get_stream_headers(enc) < 0)
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "%s: Unable to get stream headers\n", __FUNCTION__MACRO__WRAPPER__);
			return -1;
		}

		*pp_nal = &encoder->_nalList._nal[0];

		if (pi_nal)
		{
			*pi_nal = encoder->_nalList._numNal;
		}

		return encoder->_nalList._occupancy;
	}

	return -1;
}

int vega_bqb_hevc_encoder_encode(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal, vegaff_picture_t *pic_in,
								 vegaff_picture_t *pic_out)
{
	return vegaff_encoder_encode(enc, pp_nal, pi_nal, pic_in, pic_out);
}

int vega_bqb_hevc_encoder_pushimage(vegaffhandle_t enc, vegaff_picture_t *pic_in)
{
	return vegaff_encoder_pushimage(static_cast<vega_bqb_hevc_encoder *>(enc), pic_in);
}

int vega_bqb_hevc_encoder_getes(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal, vegaff_picture_t *pic_in,
								vegaff_picture_t *pic_out)
{
	return vegaff_encoder_getes(enc, pp_nal, pi_nal, pic_in, pic_out);
}

void vega_bqb_hevc_encoder_close(vegaffhandle_t enc)
{
	TRACE();
	if (enc)
	{
		vega_bqb_hevc_encoder *encoder = static_cast<vega_bqb_hevc_encoder *>(enc);
		encoder->stop();
		encoder->exit();
		encoder->destroy();
		encoder->flush();
		delete encoder;
	}
	TRACE();
}

void vega_bqb_hevc_encoder_start(vegaffhandle_t enc)
{
	vegaff_encoder_start(enc);
}

void vega_bqb_hevc_encoder_stop(vegaffhandle_t enc)
{
	vegaff_encoder_stop(enc);
}

void vega_bqb_hevc_encoder_reset(vegaffhandle_t enc)
{
	vegaff_encoder_reset(enc);
}

int vega_bqb_hevc_encoder_setbitrate(vegaffhandle_t enc, uint32_t bitrate)
{
	return vegaff_encoder_setbitrate(enc, bitrate);
}

int vega_bqb_hevc_encoder_setvbr(vegaffhandle_t enc, uint32_t max, uint32_t ave, uint32_t min)
{
	return vegaff_encoder_setvbr(enc, max, ave, min);
}

int vega_bqb_hevc_encoder_forceidr(vegaffhandle_t enc)
{
	return vegaff_encoder_forceidr(enc);
}

int vega_bqb_hevc_encoder_forceidrat(vegaffhandle_t enc, uint32_t pic_num)
{
	return vegaff_encoder_forceidrat(enc, pic_num);
}

int vega_bqb_hevc_encoder_setframerate(vegaffhandle_t enc, uint32_t fps)
{
	return vegaff_encoder_setframerate(enc, fps);
}

int vega_bqb_hevc_encoder_setframerateat(vegaffhandle_t enc, uint32_t fps, uint32_t pic_num)
{
	return vegaff_encoder_setframerateat(enc, fps, pic_num);
}
