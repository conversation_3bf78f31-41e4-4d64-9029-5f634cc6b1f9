

#ifdef __linux__
#include <unistd.h>
#include "../common/LIB_MISC_LINUX.h"
#define __STDC_FORMAT_MACROS
#elif _WIN32
#include <Windows.h>
#include "../common/LIB_MISC_WIN32.h"
#define __STDC_FORMAT_MACROS
#endif
#include <inttypes.h>
#include <cmath>
#include "encoder_base.h"
#include "../common/common.h"
#include "../common/sei.h"
#include "../common/message_option.h"
#include "../common/frame.h"

// using namespace boost::interprocess;

static inline int _confirm(bool bflag, const char *message)
{
	if (!bflag)
		return 0;

	vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "%s\n", message);
	return 1;
}

vega_bqb_encoder::vega_bqb_encoder()
{
	_aborted = false;
	_apiInitParam = NULL;
	_channel = (API_VEGA_BQB_CHN_E)0;
	_device = (API_VEGA_BQB_DEVICE_E)0;
	_esBuf = NULL;
	_esBufEnd = NULL;
	_esBufPtr = NULL;
	_inFrame = NULL;
	_inFrameCnt = 0;
	_lastES = false;
	_mqReceiver = NULL;
	_outFrameCnt = 0;
	_param = NULL;
	_pendingFrame = false;
	_picInFmt = (API_VEGA_BQB_IMAGE_FORMAT_E)0;
	_picInPts = 0;
	_picInSize = 0;
	_seiBuf = NULL;
	_seiSize = 0;
	_vrawBuf = NULL;
	gst_res_count = 0;
	gst_res_table = NULL;

	ts_extent_reset(&dts_64b, 1);
	ts_extent_reset(&pts_64b, 1);
	clear_name();
}

vega_bqb_encoder::~vega_bqb_encoder()
{
	clear_resource();
}

void vega_bqb_encoder::clear_resource()
{
	if (_mqReceiver)
	{
		delete _mqReceiver;
		_mqReceiver = NULL;
	}

	if (_inFrame)
	{
		_inFrame->destroy();
		delete _inFrame;
		_inFrame = NULL;
	}

	vegaff_free(_seiBuf);
	vegaff_free(_esBuf);
	vegaff_free(_vrawBuf);
	vegaff_free(_apiInitParam);
	vegaff_free(_param);

	_apiInitParam = NULL;
	_seiBuf = NULL;
	_esBuf = NULL;
	_inFrame = NULL;
	_inFrameCnt = 0;
	_lastES = false;
	_mqReceiver = NULL;
	_outFrameCnt = 0;
	_pendingFrame = false;
	_picInFmt = (API_VEGA_BQB_IMAGE_FORMAT_E)0;
	_picInPts = 0;
	_picInSize = 0;
	_seiSize = 0;
	_vrawBuf = NULL;
	_param = NULL;
}

void vega_bqb_encoder::clear_name()
{
	_mqName[0] = 0;
}

bool vega_bqb_encoder::create(const vegaff_codec_param *arg_param)
{
	uint32_t cpbDelay = 0;
	uint32_t bitrate = 0;
	uint32_t esBufSize = 0;

	if (!arg_param)
		goto fail;

	_param = vegaff_memdupT<vegaff_codec_param>(arg_param);
	if (!_param)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "init parameter allocation failure, aborting\n");
		abort();
	}
	_apiInitParam = (API_VEGA_BQB_INIT_PARAM_T *)vegaff_malloc(sizeof(API_VEGA_BQB_INIT_PARAM_T));
	if (!_apiInitParam)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "init parameter allocation failure, aborting\n");
		goto fail;
	}

	ts_extent_reset(&dts_64b, 1);
	ts_extent_reset(&pts_64b, 1);
	memset(_apiInitParam, 0, sizeof(API_VEGA_BQB_INIT_PARAM_T));
	_vrawBuf = (uint8_t *)vegaff_malloc(MAX_VRAW_BUF_SIZE);

	if (!_vrawBuf)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "image backup allocation failure, aborting\n");
		goto fail;
	}

	if (!_param->rc.bitrate)
		goto fail;

	cpbDelay = (uint32_t)(_param->cpbDelay + 0.5);
	bitrate = (_param->rc.bitrate >= 500) ? _param->rc.bitrate : 500;
	esBufSize = (((uint32_t)ceil(bitrate / (float)1000) << 17) * ((cpbDelay) ? (cpbDelay + 5) : 5));
	_esBufSize = esBufSize;
	_esBuf = (uint8_t *)vegaff_malloc(esBufSize);

	if (!_esBuf)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "es buffer allocation failure, aborting\n");
		goto fail;
	}

	_esBufPtr = _esBuf;
	_esBufEnd = _esBuf + esBufSize;
	_seiBuf = (uint8_t *)vegaff_malloc(MAX_SEI_BUF_SIZE);

	if (!_seiBuf)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "closed captions backup allocation failure, aborting\n");
		goto fail;
	}

	memset(_seiBuf, 0, MAX_SEI_BUF_SIZE);

	_inFrame = new Frame;

	if (!_inFrame->create(_param))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "memory allocation failure, aborting encode\n");
		_inFrame->destroy();
		delete _inFrame;
		_inFrame = NULL;
		goto fail;
	}

	memset(_mqName, 0, sizeof(_mqName));
#ifdef __linux__
	SNPRINTF(_mqName, sizeof(_mqName), "MessageQueue:Bd%02u:Ch%02u", _param->device, _param->channel);
#elif _WIN32
	SNPRINTF(_mqName, sizeof(_mqName), "MessageQueue_Bd%02u_Ch%02u", _param->device, _param->channel);
#endif

	try
	{
		boost::interprocess::message_queue::remove(_mqName);
		_mqReceiver = new boost::interprocess::message_queue(boost::interprocess::create_only, _mqName, 100,
															 sizeof(MessageOption));
	}
	catch (boost::interprocess::interprocess_exception &mq_err)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "mqReceiver: interprocess_exception of message queue = %d\n",
				   mq_err.what());
	}

	return true;

fail:
	_aborted = true;
	return false;
}

void vega_bqb_encoder::destroy()
{
	boost::interprocess::message_queue::remove(_mqName);

	clear_resource();
}

void vega_bqb_encoder::stop()
{
	if (_aborted)
	{
		if (_mqReceiveThread.joinable())
			_mqReceiveThread.join();

		API_VEGA_BQB_STATUS_E st =
			(API_VEGA_BQB_STATUS_E)VEGA_BQB_ENC_GetStatus((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel);

		if (st != API_VEGA_BQB_STATUS_ENCODING)
			return;

		if (VEGA_BQB_ENC_BlockingStop((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to blocking stop\n", _device, _channel);
		}

		return;
	}

	API_VEGA_BQB_STATUS_E st =
		(API_VEGA_BQB_STATUS_E)VEGA_BQB_ENC_GetStatus((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel);

	if (st != API_VEGA_BQB_STATUS_ENCODING)
	{
		if (_mqReceiveThread.joinable())
			_mqReceiveThread.join();

		return;
	}

	for (;;)
	{
		int ret = VEGA_BQB_ENC_Stop(_device, _channel);

		if (ret == API_VEGA_BQB_RET_NO_LAST_FRAME)
		{
			vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "LAST FRAME not yet being pushed! stop device:%d channel:%d failed!\n",
					   _device, _channel);
		}
		else if (ret == API_VEGA_BQB_RET_NO_LAST_ES)
		{
			vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "LAST ES not yet being popped! stop device:%d channel:%d failed!\n",
					   _device, _channel);
		}
		else if (ret == API_VEGA_BQB_RET_SUCCESS)
		{
			vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "stop device:%d channel:%d completed!\n", _device, _channel);
			break;
		}

		SLEEP_MICROSECOND(100000);
	}

	if (_mqReceiveThread.joinable())
		_mqReceiveThread.join();
}

void vega_bqb_encoder::reset()
{
	if (VEGA_BQB_ENC_Reset(_device))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d unable to reset vega encoder\n", _device);
	}
}

int vega_bqb_encoder::writeUserDataRegisteredSEI(API_VEGA_BQB_SEI_PARAM_T *sei_param)
{
	SEIuserDataRegistered ccsei;

	ccsei._userData = (uint8_t *)_seiBuf;
	ccsei._userDataLength = (uint8_t)_seiSize;

	if (ccsei.write(&sei_param[0]) < 0)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "ccsei.write failed\n");
		return -1;
	}

	return 0;
}

uint32_t vega_bqb_encoder::getBitrate()
{
	return VEGA_BQB_ENC_GetBitrate(_device, _channel);
}

uint32_t vega_bqb_encoder::getFramerate()
{
	return (uint32_t)VEGA_BQB_ENC_GetFramerate(_device, _channel);
}

int vega_bqb_encoder::forceIDR()
{
	if (VEGA_BQB_ENC_ForceIDR(_device, _channel))
		return -1;
	return 0;
}

int vega_bqb_encoder::forceIDRAt(uint32_t pic_num)
{
#define CHECK(expr, msg) check_failed |= _confirm(expr, msg)
	int check_failed = 0;

	CHECK(pic_num - 1 < _inFrameCnt, "Picture number cannot be less than present encoded frame count");

	if (check_failed)
		return -1;

	if (VEGA_BQB_ENC_ForceIDRAt(_device, _channel, pic_num))
		return -1;
	return 0;
}

bool vega_bqb_encoder::verifyIDRAt(uint32_t pic_num)
{
	int64_t startTime = vegaff_mdate();
	int64_t timeout = 1000000;
	bool    IsIdr = false;

	while (vegaff_mdate() - startTime < timeout)
	{
		SLEEP_MICROSECOND(100000);

		if ((VEGA_BQB_TYPE_I == (int)VEGA_BQB_ENC_GetFrameType(_device, _channel, pic_num)) &&
			(true == VEGA_BQB_ENC_IsForcedIdr(_device, _channel, pic_num)))
		{
			IsIdr = true;
			break;
		}
	}

	return IsIdr;
}

int vega_bqb_encoder::setFramerateAt(uint32_t fps, uint32_t pic_num)
{
	vegaff_codec_param *p = _param;
#define CHECK(expr, msg) check_failed |= _confirm(expr, msg)
	int check_failed = 0;

	uint32_t init_fps = p->fpsNum / p->fpsDenom;

	CHECK(pic_num - 1 < _inFrameCnt, "Picture number cannot be less than present encoded frame count");
	CHECK(init_fps != 25 && init_fps != 29 && init_fps != 30 && init_fps != 59 && init_fps != 60,
		  "Supported frame rate: 25, 29.97, 30, 59.94 or 60");
	CHECK((init_fps == 25 || init_fps == 50) && (fps != 25 && fps != 50), "Valid frame rate: 25 or 50");
	CHECK((init_fps == 29 || init_fps == 59) && (fps != 29 && fps != 59), "Valid frame rate: 29 or 59");
	CHECK((init_fps == 30 || init_fps == 60) && (fps != 30 && fps != 60), "Valid frame rate: 30 or 60");

	if (check_failed)
		return -1;

	if (VEGA_BQB_ENC_SetFramerateAt(_device, _channel, (API_VEGA_BQB_FPS_E)fps, pic_num))
		return -1;
	return 0;
}

int64_t vega_bqb_encoder::getPicInPts()
{
	return _picInPts;
}

int64_t vega_bqb_encoder::getPicOutPts(int64_t pts_90khz)
{
	return ts_extent_incrstep(&pts_64b, pts_90khz);
}

int vega_bqb_encoder::setFramerate(uint32_t fps)
{
	if (VEGA_BQB_ENC_SetFramerate((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel,
								  (API_VEGA_BQB_FPS_E)fps))
		return -1;
	return 0;
}

int vega_bqb_encoder::getStreamHeaders()
{
	if (_aborted)
		return -1;

	vegaff_codec_param *        p = (vegaff_codec_param *)_param;
	API_VEGA_BQB_IMG_T          img;
	API_VEGA_BQB_IMAGE_FORMAT_E imgFormat = API_VEGA_BQB_IMAGE_FORMAT_NV12;
	int                         colorSpace = p->internalCsp;
	int                         bitDepth = p->internalBitDepth;

	if (colorSpace == VEGA_BQB_CSP_NV12)
		imgFormat = API_VEGA_BQB_IMAGE_FORMAT_NV12;
	else if (colorSpace == VEGA_BQB_CSP_NV16)
		imgFormat = API_VEGA_BQB_IMAGE_FORMAT_NV16;
	else if (colorSpace == VEGA_BQB_CSP_I420 && bitDepth == 8)
		imgFormat = API_VEGA_BQB_IMAGE_FORMAT_I420;
	else if (colorSpace == VEGA_BQB_CSP_I420 && bitDepth == 10)
		imgFormat = API_VEGA_BQB_IMAGE_FORMAT_I0AL;
	else if (colorSpace == VEGA_BQB_CSP_I422 && bitDepth == 10)
		imgFormat = API_VEGA_BQB_IMAGE_FORMAT_I2AL;

	memset(_vrawBuf, 0, MAX_VRAW_BUF_SIZE);

	if (p->interlaceMode)
	{
		img.pu8Addr = (uint8_t *)_vrawBuf;
		img.u32Size = _inFrame->getPictureSize(NULL);
		img.pts = 0;
		img.bLastFrame = false;
		img.eTimeBase = API_VEGA_BQB_TIMEBASE_90KHZ;
		img.eFormat = imgFormat;
		img.u32SeiNum = 0;

		if (VEGA_BQB_ENC_PushImage(_device, _channel, &img))
		{
			vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "unable to get stream headers\n");
			_aborted = true;
			return -1;
		}
	}

	img.pu8Addr = (uint8_t *)_vrawBuf;
	img.u32Size = _inFrame->getPictureSize(NULL);
	img.pts = 0;
	img.bLastFrame = true;
	img.eTimeBase = API_VEGA_BQB_TIMEBASE_90KHZ;
	img.eFormat = imgFormat;
	img.u32SeiNum = 0;

	if (VEGA_BQB_ENC_PushImage(_device, _channel, &img))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "unable to get stream headers\n");
		_aborted = true;
		return -1;
	}

	return 0;
}

int vega_bqb_encoder::writeUserDataUnregisteredSEI(API_VEGA_BQB_SEI_PARAM_T *sei_param)
{
	uint8_t *               dst = NULL;
	uint32_t                dst_size = 0;
	SEIuserDataUnregistered idsei0;
	SEIuserDataUnregistered idsei1;
	uint8_t *               buffer = (uint8_t *)vegaff_malloc(MAX_SEI_BUF_SIZE);

	if (!buffer)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "SEI buffer allocation failure, aborting...\n");
		goto fail;
	}

	memset(buffer, 0, MAX_SEI_BUF_SIZE);
	dst = buffer;
	dst_size = MAX_SEI_BUF_SIZE;
	memcpy(dst, idsei0._uuid_iso_iec_11578, sizeof(idsei0._uuid_iso_iec_11578));
	dst += sizeof(idsei0._uuid_iso_iec_11578);
	dst_size -= sizeof(idsei0._uuid_iso_iec_11578);
	SNPRINTF((char *)dst, dst_size, "Serial Number: %u\n", _inFrameCnt);
	/* writing SEI 0 */
	idsei0._userData = buffer;
	idsei0._userDataLength = sizeof(idsei0._uuid_iso_iec_11578) + (uint8_t)strlen((char *)dst);

	if (idsei0.write(&sei_param[1]) < 0)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "idsei0.write failed\n");
		goto fail;
	}

	memset(buffer, 0, MAX_SEI_BUF_SIZE);
	dst = buffer;
	dst_size = MAX_SEI_BUF_SIZE;
	memcpy(dst, idsei1._uuid_iso_iec_11578, sizeof(idsei1._uuid_iso_iec_11578));
	dst += sizeof(idsei1._uuid_iso_iec_11578);
	dst_size -= sizeof(idsei1._uuid_iso_iec_11578);
	SNPRINTF((char *)dst, dst_size, "PTS: %" PRId64 "\n", _picInPts);
	/* writing SEI 1 */
	idsei1._userData = buffer;
	idsei1._userDataLength = sizeof(idsei1._uuid_iso_iec_11578) + (uint8_t)strlen((char *)dst);

	if (idsei1.write(&sei_param[2]) < 0)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "idsei1.write failed\n");
		goto fail;
	}

	vegaff_free(buffer);
	return 0;
fail:
	vegaff_free(buffer);
	return -1;
}

int vega_bqb_encoder::start()
{
	if (_aborted)
		return -1;

	API_VEGA_BQB_STATUS_E st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_STANDBY)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Encoder isn't in standby mode.\n");
		_aborted = true;
		return -2;
	}

	if (VEGA_BQB_ENC_Start(_device, _channel))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Dev(%d:%d) unable to start VEGA_BQB_AVC encoder\n", _device,
				   _channel);
		_aborted = true;
		return -3;
	}

	_aborted = false;
	_lastES = false;
	_mqReceiveThread = std::thread(&mqReceiveThreadMain, this);

	return 0;
}

API_VEGA_BQB_RESOLUTION_E vega_bqb_encoder::getResolution(int width, int height)
{
	API_VEGA_BQB_RESOLUTION_E res = API_VEGA_BQB_RESOLUTION_INVALID;

	for (int i = 0; i < gst_res_count; i++)
	{
		if (width == gst_res_table[i].width && height == gst_res_table[i].height)
		{
			res = gst_res_table[i].eRes;
			break;
		}
	}

	return res;
}

void vega_bqb_encoder::registVideoCaptureStartCallback(void *pfunc_callback)
{
	if (_aborted)
		return;

	vegaff_codec_param *p = _param;
	if (!p->inputMode)
		return;

	API_VEGA_BQB_STATUS_E st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_STANDBY)
		return;
	if (VEGA_BQB_ENC_RegisterVideoCaptureStartCallback(
			_device, _channel, (API_VEGA_BQB_VIDEO_CAPTURE_START_CALLBACK)pfunc_callback, (void *)this))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
				   "device:%d channel:%d unable to register video-capture-start callback function\n", _device,
				   _channel);
		_aborted = true;
		return;
	}
}

void vega_bqb_encoder::deregistVideoCaptureStartCallback()
{
	if (_aborted)
		return;

	vegaff_codec_param *p = _param;

	if (!p->inputMode)
		return;

	API_VEGA_BQB_STATUS_E st = VEGA_BQB_ENC_GetStatus(_device, _channel);
	if (st != API_VEGA_BQB_STATUS_ENCODING)
		return;

	if (VEGA_BQB_ENC_RegisterVideoCaptureStartCallback(_device, _channel, NULL, NULL))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
				   "device:%d channel:%d unable to deregister video-capture-start callback function\n", _device,
				   _channel);
		_aborted = true;
		return;
	}
}

void vega_bqb_encoder::exit()
{
	API_VEGA_BQB_STATUS_E st = VEGA_BQB_ENC_GetStatus(_device, _channel);

	if (st != API_VEGA_BQB_STATUS_STANDBY)
		return;

	if (VEGA_BQB_ENC_Exit((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to leave vega encoder\n", _device, _channel);
		return;
	}
	if (VEGA_BQB_XC_Exit((API_VEGA_BQB_DEVICE_E)_device, (API_VEGA_BQB_CHN_E)_channel) != API_VEGA_BQB_RET_SUCCESS)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "device:%d channel:%d unable to leave vega scaler\n", _device, _channel);
		return;
	}
}

int vega_bqb_encoder::setBitrate(uint32_t bitrate)
{
	vegaff_codec_param *p = _param;
#define CHECK(expr, msg) check_failed |= _confirm(expr, msg)
	int check_failed = 0;

	CHECK(bitrate == 0, "Target bitrate cannot be less than 0");
	CHECK(bitrate > VEGA_MAX_BITRATE, "Target bitrate cannot be greater than 600000");
	CHECK(bitrate > (uint32_t)p->rc.bitrate, "Target bitrate cannot be greater than initial bitrate");
	CHECK(p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR, "Invalid rate control mode");

	if (check_failed)
		return -1;

	if (VEGA_BQB_ENC_SetBitrate(_device, _channel, bitrate))
		return -1;
	return 0;
}

int vega_bqb_encoder::setVBR(uint32_t max, uint32_t ave, uint32_t min)
{

	vegaff_codec_param *p = _param;
#define CHECK(expr, msg) check_failed |= _confirm(expr, msg)
	int check_failed = 0;

	CHECK(max == 0, "VBR max bitrate cannot be 0");
	CHECK(max > VEGA_MAX_BITRATE, "VBR max bitrate cannot be greater than 600000");
	CHECK(ave == 0, "VBR average bitrate cannot be 0");
	CHECK(ave > VEGA_MAX_BITRATE, "VBR average bitrate cannot be greater than 600000");
	CHECK(min == 0, "VBR min bitrate cannot be 0");
	CHECK(min > VEGA_MAX_BITRATE, "VBR min bitrate cannot be greater than 600000");
	CHECK(max > (uint32_t)p->rc.vbrMaxBitrate, "VBR max bitrate cannot be greater than initial VBR max bitrate");
	CHECK(ave > max, "VBR average bitrate can be less than or equal to VBR max bitrate");
	CHECK(min > ave, "VBR min bitrate can be less than or equal to VBR average bitrate");
	CHECK(min > max, "VBR min bitrate can be less than or equal to VBR max bitrate");
	CHECK(p->rc.rateControlMode == VEGA_BQB_RC_CBR, "Invalid rate control mode");

	if (check_failed)
		return -1;

	if (VEGA_BQB_ENC_SetVBR(_device, _channel, max, ave, min))
		return -1;
	return 0;
}

int vega_bqb_encoder::setResolutionAt(int width, int height, uint32_t pic_num)
{
	vegaff_codec_param *p = _param;
#define CHECK(expr, msg) check_failed |= _confirm(expr, msg)
	int check_failed = 0;

	CHECK(pic_num - 1 < _inFrameCnt, "Picture number cannot be less than present encoded frame count");
	CHECK(width > p->sourceWidth, "Picture width cannot be greater than initial picture width");
	CHECK(height > p->sourceHeight, "Picture height cannot be greater than initial picture height");

	API_VEGA_BQB_RESOLUTION_E res = API_VEGA_BQB_RESOLUTION_INVALID;

	for (int i = 0; i < gst_res_count; i++)
	{
		if (width == gst_res_table[i].width && height == gst_res_table[i].height)
		{
			res = gst_res_table[i].eRes;
			break;
		}
	}
	if (res == API_VEGA_BQB_RESOLUTION_INVALID)
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "Unsupported resolution\n");
		check_failed = 1;
	}

	if (check_failed)
		return -1;

	if (VEGA_BQB_ENC_SetResolutionAt(_device, _channel, res, pic_num))
		return -1;
	return 0;
}

void vega_bqb_encoder::mqReceiveThreadMain(void *arg)
{
	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(arg);
	unsigned int      priority;
	size_t            recvd_size;
	bool              recvd = false;

	for (;;)
	{
		if (encoder->_aborted || encoder->_lastES)
			break;

		MessageOption msgOpt;
		recvd = encoder->_mqReceiver->try_receive((void *)&msgOpt, sizeof(MessageOption), recvd_size, priority);

		if (!recvd)
		{
			SLEEP_MICROSECOND(1000000);
			continue;
		}

		if (recvd_size != sizeof(MessageOption))
			break;

		MessageOptionType msgOptType = msgOpt.messageOptionType();

		switch (msgOptType)
		{
		case BITRATE_SET:
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "mqReceiver: bitrate before BITRATE_SET = %u\n",
					   encoder->getBitrate());
			encoder->setBitrate(msgOpt._bitrate);
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "mqReceiver: bitrate after BITRATE_SET = %u\n",
					   encoder->getBitrate());
			break;

		case VARIABLE_BITRATE_SET:
			encoder->setVBR(msgOpt._maxVbr, msgOpt._aveVbr, msgOpt._minVbr);
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "mqReceiver: VARIABLE_BITRATE_SET\n");
			break;

		case IDR_INSERT:
			encoder->forceIDR();
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "mqReceiver: IDR_INSERT\n");
			break;

		case IDR_INSERT_AT:
			encoder->forceIDRAt(msgOpt._picNum);
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "mqReceiver: IDR_INSERT_AT\n");
			break;

		case FRAMERATE_SET_AT:
			encoder->setFramerateAt(msgOpt._fps, msgOpt._picNum);
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "mqReceiver: FRAMERATE_SET_AT\n");
			break;

		case RESOLUTION_SET_AT:
			encoder->setResolutionAt(msgOpt._width, msgOpt._height, msgOpt._picNum);
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "mqReceiver: RESOLUTION_SET_AT\n");
			break;

		case UNKNOWN_OPT:
		default:
			vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "mqReceiver: UNKNOWN_OPT\n");
			break;
		}
	}
}

void vegaff_signal_capture_start(const int capture_counter, vega_bqb_encoder *args)
{
	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(args);
	encoder->_inFrameCnt = capture_counter - 1;
	vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "bd=%d ch=%d input frame=%d\n", encoder->_device, encoder->_channel,
			   encoder->_inFrameCnt);
}

int vegaff_encoder_pushimage(vega_bqb_encoder *enc, vegaff_picture_t *pic_in)
{
	vega_bqb_encoder *encoder;

	if (!enc)
		return -1;

	encoder = static_cast<vega_bqb_encoder *>(enc);
	encoder->encode(pic_in);

	return (encoder->_aborted) ? -1 : 0;
}

void vegaff_encoder_start(vegaffhandle_t enc)
{
	if (enc)
	{
		vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
		encoder->start();
	}
}

void vegaff_encoder_stop(vegaffhandle_t enc)
{
	if (enc)
	{
		vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
		encoder->stop();
	}
}

void vegaff_encoder_reset(vegaffhandle_t enc)
{
	if (enc)
	{
		vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
		encoder->reset();
	}
}

int vegaff_encoder_setbitrate(vegaffhandle_t enc, uint32_t bitrate)
{
	if (!enc)
		return -1;

	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);

	vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "bitrate before BITRATE_SET = %u\n", encoder->getBitrate());

	if (encoder->setBitrate(bitrate))
		return -1;

	vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "bitrate after BITRATE_SET = %u\n", encoder->getBitrate());

	return 0;
}

int vegaff_encoder_setvbr(vegaffhandle_t enc, uint32_t max, uint32_t ave, uint32_t min)
{
	if (!enc)
		return -1;
	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
	return encoder->setVBR(max, ave, min) ? -1 : 0;
}

int vegaff_encoder_forceidr(vegaffhandle_t enc)
{
	if (!enc)
		return -1;
	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
	return (encoder->forceIDR()) ? -1 : 0;
}

int vegaff_encoder_forceidrat(vegaffhandle_t enc, uint32_t pic_num)
{
	if (!enc)
		return -1;

	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
	if (encoder->forceIDRAt(pic_num))
		return -1;

	if (false == encoder->verifyIDRAt(pic_num))
	{
		vegaff_log(NULL, VEGA_BQB_LOG_ERROR, "inserting IDR at picture number %d failed\n", pic_num);
		return -1;
	}

	vegaff_log(NULL, VEGA_BQB_LOG_VERBOSE, "inserting IDR at picture number %d succeeded\n", pic_num);
	return 0;
}

int vegaff_encoder_setframerate(vegaffhandle_t enc, uint32_t fps)
{
	if (!enc)
		return -1;

	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
	return (encoder->setFramerate(fps)) ? -1 : 0;
}

int vegaff_encoder_setframerateat(vegaffhandle_t enc, uint32_t fps, uint32_t pic_num)
{
	if (!enc)
		return -1;

	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
	return (encoder->setFramerateAt(fps, pic_num)) ? -1 : 0;
}

int vegaff_encoder_encode(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal, vegaff_picture_t *pic_in,
						  vegaff_picture_t *pic_out)
{
	int i = 0;
	int loop = 0;
	int timeout = 1000;
	int ret;

	if (!enc)
		return -1;

	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
	ret = encoder->encode(pic_in);
	if (ret < 0)
		return ret;

	if (encoder->_aborted)
		return -1;

	pic_out->is_last_pic = 0;
	// !pic_in indicates ffmpeg has no more input,
	// hang on here until at least one ES be popped.
	if (pi_nal && !pic_in)
	{
		if (encoder->_outFrameCnt < encoder->_inFrameCnt || encoder->_lastES == false)
		{
			while (loop < timeout)
			{
				if (!encoder->_esQueue.empty())
					break;
				else
				{
					loop++;
					SLEEP_MICROSECOND(1000);
				}
			}

			if ((loop == timeout) && (encoder->_lastES == false))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device %d channel %d: waiting for lastES timeout, lastES = %s, input frame = %d, "
						   "output frame = %d\n",
						   encoder->_device, encoder->_channel, ((encoder->_lastES) ? "true" : "false"),
						   encoder->_inFrameCnt, encoder->_outFrameCnt);
				return -1;
			}
		}
	}

	if (pp_nal && !encoder->_esQueue.empty())
	{
		Queue<vegaff_nal_t> tempQueue = encoder->_esQueue.pop();
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d from vega: output frame=%d\n", encoder->_device,
				   encoder->_channel, encoder->_outFrameCnt++);
		encoder->_nalList._numNal = (uint32_t)tempQueue.size();

		while (!tempQueue.empty())
		{
			encoder->_nalList._nal[i++] = tempQueue.pop();
			encoder->_esBufSize = encoder->_esBufSize + encoder->_nalList._nal[i - 1].u32Length;
		}

		*pp_nal = &encoder->_nalList._nal[0];

		if (pi_nal)
		{
			*pi_nal = encoder->_nalList._numNal;
		}
		pic_out->is_last_pic = tempQueue.is_last_ES ? 1 : 0;

		pic_out->pts = encoder->_nalList._nal[0].pts;
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d: pic out pts=%ld\n", encoder->_device,
				   encoder->_channel, pic_out->pts);
		pic_out->dts = encoder->_nalList._nal[0].dts;
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d: pic out dts=%ld\n", encoder->_device,
				   encoder->_channel, pic_out->dts);
		pic_out->sliceType = encoder->_nalList._nal[0].sliceType;
	}
	else if (pi_nal && pic_in)
		*pi_nal = 0;

	return 0;
}

int vegaff_encoder_getes(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal, vegaff_picture_t *pic_in,
						 vegaff_picture_t *pic_out)
{
	int i = 0;
	int loop = 0;
	int timeout = 1000;

	*pi_nal = 0;
	*pp_nal = NULL;
	if (!enc)
		return -1;

	vega_bqb_encoder *encoder = static_cast<vega_bqb_encoder *>(enc);
	if (pi_nal && !pic_in)
	{
		if (encoder->_outFrameCnt < encoder->_inFrameCnt || encoder->_lastES == false)
		{
			while (loop < timeout)
			{
				vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "%s:%d busy waiting\n", __FUNCTION__, __LINE__);
				if (!encoder->_esQueue.empty())
					break;
				else
				{
					loop++;
					SLEEP_MICROSECOND(1000);
				}
			}

			if ((loop == timeout) && (encoder->_lastES == false))
			{
				vegaff_log(NULL, VEGA_BQB_LOG_ERROR,
						   "device %d channel %d: waiting for lastES timeout, lastES = %s, input frame = %d, "
						   "output frame = %d\n",
						   encoder->_device, encoder->_channel, ((encoder->_lastES) ? "true" : "false"),
						   encoder->_inFrameCnt, encoder->_outFrameCnt);
				return -1;
			}
		}
	}

	if (pp_nal && !encoder->_esQueue.empty())
	{
		Queue<vegaff_nal_t> tempQueue = encoder->_esQueue.pop();
		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d from vega: output frame=%d\n", encoder->_device,
				   encoder->_channel, encoder->_outFrameCnt++);
		encoder->_nalList._numNal = (uint32_t)tempQueue.size();

		pic_out->is_last_pic = (tempQueue.is_last_ES) ? 1 : 0;
		while (!tempQueue.empty())
		{
			encoder->_nalList._nal[i++] = tempQueue.pop();
			encoder->_esBufSize = encoder->_esBufSize + encoder->_nalList._nal[i - 1].u32Length;
		}

		*pp_nal = &encoder->_nalList._nal[0];
		if (pi_nal)
		{
			*pi_nal = encoder->_nalList._numNal;
		}

		pic_out->pts = encoder->_nalList._nal[0].pts;
		pic_out->dts = encoder->_nalList._nal[0].dts;
		pic_out->sliceType = encoder->_nalList._nal[0].sliceType;

		vegaff_log(NULL, VEGA_BQB_LOG_DEBUG, "device %d channel %d: pic out pts=%lld dts=%lld last=%d\n",
				   encoder->_device, encoder->_channel, (long long)pic_out->pts, (long long)pic_out->dts,
				   (int)pic_out->is_last_pic);
	}
	else if (pi_nal && pic_in)
		*pi_nal = 0;

	return 0;
}
