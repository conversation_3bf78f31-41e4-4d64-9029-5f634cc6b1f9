#pragma once

#include <boost/interprocess/ipc/message_queue.hpp>
//#include <boost/interprocess/managed_shared_memory.hpp>
//#include <boost/interprocess/sync/named_condition.hpp>
//#include <boost/interprocess/sync/named_mutex.hpp>
//#include <boost/interprocess/sync/scoped_lock.hpp>
#include <thread>

#ifdef _WIN32
#include <Windows.h>
#endif

#include "../common/common.h"
#include "../common/queue.h"
#include "../common/nal.h"
#include "../common/sei.h"
#include "../vegaff.h"

class Frame;

class vega_bqb_encoder
{
public:
	vega_bqb_encoder();
	virtual ~vega_bqb_encoder();

	virtual void                      clear_name();
	virtual void                      clear_resource();
	virtual int                       configure(const vegaff_codec_param *p) = 0;
	virtual bool                      create(const vegaff_codec_param *p);
	virtual void                      destroy();
	virtual int                       encode(const vegaff_picture_t *pic_in) = 0;
	virtual void                      exit();
	virtual void                      flush() = 0;
	virtual int                       forceIDR();
	virtual int                       forceIDRAt(uint32_t pic_num);
	virtual uint32_t                  getBitrate();
	virtual uint32_t                  getFramerate();
	virtual int64_t                   getPicInPts();
	virtual int64_t                   getPicOutDts(int64_t dts_90khz);
	virtual int64_t                   getPicOutPts(int64_t pts_90khz);
	virtual API_VEGA_BQB_RESOLUTION_E getResolution(int width, int height);
	virtual int                       getStreamHeaders();
	virtual void                      registEsPopCallback(void *pfunc_callback) = 0;
	virtual void                      reset();
	virtual int                       setBitrate(uint32_t bitrate);
	virtual int                       setFramerate(uint32_t fps);
	virtual int                       setFramerateAt(uint32_t fps, uint32_t pic_num);
	virtual int                       setResolutionAt(int width, int height, uint32_t pic_num);
	virtual int                       setVBR(uint32_t max, uint32_t ave, uint32_t min);
	virtual int                       start();
	virtual void                      stop();
	virtual bool                      verifyIDRAt(uint32_t pic_num);
	virtual int                       writeUserDataRegisteredSEI(API_VEGA_BQB_SEI_PARAM_T *sei_param);
	virtual int                       writeUserDataUnregisteredSEI(API_VEGA_BQB_SEI_PARAM_T *sei_param);

	virtual void registVideoCaptureStartCallback(void *pfunc_callback);
	virtual void deregistVideoCaptureStartCallback();
	static void  mqReceiveThreadMain(void *arg);

	bool                                _aborted;
	API_VEGA_BQB_INIT_PARAM_T *         _apiInitParam;
	API_VEGA_BQB_CHN_E                  _channel;
	API_VEGA_BQB_DEVICE_E               _device;
	uint8_t *                           _esBuf;
	uint8_t *                           _esBufEnd;
	uint8_t *                           _esBufPtr;
	uint32_t                            _esBufSize;
	Queue<Queue<vegaff_nal_t>>          _esQueue;
	Frame *                             _inFrame;
	uint32_t                            _inFrameCnt;
	bool                                _lastES;
	char                                _mqName[32];
	std::thread                         _mqReceiveThread;
	boost::interprocess::message_queue *_mqReceiver;
	NALList                             _nalList;
	uint32_t                            _outFrameCnt;
	vegaff_codec_param *                _param;
	bool                                _pendingFrame;
	API_VEGA_BQB_IMAGE_FORMAT_E         _picInFmt;
	int64_t                             _picInPts;
	uint32_t                            _picInSize;
	uint8_t *                           _seiBuf;
	size_t                              _seiSize;
	uint8_t *                           _vrawBuf;
	ts_extent_t                         dts_64b;
	ts_extent_t                         pts_64b;

	const stResolution *gst_res_table;
	int                 gst_res_count;
};
