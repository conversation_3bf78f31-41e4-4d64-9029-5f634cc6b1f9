/*
 *
 * Copyright (C) 2019 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#include "../common/common.h"
#include "../common/constants.h"
#include "param262.h"
#include "../vegaff.h"

extern "C" {
#include "libavutil/log.h"
#include "libavutil/avstring.h"
}

#ifdef _MSC_VER
#pragma warning(disable : 4996) // POSIX functions are just fine, thanks
#endif

extern "C" const stResolution gstRes262[];
extern "C" const int          gstRes262_count;

/* this table is kept internal to avoid confusion, since log level indices start at -1 */
static const char *const logLevelNames[] = {"none", "error", "warning", "info", "frame", "debug", "full", 0};

vega_bqb_mpeg_param *vega_bqb_mpeg_param_alloc()
{
	return vegaff_zallocT<vega_bqb_mpeg_param>();
}

vega_bqb_mpeg_dec_param *vega_bqb_mpeg_dec_param_alloc()
{
	return vegaff_zallocT<vega_bqb_mpeg_dec_param>();
}

void vega_bqb_mpeg_param_free(void *p)
{
	vegaff_free(p);
}

void vega_bqb_mpeg_dec_param_free(vega_bqb_mpeg_dec_param *p)
{
	vegaff_free(p);
}

void vega_bqb_mpeg_param_default(vega_bqb_mpeg_param *param)
{
	vegaff_zeroT(param);
	/* Applying default values to all elements in the param structure */
	param->logLevel = VEGA_BQB_LOG_INFO;

	/* Video properties */
	param->internalBitDepth = 8;
	param->internalCsp = VEGA_BQB_CSP_NV12;
	param->levelIdc = API_VEGA_BQB_MPEG_LEVEL_AUTO;
	param->vui.bEnableVideoSignalTypePresentFlag = 0;
	param->vui.videoFormat = 0;
	param->vui.bEnableColorDescriptionPresentFlag = 0;
	param->vui.colorPrimaries = 0;
	param->vui.transferCharacteristics = 0;
	param->vui.matrixCoeffs = 0;
	param->vui.bEnableDefaultDisplayWindowFlag = 0;
	param->inputWidth = 0;
	param->inputHeight = 0;
	/* Encoder parameters */
	param->gopType = 2;
	param->keyframeMax = 24;
	param->bOpenGOP = 1;
	param->scenecutThreshold = 0;
	param->interlaceMode = 0;

	param->rc.rateControlMode = VEGA_BQB_RC_CBR;
	param->rc.bitrate = 0;
	param->rc.vbrMaxBitrate = 0;
	param->rc.vbrAveBitrate = 0;
	param->rc.vbrMinBitrate = 0;
	param->rc.vbrFillerBitrate = 0;
	param->rc.bStrictCbr = 0;

	param->cpbDelay = 0;

	/* VENC */
	param->device = 0;
	param->channel = 0;
	param->inputMode = 0;
	param->dbgLevel = 0;
}

extern "C" void vega_bqb_mpeg_dec_param_default(vega_bqb_mpeg_dec_param *param)
{
	vegaff_zeroT(param);
	/* Applying default values to all elements in the param structure */
	param->logLevel = VEGA_BQB_LOG_INFO;

	/* VDEC */
	param->device = 0;
	param->channel = 0;
	param->inputMode = 0;
	param->outputPath = 0;
	param->outputFormat = 0;
	param->dbgLevel = 0;
}

extern "C" void vega_bqb_mpeg_param_default_bitrate(vega_bqb_mpeg_param *p)
{
	if (p->sourceWidth == 1920 && p->sourceHeight == 1080)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 8000;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 15000;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 1280 && p->sourceHeight == 720)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 4000;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 4000;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 720 && (p->sourceHeight == 576 || p->sourceHeight == 480))
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 1500;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 1500;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 1920 && p->sourceHeight == 540 && p->interlaceMode)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 8000;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 8000;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 720 && (p->sourceHeight == 288 || p->sourceHeight == 240) && p->interlaceMode)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 1500;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 1500;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 416 && p->sourceHeight == 240)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 500;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 500;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 352 && p->sourceHeight == 288)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 500;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 500;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 1000;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 1000;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
}

/* internal versions of string-to-int with additional error checking */
#define ATOI(str)    vegaff_atoi(str, bError)
#define ATOF(str)    vegaff_atof(str, bError)
#define ATOBOOL(str) (bNameWasBool = true, vegaff_atobool(str, bError))

extern "C" int vega_bqb_mpeg_param_parse(vega_bqb_mpeg_param *p, const char *name, const char *value)
{
	bool bError = false;
	bool bNameWasBool = false;
	bool bValueWasNull = !value;
	char nameBuf[64];

	if (!name)
		return VEGA_BQB_PARAM_BAD_NAME;

	// skip -- prefix if provided
	if (name[0] == '-' && name[1] == '-')
		name += 2;

	// s/_/-/g
	if (strlen(name) + 1 < sizeof(nameBuf) && strchr(name, '_'))
	{
		char *c;
		strcpy(nameBuf, name);

		while ((c = strchr(nameBuf, '_')) != 0)
			*c = '-';

		name = nameBuf;
	}

	if (!strncmp(name, "no-", 3))
	{
		name += 3;
		value = !value || vegaff_atobool(value, bError) ? "false" : "true";
	}
	else if (!strncmp(name, "no", 2))
	{
		name += 2;
		value = !value || vegaff_atobool(value, bError) ? "false" : "true";
	}
	else if (!value)
		value = "true";
	else if (value[0] == '=')
		value++;

#define OPT(STR)         else if (!av_strcasecmp(name, STR))
#define OPT2(STR1, STR2) else if (!av_strcasecmp(name, STR1) || !av_strcasecmp(name, STR2))

	if (0)
		;

	OPT("fps")
	{
		if (sscanf(value, "%u/%u", &p->fpsNum, &p->fpsDenom) == 2)
			;
		else
		{
			float fps = (float)ATOF(value);

			if (fps > 0 && fps <= INT_MAX / 1000)
			{
				p->fpsNum = (int)(fps * 1000 + .5);
				p->fpsDenom = 1000;
			}
			else
			{
				p->fpsNum = ATOI(value);
				p->fpsDenom = 1;
			}
		}
	}

	OPT2("level-idc", "level")
	{
		if (!av_strcasecmp(value, "auto"))
			p->levelIdc = API_VEGA_BQB_MPEG_LEVEL_AUTO;
		else if (!av_strcasecmp(value, "high"))
			p->levelIdc = API_VEGA_BQB_MPEG_LEVEL_HIGH;
		else if (!av_strcasecmp(value, "high1440"))
			p->levelIdc = API_VEGA_BQB_MPEG_LEVEL_HIGH14;
		else if (!av_strcasecmp(value, "main"))
			p->levelIdc = API_VEGA_BQB_MPEG_LEVEL_MAIN;
		else if (!av_strcasecmp(value, "low"))
			p->levelIdc = API_VEGA_BQB_MPEG_LEVEL_LOW;
		else
			p->levelIdc = API_VEGA_BQB_MPEG_LEVEL_AUTO;
	}
	OPT2("log-level", "log")
	{
		p->logLevel = ATOI(value);

		if (bError)
		{
			bError = false;
			p->logLevel = vegaff_parseName(value, logLevelNames, bError) - 1;
		}
	}
	OPT("open-gop") p->bOpenGOP = ATOI(value);
	OPT("scenecut")
	{
		p->scenecutThreshold = ATOBOOL(value);
		if (bError || p->scenecutThreshold)
		{
			bError = false;
			p->scenecutThreshold = ATOI(value);
		}
	}
	OPT("gop-type") p->gopType = ATOI(value);
	OPT("keyint")
	{
		if (strstr(value, "infinite"))
			p->keyframeMax = VEGA_BQB_KEYINT_MAX_INFINITE;
		else
			p->keyframeMax = ATOI(value);
	}
	OPT("interlaced")
	{
		p->interlaceMode = ATOBOOL(value);
		printf("bError %d, interlacemode: %d, value:%s\n", bError, p->interlaceMode, value);
		if (bError || p->interlaceMode)
		{
			bError = false;
			p->interlaceMode = vegaff_parseName(value, vega_bqb_mpeg_interlace_names, bError);
		}
	}
	OPT("rate-ctrl")
	{
		if (!av_strcasecmp(value, "cbr"))
			p->rc.rateControlMode = VEGA_BQB_RC_CBR;
		else if (!av_strcasecmp(value, "vbr"))
			p->rc.rateControlMode = VEGA_BQB_RC_CAPPED_VBR;
	}
#if 0
	OPT("fillerrate")
	{
		p->rc.fillerrate = ATOI(value);
	}
#endif
	OPT("bitrate")
	{
		int is_err = 0;
		p->rc.bitrate = vegaff_str_to_weighted_size(value, &is_err);
		if (is_err)
			bError |= 1;
	}
	OPT("vbr")
	{
		int      is_err = 0;
		char *   saveptr, *token, *line;
		uint32_t tvalue[3] = {0, 0, 0};
		int      i;
		line = (char *)value;
		/* get V-MAX, V-AVG, and V-MIN */
		for (i = 0; i < 3; i++, line = NULL)
		{
			token = STR_TOK(line, ",", &saveptr);
			if (token)
			{
				tvalue[i] = vegaff_str_to_weighted_size(token, &is_err);
				if (is_err)
					bError |= 1;
			}
			else
			{
				bError |= 1;
				break;
			}
		}
		p->rc.vbrMaxBitrate = tvalue[0];
		p->rc.vbrAveBitrate = tvalue[1];
		p->rc.vbrMinBitrate = tvalue[2];
	}
	OPT("input-depth") p->internalBitDepth = ATOI(value);
	OPT("input-res") bError |= sscanf(value, "%dx%d", &p->sourceWidth, &p->sourceHeight) != 2;
	OPT("input-csp") p->internalCsp = vegaff_parseName(value, vega_bqb_mpeg_source_csp_names, bError);
	OPT("strict-cbr") p->rc.bStrictCbr = ATOBOOL(value);
	OPT("videoformat")
	{
		p->vui.bEnableVideoSignalTypePresentFlag = 1;
		p->vui.videoFormat = vegaff_parseName(value, vega_bqb_mpeg_video_format_names, bError);
	}
	OPT("colorprim")
	{
		p->vui.bEnableVideoSignalTypePresentFlag = 1;
		p->vui.bEnableColorDescriptionPresentFlag = 1;
		p->vui.colorPrimaries = vegaff_parseName(value, vega_bqb_mpeg_colorprim_names, bError);
	}
	OPT("transfer")
	{
		p->vui.bEnableVideoSignalTypePresentFlag = 1;
		p->vui.bEnableColorDescriptionPresentFlag = 1;
		p->vui.transferCharacteristics = vegaff_parseName(value, vega_bqb_mpeg_transfer_names, bError);
	}
	OPT("colormatrix")
	{
		p->vui.bEnableVideoSignalTypePresentFlag = 1;
		p->vui.bEnableColorDescriptionPresentFlag = 1;
		p->vui.matrixCoeffs = vegaff_parseName(value, vega_bqb_mpeg_colmatrix_names, bError);
	}
	OPT("device") p->device = ATOI(value);
	OPT("channel") p->channel = ATOI(value);
	OPT("input-mode") p->inputMode = ATOI(value);
	OPT("dbg-level") p->dbgLevel = ATOI(value);
	OPT("cpb-delay") p->cpbDelay = ATOF(value);
	OPT("robust-mode") p->robustMode = ATOI(value);
	else return VEGA_BQB_PARAM_BAD_NAME;

#undef OPT
#undef OPT2
	bError |= bValueWasNull && !bNameWasBool;
	return bError ? VEGA_BQB_PARAM_BAD_VALUE : 0;
}

extern "C" int vega_bqb_mpeg_dec_param_parse(vega_bqb_mpeg_dec_param *p, const char *name, const char *value)
{
	bool bError = false;
	bool bNameWasBool = false;
	bool bValueWasNull = !value;
	char nameBuf[64];

	if (!name)
		return VEGA_BQB_PARAM_BAD_NAME;

	// skip -- prefix if provided
	if (name[0] == '-' && name[1] == '-')
		name += 2;

	// s/_/-/g
	if (strlen(name) + 1 < sizeof(nameBuf) && strchr(name, '_'))
	{
		char *c;
		strcpy(nameBuf, name);

		while ((c = strchr(nameBuf, '_')) != 0)
			*c = '-';

		name = nameBuf;
	}

	if (!strncmp(name, "no-", 3))
	{
		name += 3;
		value = !value || vegaff_atobool(value, bError) ? "false" : "true";
	}
	else if (!strncmp(name, "no", 2))
	{
		name += 2;
		value = !value || vegaff_atobool(value, bError) ? "false" : "true";
	}
	else if (!value)
		value = "true";
	else if (value[0] == '=')
		value++;

#define OPT(STR)         else if (!av_strcasecmp(name, STR))
#define OPT2(STR1, STR2) else if (!av_strcasecmp(name, STR1) || !av_strcasecmp(name, STR2))

	if (0)
		;

	OPT2("log-level", "log")
	{
		p->logLevel = ATOI(value);

		if (bError)
		{
			bError = false;
			p->logLevel = vegaff_parseName(value, logLevelNames, bError) - 1;
		}
	}
	OPT("device") p->device = ATOI(value);
	OPT("channel")
	{
		p->channel = ATOI(value);
		if (p->channel != 0 && p->channel != 2)
			bError = true;
	}
	OPT("input-mode") p->inputMode = ATOI(value);
	OPT("output-path")
	{
		p->outputPath = ATOI(value);
		// p->outputFormat = (p->outputPath) ? 0 : 1;
		p->outputFormat = 0;
	}
	OPT("output-format")
	{
		p->outputFormat = ATOI(value); /* 0=vraw, 1=yuv422p10 , 2=SNI_V210 , 3=NV16*/
	}
	OPT("dbg-level") p->dbgLevel = ATOI(value);
	else return VEGA_BQB_PARAM_BAD_NAME;

#undef OPT
#undef OPT2
	bError |= bValueWasNull && !bNameWasBool;
	return bError ? VEGA_BQB_PARAM_BAD_VALUE : 0;
}

template <typename T> static inline int _confirm(T *param, bool bflag, const char *message)
{
	if (!bflag)
		return 0;

	vegaff_log(param, VEGA_BQB_LOG_ERROR, "%s\n", message);
	return 1;
}

static int _vega_bqb_check_res(const vega_bqb_mpeg_param *p)
{
	char  string[2048];
	char *pString = string;
	pString += sprintf(pString, "Picture resolution must be ");
	for (int i = 0; i < gstRes262_count; i++)
	{
		if (p->sourceWidth == gstRes262[i].width && p->sourceHeight == gstRes262[i].height)
		{
			return 0;
		}
		if (i != API_VEGA_BQB_RESOLUTION_INVALID - 1)
			pString += sprintf(pString, "%s, ", gstRes262[i].string);
		else
			pString += sprintf(pString, "%s", gstRes262[i].string);
	}
	vegaff_log(p, VEGA_BQB_LOG_ERROR, "%s\n", string);
	return 1;
}

int vega_bqb_mpeg_check_params(const vega_bqb_mpeg_param *param)
{
#define CHECK(expr, msg) check_failed |= _confirm(param, expr, msg)
	int check_failed = 0; /* abort if there is a fatal configuration problem */

	if (check_failed == 1)
		return check_failed;

	/* These checks might be temporary */
	CHECK(param->internalBitDepth != 10 && param->internalBitDepth != 8,
		  "only 10bit and 8bit internal depth supported");
	CHECK(param->fpsNum == 0 || param->fpsDenom == 0, "Frame rate numerator and denominator must be specified");
	CHECK(param->interlaceMode < 0 || param->interlaceMode > 1,
		  "Interlace mode must be 0 (progressive) or 1 (interlace)");

	CHECK(param->levelIdc != API_VEGA_BQB_MPEG_LEVEL_AUTO && param->levelIdc != API_VEGA_BQB_MPEG_LEVEL_HIGH &&
			  param->levelIdc != API_VEGA_BQB_MPEG_LEVEL_HIGH14 && param->levelIdc != API_VEGA_BQB_MPEG_LEVEL_MAIN &&
			  param->levelIdc != API_VEGA_BQB_MPEG_LEVEL_LOW,
		  "Level must be auto, high, high1440, main, low");

	CHECK(param->internalCsp < VEGA_BQB_CSP_I420 || VEGA_BQB_CSP_NV16 < param->internalCsp,
		  "Color space must be i420, i422, nv12 or nv16");
	CHECK(param->sourceWidth > 1920 || param->sourceHeight > 1080, "Max resolution is 1920x1080");

	CHECK((param->sourceWidth > 1280 || param->sourceHeight > 720) && param->interlaceMode == 0 &&
			  (param->fpsNum / param->fpsDenom > 30),
		  "This resolution can't over 30 fps");

	CHECK(param->rc.rateControlMode > VEGA_BQB_RC_CAPPED_VBR || param->rc.rateControlMode < VEGA_BQB_RC_CBR,
		  "Rate control mode is out of range");

	CHECK(param->vui.videoFormat < 0 || param->vui.videoFormat > 5, "Video Format must be component,"
																	" pal, ntsc, secam, mac or undef");
	CHECK(param->vui.colorPrimaries < 0 || param->vui.colorPrimaries > 9 || param->vui.colorPrimaries == 3,
		  "Color Primaries must be undef, bt709, bt470m,"
		  " bt470bg, smpte170m, smpte240m, film or bt2020");
	CHECK(param->vui.transferCharacteristics < 0 || param->vui.transferCharacteristics > 18 ||
			  param->vui.transferCharacteristics == 3,
		  "Transfer Characteristics must be undef, bt709, bt470m, bt470bg,"
		  " smpte170m, smpte240m, linear, log100, log316, iec61966-2-4, bt1361e,"
		  " iec61966-2-1, bt2020-10, bt2020-12, smpte-st-2084, smpte-st-428 or arib-std-b67");
	CHECK(param->vui.matrixCoeffs < 0 || param->vui.matrixCoeffs > 10 || param->vui.matrixCoeffs == 3,
		  "Matrix Coefficients must be undef, bt709, fcc, bt470bg, smpte170m,"
		  " smpte240m, GBR, YCgCo, bt2020nc or bt2020c");

	CHECK(param->logLevel < -1 || param->logLevel > VEGA_BQB_LOG_FULL,
		  "Valid Logging level -1:none 0:error 1:warning 2:info 3:debug 4:full");
	CHECK(param->scenecutThreshold < 0 || param->scenecutThreshold > 1,
		  "scenecutThreshold must be 0:not insert I-picture or 1:insert I-picture");
	CHECK(param->bOpenGOP < 0 || param->bOpenGOP > 3, "Valid open GOP value 0:closed GOP 1:open GOP 2:every two IRAP "
													  "frames has one as IDR 3:every 100 IRAP frames has one as IDR");
	CHECK(param->keyframeMax > 240, "GOP size should be smaller than 240");

	CHECK(param->rc.bitrate <= 0, "Target bitrate cannot be less than or equal to zero");
	CHECK(param->rc.bitrate > VEGA_MAX_BITRATE, "Target bitrate cannot be greater than 80000");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMaxBitrate <= 0),
		  "VBR max bitrate cannot be less than or equal to zero");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMaxBitrate > VEGA_MAX_BITRATE),
		  "VBR max bitrate cannot be greater than 80000");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrAveBitrate <= 0),
		  "VBR average bitrate cannot be less than or equal to zero");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrAveBitrate > VEGA_MAX_BITRATE),
		  "VBR average bitrate cannot be greater than 80000");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate <= 0),
		  "VBR min bitrate cannot be less than or equal to zero");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate > VEGA_MAX_BITRATE),
		  "VBR min bitrate cannot be greater than 80000");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMaxBitrate > param->rc.bitrate),
		  "VBR max bitrate can be less then or equal to target bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrAveBitrate > param->rc.bitrate),
		  "VBR average bitrate can be less than or equal to target bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrAveBitrate > param->rc.vbrMaxBitrate),
		  "VBR average bitrate can be less than or equal to VBR max bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate > param->rc.bitrate),
		  "VBR min bitrate can be less than or equal to target bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate > param->rc.vbrMaxBitrate),
		  "VBR min bitrate can be less than or equal to VBR max bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate > param->rc.vbrAveBitrate),
		  "VBR min bitrate can be less than or equal to VBR average bitrate");
	CHECK(param->rc.bStrictCbr && (param->rc.bitrate <= 0),
		  "Strict-cbr cannot be applied without specifying target bitrate");
	CHECK(param->device < 0 || param->device > 7,
		  "Valid device number 0:device1 1:device2 2:device3 3:device4 4:device5 5:device6 6:device7 7:device8");
	CHECK(param->channel < 0 || param->channel > 11,
		  "Valid channel number 0:channel1 1:channel2 2:channel3 ...11:channel12");

	CHECK(param->inputMode < 0 || param->inputMode > 1, "Invalid input mode. value must be 0, 1");
	CHECK(param->gopType < 0 || param->gopType > 3, "Invalid gop type. value must be 0, 1, 2, 3");
	CHECK((param->gopType == 0) && (param->keyframeMax != 1),
		  "Invalid GOP size. gop type = 0(I frame only) Gop size value must be 1");
	CHECK((param->gopType == 1) && (param->keyframeMax < 1),
		  "Invalid GOP size. gop type = 1(IP) Gop size value must be larger than 1");
	CHECK((param->gopType == 2) && (param->keyframeMax % 2 != 0),
		  "Invalid GOP size. gop type = 2(IBP) Gop size value must be multiple of 2");
	CHECK((param->gopType == 3) && (param->keyframeMax % 3 != 0),
		  "Invalid GOP size. gop type = 3(IBBP) Gop size value must be multiple of 3");

	CHECK(param->dbgLevel < 0 || param->dbgLevel > 3, "Invalid debug level for VEGA API. value must be 0, 1, 2, 3");
	CHECK(param->cpbDelay < 0, "cpb delay can not be less than zero");
	check_failed |= _vega_bqb_check_res(param);
	return check_failed;
}

int vega_bqb_mpeg_dec_check_params(const vega_bqb_mpeg_dec_param *param)
{
#define CHECK(expr, msg) check_failed |= _confirm(param, expr, msg)
	int check_failed = 0; /* abort if there is a fatal configuration problem */

	if (check_failed == 1)
		return check_failed;

	/* These checks might be temporary */
	CHECK(param->logLevel < -1 || param->logLevel > VEGA_BQB_LOG_FULL,
		  "Valid Logging level -1:none 0:error 1:warning 2:info 3:debug 4:full");
	CHECK(param->device < 0 || param->device > 7,
		  "Valid device number 0:device1 1:device2 2:device3 3:device4 4:device5 5:device6 6:device7 7:device8");
	CHECK(param->channel < 0 || param->channel > 11,
		  "Valid channel number 0:channel1 1:channel2 2:channel3 ...11:channel12");

	CHECK(param->inputMode < 0 || param->inputMode > 1,
		  "Invalid input mode. value must be 0=file in, 1=stream interface");
	CHECK(param->outputPath < 0 || param->outputPath > 1,
		  "Invalid output path. value must be 0=file out, 1=video interface");
	CHECK(param->dbgLevel < 0 || param->dbgLevel > 3, "Invalid debug level for VEGA API. value must be 0, 1, 2, 3");
	return check_failed;
}

void vega_bqb_mpeg_print_params(const vega_bqb_mpeg_param *param)
{
	vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Device / Channel / Input mode       : %d / %d / %d\n", param->device,
			   param->channel, param->inputMode);
	vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Internal bit depth                  : %d\n", param->internalBitDepth);
	vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Dimension (%d,%d) (%d,%d)\n", param->sourceWidth, param->sourceHeight,
			   param->inputWidth, param->inputHeight);

	if (param->interlaceMode)
		vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Interlaced field inputs             : %s\n",
				   vega_bqb_mpeg_interlace_names[param->interlaceMode]);

	if (param->keyframeMax != INT_MAX || param->scenecutThreshold)
		vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Keyframe max / scenecut             : %d / enabled\n",
				   param->keyframeMax);
	else
		vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Keyframe max / scenecut             : %d / disabled\n",
				   param->keyframeMax);

	vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "cpb delay                           : %.1f s\n", param->cpbDelay);

	switch (param->rc.rateControlMode)
	{
	case VEGA_BQB_RC_CBR:
		vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Rate Control / bitrate              : CBR / %d kbps\n",
				   param->rc.bitrate);
		break;

	case VEGA_BQB_RC_CAPPED_VBR:
		vegaff_log(param, VEGA_BQB_LOG_VERBOSE,
				   "Rate Control / max / ave / min      : VBR / %d kbps / %d kbps / %d kbps\n", param->rc.vbrMaxBitrate,
				   param->rc.vbrAveBitrate, param->rc.vbrMinBitrate);
		break;
	}
}

void vega_bqb_mpeg_dec_print_params(const vega_bqb_mpeg_dec_param *param)
{
	vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Device / Channel                    : %d / %d\n", param->device,
			   param->channel);
	vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Input mode / Output path            : %s / %s\n",
			   param->inputMode ? "stream interface" : "file in", param->outputPath ? "video interface" : "file out");
}
