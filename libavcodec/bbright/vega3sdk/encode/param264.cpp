/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <cinttypes>
#include <string>
#include "../vegaff.h"
#include "../common/common.h"
#include "../common/constants.h"
#include "param264.h"

extern "C" {
#include "libavutil/log.h"
#include "libavutil/avstring.h"
}

#ifdef _MSC_VER
#pragma warning(disable : 4996) // POSIX functions are just fine, thanks
#endif

/* this table is kept internal to avoid confusion, since log level indices start at -1 */
static const char *const logLevelNames[] = {"none", "error", "warning", "info", "frame", "debug", "full", 0};

vega_bqb_avc_param *vega_bqb_avc_param_alloc()
{
	return vegaff_zallocT<vega_bqb_avc_param>();
}

vega_bqb_avc_dec_param *vega_bqb_avc_dec_param_alloc()
{
	return vegaff_zallocT<vega_bqb_avc_dec_param>();
}

void vega_bqb_avc_param_free(void *p)
{
	vegaff_free(p);
}

void vega_bqb_avc_dec_param_free(vega_bqb_avc_dec_param *p)
{
	vegaff_free(p);
}

void vega_bqb_avc_param_default(vega_bqb_avc_param *param)
{
	vegaff_zeroT(param);
	/* Applying default values to all elements in the param structure */
	param->logLevel = VEGA_BQB_LOG_VERBOSE;

	/* Video properties */
	param->internalBitDepth = 8;
	param->internalCsp = VEGA_BQB_CSP_NV12;
	param->profile = 2;
	param->levelIdc = 40;
	param->vui.aspectRatioIdc = 1;
	param->vui.sarWidth = 0;
	param->vui.sarHeight = 0;
	param->vui.bEnableOverscanInfoPresentFlag = 0;
	param->vui.bEnableOverscanAppropriateFlag = 0;
	param->vui.bEnableVideoSignalTypePresentFlag = 0;
	param->vui.videoFormat = 0;
	param->vui.bEnableVideoFullRangeFlag = 0;
	param->vui.bEnableColorDescriptionPresentFlag = 0;
	param->vui.colorPrimaries = 0;
	param->vui.transferCharacteristics = 0;
	param->vui.matrixCoeffs = 0;
	param->vui.bEnableChromaLocInfoPresentFlag = 0;
	param->vui.chromaSampleLocTypeTopField = 0;
	param->vui.chromaSampleLocTypeBottomField = 0;
	param->vui.bEnableDefaultDisplayWindowFlag = 0;
	param->vui.defDispWinLeftOffset = 0;
	param->vui.defDispWinRightOffset = 0;
	param->vui.defDispWinTopOffset = 0;
	param->vui.defDispWinBottomOffset = 0;
	param->inputWidth = 0;
	param->inputHeight = 0;
	/* Encoder parameters */
	param->gopType = 2;
	param->keyframeMax = 24;
	param->bOpenGOP = 1;
	param->bframes = 2;
	param->scenecutThreshold = 0;
	param->interlaceMode = 0;

	param->bEnableLoopFilter = 1;

	param->bCabac = 1;

	param->rc.rateControlMode = VEGA_BQB_RC_CBR;
	param->rc.fillerrate = 0;
	param->rc.bitrate = 0;
	param->rc.vbrMaxBitrate = 0;
	param->rc.vbrAveBitrate = 0;
	param->rc.vbrMinBitrate = 0;
	param->rc.vbrFillerBitrate = 0;
	param->rc.bStrictCbr = 0;

	param->cpbDelay = 0;

	param->bEnableWeightedPred = 0;
	param->bEnableWeightedBiPred = 0;

	/* VENC */
	param->device = 0;
	param->channel = 0;
	param->inputMode = 0;
	param->dbgLevel = 0;
	param->iDRDisplayOrderFirst = 1;
	param->encodeMode = 0; // auto mode
}

void vega_bqb_avc_dec_param_default(vega_bqb_avc_dec_param *param)
{
	vegaff_zeroT(param);
	/* Applying default values to all elements in the param structure */
	param->logLevel = VEGA_BQB_LOG_VERBOSE;

	/* VDEC */
	param->device = 0;
	param->channel = 0;
	param->inputMode = 0;
	param->outputPath = 0;
	param->outputFormat = 0;
	param->dbgLevel = 0;
}

int vega_bqb_avc_param_default_preset(vega_bqb_avc_param *param, const char *preset)
{
	vega_bqb_avc_param_default(param);

	if (preset)
	{
		char *end;
		int   i = strtol(preset, &end, 10);

		if (*end == 0 && i >= 0 &&
			i < (int)(sizeof(vega_bqb_avc_preset_names) / sizeof(*vega_bqb_avc_preset_names) - 1))
			preset = vega_bqb_avc_preset_names[i];

		if (!av_strcasecmp(preset, "fast"))
		{
			param->bframes = 0;
			param->bCabac = 0;
			param->scenecutThreshold = 0;
			param->bEnableLoopFilter = 0;
		}
		else if (!av_strcasecmp(preset, "medium"))
		{
			/* default */
		}
		else if (!av_strcasecmp(preset, "slow"))
		{
			param->bframes = 2;
		}
		else
			return -1;
	}

	return 0;
}

void vega_bqb_avc_param_default_bitrate(vega_bqb_avc_param *p)
{
	if ((p->sourceWidth == 4096 || p->sourceWidth == 3840 || p->sourceWidth == 2880 || p->sourceWidth == 2560) &&
		(p->sourceHeight == 2160 || p->sourceHeight == 1440))
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 32000;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 32000;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if ((p->sourceWidth == 2048 || p->sourceWidth == 1920) && p->sourceHeight == 1080)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 8000;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 15000;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 1280 && p->sourceHeight == 720)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 4000;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 4000;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 720 && (p->sourceHeight == 576 || p->sourceHeight == 480))
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 1500;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 1500;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 1920 && p->sourceHeight == 540 && p->interlaceMode)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 8000;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 8000;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 720 && (p->sourceHeight == 288 || p->sourceHeight == 240) && p->interlaceMode)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 1500;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 1500;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 416 && p->sourceHeight == 240)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 500;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 500;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else if (p->sourceWidth == 352 && p->sourceHeight == 288)
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 500;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 500;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
	else
	{
		if (p->rc.rateControlMode == VEGA_BQB_RC_CBR)
		{
			if (!p->rc.bitrate)
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate = 1000;
			else
				p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate = p->rc.bitrate;
		}
		else if (p->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR)
		{
			if (p->rc.vbrMaxBitrate && p->rc.vbrAveBitrate && p->rc.vbrMinBitrate)
			{
				p->rc.bitrate = p->rc.vbrMaxBitrate;
			}
			else if (!p->rc.vbrMaxBitrate && !p->rc.vbrAveBitrate && !p->rc.vbrMinBitrate)
			{
				if (!p->rc.bitrate)
				{
					p->rc.bitrate = p->rc.vbrMaxBitrate = 1000;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
				else
				{
					p->rc.vbrMaxBitrate = p->rc.bitrate;
					p->rc.vbrMinBitrate = p->rc.vbrAveBitrate = p->rc.vbrMaxBitrate / 2;
				}
			}
		}
	}
}

/* internal versions of string-to-int with additional error checking */
#define ATOI(str)    vegaff_atoi(str, bError)
#define ATOF(str)    vegaff_atof(str, bError)
#define ATOBOOL(str) (bNameWasBool = true, vegaff_atobool(str, bError))

int vega_bqb_avc_param_parse(vega_bqb_avc_param *p, const char *name, const char *value)
{
	bool bError = false;
	bool bNameWasBool = false;
	bool bValueWasNull = !value;
	char nameBuf[64];

	if (!name)
		return VEGA_BQB_PARAM_BAD_NAME;

	// skip -- prefix if provided
	if (name[0] == '-' && name[1] == '-')
		name += 2;

	// s/_/-/g
	if (strlen(name) + 1 < sizeof(nameBuf) && strchr(name, '_'))
	{
		char *c;
		strcpy(nameBuf, name);

		while ((c = strchr(nameBuf, '_')) != 0)
			*c = '-';

		name = nameBuf;
	}

	if (!strncmp(name, "no-", 3))
	{
		name += 3;
		value = !value || vegaff_atobool(value, bError) ? "false" : "true";
	}
	else if (!strncmp(name, "no", 2))
	{
		name += 2;
		value = !value || vegaff_atobool(value, bError) ? "false" : "true";
	}
	else if (!value)
		value = "true";
	else if (value[0] == '=')
		value++;

#define OPT(STR)         else if (!av_strcasecmp(name, STR))
#define OPT2(STR1, STR2) else if (!av_strcasecmp(name, STR1) || !av_strcasecmp(name, STR2))

	if (0)
		;

	OPT("fps")
	{
		if (sscanf(value, "%u/%u", &p->fpsNum, &p->fpsDenom) == 2)
			;
		else
		{
			float fps = (float)ATOF(value);

			if (fps > 0 && fps <= INT_MAX / 1000)
			{
				p->fpsNum = (int)(fps * 1000 + .5);
				p->fpsDenom = 1000;
			}
			else
			{
				p->fpsNum = ATOI(value);
				p->fpsDenom = 1;
			}
		}
	}
	OPT("profile")
	{
		if (!av_strcasecmp(value, "main"))
			p->profile = 1;
		else if (!av_strcasecmp(value, "high"))
			p->profile = 2;
		else if (!av_strcasecmp(value, "high10"))
			p->profile = 3;
		else if (!av_strcasecmp(value, "high422"))
			p->profile = 4;
		else if (!av_strcasecmp(value, "baseline"))
			p->profile = 5;
		else
			bError = true;
	}
	OPT2("level-idc", "level")
	{
		/* allow "5.1" or "51", both converted to integer 51 */
		if (ATOF(value) < 6)
			p->levelIdc = (int)(10 * ATOF(value) + .5);
		else
			p->levelIdc = ATOI(value);
	}
	OPT2("log-level", "log")
	{
		p->logLevel = ATOI(value);

		if (bError)
		{
			bError = false;
			p->logLevel = vegaff_parseName(value, logLevelNames, bError) - 1;
		}
	}
	OPT("open-gop") p->bOpenGOP = ATOI(value);
	OPT("scenecut")
	{
		p->scenecutThreshold = ATOBOOL(value);
		if (bError || p->scenecutThreshold)
		{
			bError = false;
			p->scenecutThreshold = ATOI(value);
		}
	}
	OPT("gop-type") p->gopType = ATOI(value);
	OPT("keyint")
	{
		if (strstr(value, "infinite"))
			p->keyframeMax = VEGA_BQB_KEYINT_MAX_INFINITE;
		else
			p->keyframeMax = ATOI(value);
	}
	OPT("bframes") p->bframes = ATOI(value);
	OPT("interlaced")
	{
		p->interlaceMode = ATOBOOL(value);

		if (bError || p->interlaceMode)
		{
			bError = false;
			p->interlaceMode = vegaff_parseName(value, vega_bqb_avc_interlace_names, bError);
		}
	}
	OPT("weightp") p->bEnableWeightedPred = ATOBOOL(value);
	OPT("weightb") p->bEnableWeightedBiPred = ATOBOOL(value);
	OPT2("deblock", "filter") p->bEnableLoopFilter = ATOBOOL(value);
	OPT("cabac") p->bCabac = ATOBOOL(value);
	OPT("rate-ctrl")
	{
		if (!av_strcasecmp(value, "cbr"))
			p->rc.rateControlMode = VEGA_BQB_RC_CBR;
		else if (!av_strcasecmp(value, "vbr"))
			p->rc.rateControlMode = VEGA_BQB_RC_CAPPED_VBR;
	}
	OPT("fillerrate") p->rc.fillerrate = ATOI(value);
	OPT("bitrate")
	{
		int is_err = 0;

		p->rc.bitrate = vegaff_str_to_weighted_size(value, &is_err);
		if (is_err)
			bError |= 1;
	}
	OPT("vbr")
	{
		int      is_err = 0;
		char *   saveptr, *token, *line;
		uint32_t tvalue[3] = {0, 0, 0};
		int      i;
		line = (char *)value;
		/* get V-MAX, V-AVG, and V-MIN */
		for (i = 0; i < 3; i++, line = NULL)
		{
			token = STR_TOK(line, ",", &saveptr);
			if (token)
			{
				tvalue[i] = vegaff_str_to_weighted_size(token, &is_err);
				if (is_err)
					bError |= 1;
			}
			else
			{
				bError |= 1;
				break;
			}
		}
		p->rc.vbrMaxBitrate = tvalue[0];
		p->rc.vbrAveBitrate = tvalue[1];
		p->rc.vbrMinBitrate = tvalue[2];
	}
	OPT("input-depth") p->internalBitDepth = ATOI(value);
	OPT("input-res") bError |= sscanf(value, "%dx%d", &p->sourceWidth, &p->sourceHeight) != 2;
	OPT("input-csp") p->internalCsp = vegaff_parseName(value, vega_bqb_avc_source_csp_names, bError);
	OPT("sar")
	{
		p->vui.aspectRatioIdc = vegaff_parseName(value, vega_bqb_avc_sar_names, bError);

		if (bError)
		{
			p->vui.aspectRatioIdc = VEGA_BQB_EXTENDED_SAR;
			bError = sscanf(value, "%d,%d", &p->vui.sarWidth, &p->vui.sarHeight) != 2;
		}
	}
	OPT("overscan")
	{
		if (!av_strcasecmp(value, "show"))
			p->vui.bEnableOverscanInfoPresentFlag = 1;
		else if (!av_strcasecmp(value, "crop"))
		{
			p->vui.bEnableOverscanInfoPresentFlag = 1;
			p->vui.bEnableOverscanAppropriateFlag = 1;
		}
		else if (!av_strcasecmp(value, "undef"))
			p->vui.bEnableOverscanInfoPresentFlag = 0;
		else
			bError = true;
	}
	OPT("videoformat")
	{
		p->vui.bEnableVideoSignalTypePresentFlag = 1;
		p->vui.videoFormat = vegaff_parseName(value, vega_bqb_avc_video_format_names, bError);
	}
	OPT("range")
	{
		p->vui.bEnableVideoSignalTypePresentFlag = 1;
		p->vui.bEnableVideoFullRangeFlag = vegaff_parseName(value, vega_bqb_avc_fullrange_names, bError);
	}
	OPT("colorprim")
	{
		p->vui.bEnableVideoSignalTypePresentFlag = 1;
		p->vui.bEnableColorDescriptionPresentFlag = 1;
		p->vui.colorPrimaries = vegaff_parseName(value, vega_bqb_avc_colorprim_names, bError);
	}
	OPT("transfer")
	{
		p->vui.bEnableVideoSignalTypePresentFlag = 1;
		p->vui.bEnableColorDescriptionPresentFlag = 1;
		p->vui.transferCharacteristics = vegaff_parseName(value, vega_bqb_avc_transfer_names, bError);
	}
	OPT("colormatrix")
	{
		p->vui.bEnableVideoSignalTypePresentFlag = 1;
		p->vui.bEnableColorDescriptionPresentFlag = 1;
		p->vui.matrixCoeffs = vegaff_parseName(value, vega_bqb_avc_colmatrix_names, bError);
	}
	OPT("chromaloc")
	{
		p->vui.bEnableChromaLocInfoPresentFlag = 1;
		p->vui.chromaSampleLocTypeTopField = ATOI(value);
		p->vui.chromaSampleLocTypeBottomField = p->vui.chromaSampleLocTypeTopField;
	}
	OPT("crop-rect")
	{
		p->vui.bEnableDefaultDisplayWindowFlag = 1;
		bError |= sscanf(value, "%d,%d,%d,%d", &p->vui.defDispWinLeftOffset, &p->vui.defDispWinTopOffset,
						 &p->vui.defDispWinRightOffset, &p->vui.defDispWinBottomOffset) != 4;
	}
	OPT("device") p->device = ATOI(value);
	OPT("channel") p->channel = ATOI(value);
	OPT("input-mode") p->inputMode = ATOI(value);
	OPT("dbg-level") p->dbgLevel = ATOI(value);
	OPT("cpb-delay") p->cpbDelay = ATOF(value);
	OPT("robust-mode") p->robustMode = ATOI(value);
	OPT("idr-display-order-first") p->iDRDisplayOrderFirst = ATOI(value);
	OPT("encode-mode")
	{
		p->encodeMode = ATOI(value);
		if (bError)
		{
			bError = false;
			p->encodeMode = vegaff_parseName(value, vega_bqb_encode_mode, bError);
		}
	}
	else return VEGA_BQB_PARAM_BAD_NAME;

#undef OPT
#undef OPT2
	bError |= bValueWasNull && !bNameWasBool;
	return bError ? VEGA_BQB_PARAM_BAD_VALUE : 0;
}

int vega_bqb_avc_dec_param_parse(vega_bqb_avc_dec_param *p, const char *name, const char *value)
{
	bool bError = false;
	bool bNameWasBool = false;
	bool bValueWasNull = !value;
	char nameBuf[64];

	if (!name)
		return VEGA_BQB_PARAM_BAD_NAME;

	// skip -- prefix if provided
	if (name[0] == '-' && name[1] == '-')
		name += 2;

	// s/_/-/g
	if (strlen(name) + 1 < sizeof(nameBuf) && strchr(name, '_'))
	{
		char *c;
		strcpy(nameBuf, name);

		while ((c = strchr(nameBuf, '_')) != 0)
			*c = '-';

		name = nameBuf;
	}

	if (!strncmp(name, "no-", 3))
	{
		name += 3;
		value = !value || vegaff_atobool(value, bError) ? "false" : "true";
	}
	else if (!strncmp(name, "no", 2))
	{
		name += 2;
		value = !value || vegaff_atobool(value, bError) ? "false" : "true";
	}
	else if (!value)
		value = "true";
	else if (value[0] == '=')
		value++;

#define OPT(STR)         else if (!av_strcasecmp(name, STR))
#define OPT2(STR1, STR2) else if (!av_strcasecmp(name, STR1) || !av_strcasecmp(name, STR2))

	if (0)
		;

	OPT2("log-level", "log")
	{
		p->logLevel = ATOI(value);

		if (bError)
		{
			bError = false;
			p->logLevel = vegaff_parseName(value, logLevelNames, bError) - 1;
		}
	}
	OPT("device") p->device = ATOI(value);
	OPT("channel")
	{
		p->channel = ATOI(value);
		if (p->channel != 0 && p->channel != 2)
			bError = true;
	}
	OPT("input-mode") p->inputMode = ATOI(value);
	OPT("output-path")
	{
		p->outputPath = ATOI(value);
		// p->outputFormat = (p->outputPath) ? 0 : 1;
		p->outputFormat = 0;
	}
	OPT("output-format")
	{
		p->outputFormat = ATOI(value); /* 0=vraw, 1=yuv422p10 , 2=SNI_V210 , 3=NV16*/
	}
	OPT("dbg-level") p->dbgLevel = ATOI(value);
	else return VEGA_BQB_PARAM_BAD_NAME;

#undef OPT
#undef OPT2
	bError |= bValueWasNull && !bNameWasBool;
	return bError ? VEGA_BQB_PARAM_BAD_VALUE : 0;
}

template <typename T> static inline int _confirm(T *param, bool bflag, const char *message)
{
	if (!bflag)
		return 0;

	vegaff_log(param, VEGA_BQB_LOG_ERROR, "%s\n", message);
	return 1;
}

int vega_bqb_avc_check_params(const vega_bqb_avc_param *param)
{
#define CHECK(expr, msg) check_failed |= _confirm(param, expr, msg)
	int check_failed = 0; /* abort if there is a fatal configuration problem */

	if (check_failed == 1)
		return check_failed;

	/* These checks might be temporary */
	CHECK(param->internalBitDepth != 10 && param->internalBitDepth != 8,
		  "only 10bit and 8bit internal depth supported");
	CHECK(param->fpsNum == 0 || param->fpsDenom == 0, "Frame rate numerator and denominator must be specified");
	CHECK(param->interlaceMode < 0 || param->interlaceMode > 1,
		  "Interlace mode must be 0 (progressive) or 1 (interlace)");
	CHECK(param->levelIdc != 10 && param->levelIdc != 20 && param->levelIdc != 21 && param->levelIdc != 30 &&
			  param->levelIdc != 31 && param->levelIdc != 32 && param->levelIdc != 40 && param->levelIdc != 41 &&
			  param->levelIdc != 42 && param->levelIdc != 50 && param->levelIdc != 51 && param->levelIdc != 52,
		  "Level must be 10, 20, 21, 30, 31, 32, 40, 41, 42, 50, 51, 52");
	CHECK(param->internalCsp < VEGA_BQB_CSP_I420 || VEGA_BQB_CSP_NV16 < param->internalCsp,
		  "Color space must be i420, i422, nv12 or nv16");
#if 0	
	CHECK(param->sourceWidth != 4096 && param->sourceWidth != 3840 && param->sourceWidth != 2880 && 
		  param->sourceWidth != 2560 && param->sourceWidth != 2160 && param->sourceWidth != 2048 && 
		  param->sourceWidth != 1920 && param->sourceWidth != 1280 && param->sourceWidth != 960  && 
		  param->sourceWidth != 864 && param->sourceWidth != 720 && param->sourceWidth != 480 && 
		  param->sourceWidth != 416 && param->sourceWidth != 384 && param->sourceWidth != 352 && 
		  param->sourceWidth != 320 &&  param->sourceWidth != 176,
	      "Picture width must be 4096, 3840, 2880, 2560, 2160, 2048, 1920, 1280, 960, 864, 720, 480, 416, 384, 352, 320 or 176");
	CHECK(param->sourceHeight != 2160 && param->sourceHeight != 1620 && param->sourceHeight != 1440 && 
		  param->sourceHeight != 1080 && param->sourceHeight != 800 && param->sourceHeight != 720 && 
		  param->sourceHeight != 576 && param->sourceHeight != 532 && param->sourceHeight != 480 && 
		  param->sourceHeight != 540 && param->sourceHeight != 400 && param->sourceHeight != 360 && 
		  param->sourceHeight != 240 && param->sourceHeight != 288 && param->sourceHeight != 270 && 
		  param->sourceHeight != 200 && param->sourceHeight != 180 && param->sourceHeight != 160 && 
		  param->sourceHeight != 144,
	      "Picture height must be 2160, 1620, 1440, 1080, 800, 720, 576, 540, 532, 480, 400, 360, 270, 200, 180, 160, 240, 288 or 144");
#endif
	CHECK(param->rc.rateControlMode > VEGA_BQB_RC_CAPPED_VBR || param->rc.rateControlMode < VEGA_BQB_RC_CBR,
		  "Rate control mode is out of range");
	CHECK(param->bframes < 0, "bframes count should be greater than zero");
	// CHECK(param->bframes > 2, "max consecutive bframes count must be 2 or smaller");
	CHECK((param->vui.aspectRatioIdc < 0 || param->vui.aspectRatioIdc > 16) &&
			  param->vui.aspectRatioIdc != VEGA_BQB_EXTENDED_SAR,
		  "Sample Aspect Ratio must be 0-16 or 255");
	CHECK(param->vui.aspectRatioIdc == VEGA_BQB_EXTENDED_SAR && param->vui.sarWidth <= 0,
		  "Sample Aspect Ratio width must be greater than 0");
	CHECK(param->vui.aspectRatioIdc == VEGA_BQB_EXTENDED_SAR && param->vui.sarHeight <= 0,
		  "Sample Aspect Ratio height must be greater than 0");
	CHECK(param->vui.videoFormat < 0 || param->vui.videoFormat > 5, "Video Format must be component,"
																	" pal, ntsc, secam, mac or undef");
	CHECK(param->vui.colorPrimaries < 0 || param->vui.colorPrimaries > 9 || param->vui.colorPrimaries == 3,
		  "Color Primaries must be undef, bt709, bt470m,"
		  " bt470bg, smpte170m, smpte240m, film or bt2020");
	CHECK(param->vui.transferCharacteristics < 0 || param->vui.transferCharacteristics > 18 ||
			  param->vui.transferCharacteristics == 3,
		  "Transfer Characteristics must be undef, bt709, bt470m, bt470bg,"
		  " smpte170m, smpte240m, linear, log100, log316, iec61966-2-4, bt1361e,"
		  " iec61966-2-1, bt2020-10, bt2020-12, smpte-st-2084, smpte-st-428 or arib-std-b67");
	CHECK(param->vui.matrixCoeffs < 0 || param->vui.matrixCoeffs > 10 || param->vui.matrixCoeffs == 3,
		  "Matrix Coefficients must be undef, bt709, fcc, bt470bg, smpte170m,"
		  " smpte240m, GBR, YCgCo, bt2020nc or bt2020c");
	CHECK(param->vui.chromaSampleLocTypeTopField < 0 || param->vui.chromaSampleLocTypeTopField > 5,
		  "Chroma Sample Location Type Top Field must be 0-5");
	CHECK(param->vui.chromaSampleLocTypeBottomField < 0 || param->vui.chromaSampleLocTypeBottomField > 5,
		  "Chroma Sample Location Type Bottom Field must be 0-5");
	CHECK(param->vui.defDispWinLeftOffset < 0, "Default Display Window Left Offset must be 0 or greater");
	CHECK(param->vui.defDispWinRightOffset < 0, "Default Display Window Right Offset must be 0 or greater");
	CHECK(param->vui.defDispWinTopOffset < 0, "Default Display Window Top Offset must be 0 or greater");
	CHECK(param->vui.defDispWinBottomOffset < 0, "Default Display Window Bottom Offset must be 0 or greater");
	CHECK(param->logLevel < -1 || param->logLevel > VEGA_BQB_LOG_FULL,
		  "Valid Logging level -1:none 0:error 1:warning 2:info 3:debug 4:full");
	CHECK(param->scenecutThreshold < 0 || param->scenecutThreshold > 1,
		  "scenecutThreshold must be 0:not insert I-picture or 1:insert I-picture");
	CHECK(param->bOpenGOP < 0 || param->bOpenGOP > 3, "Valid open GOP value 0:closed GOP 1:open GOP 2:every two IRAP "
													  "frames has one as IDR 3:every 100 IRAP frames has one as IDR");
	CHECK(param->keyframeMax > 240, "GOP size should be smaller than 240");
	CHECK((param->gopType != 0) && (param->bframes != 0) && (param->keyframeMax % (param->bframes + 1) != 0),
		  "Invalid GOP size. value must be an integer multiple of reference frame number");
	CHECK(param->rc.bitrate <= 0, "Target bitrate cannot be less than or equal to zero");
	CHECK(param->rc.bitrate > VEGA_MAX_BITRATE, "Target bitrate cannot be greater than 600000");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMaxBitrate <= 0),
		  "VBR max bitrate cannot be less than or equal to zero");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMaxBitrate > VEGA_MAX_BITRATE),
		  "VBR max bitrate cannot be greater than 600000");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrAveBitrate <= 0),
		  "VBR average bitrate cannot be less than or equal to zero");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrAveBitrate > VEGA_MAX_BITRATE),
		  "VBR average bitrate cannot be greater than 600000");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate <= 0),
		  "VBR min bitrate cannot be less than or equal to zero");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate > VEGA_MAX_BITRATE),
		  "VBR min bitrate cannot be greater than 600000");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMaxBitrate > param->rc.bitrate),
		  "VBR max bitrate can be less then or equal to target bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrAveBitrate > param->rc.bitrate),
		  "VBR average bitrate can be less than or equal to target bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrAveBitrate > param->rc.vbrMaxBitrate),
		  "VBR average bitrate can be less than or equal to VBR max bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate > param->rc.bitrate),
		  "VBR min bitrate can be less than or equal to target bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate > param->rc.vbrMaxBitrate),
		  "VBR min bitrate can be less than or equal to VBR max bitrate");
	CHECK((param->rc.rateControlMode == VEGA_BQB_RC_CAPPED_VBR) && (param->rc.vbrMinBitrate > param->rc.vbrAveBitrate),
		  "VBR min bitrate can be less than or equal to VBR average bitrate");
	CHECK(param->rc.bStrictCbr && (param->rc.bitrate <= 0),
		  "Strict-cbr cannot be applied without specifying target bitrate");
	CHECK(param->device < 0 || param->device > 7,
		  "Valid device number 0:device1 1:device2 2:device3 3:device4 4:device5 5:device6 6:device7 7:device8");
	CHECK(param->channel < 0 || param->channel > 15,
		  "Valid channel number 0:channel1 1:channel2 2:channel3 ...15:channel16");
	CHECK(param->channel != 0 && ((param->sourceWidth == 4096 && param->sourceHeight == 2160) ||
								  (param->sourceWidth == 3840 && param->sourceHeight == 2160)),
		  "Single channel encoding (VEGA_BQB_AVC_CHN_1) for 4K");
	CHECK(param->inputMode < 0 || param->inputMode > 2, "Invalid input mode. value must be 0, 1 or 2");
	CHECK(param->gopType < 0 || param->gopType > 2, "Invalid gop type. value must be 0, 1, 2");
	CHECK(param->dbgLevel < 0 || param->dbgLevel > 3, "Invalid debug level for VEGA API. value must be 0, 1, 2, 3");
	CHECK(param->cpbDelay < 0, "cpb delay can not be less than zero");
	CHECK(param->encodeMode < 0 || param->encodeMode > 7,
		  "encode mode can be 0 ~ 7 (auto,1ch-4k,4ch-1080p,8ch-720p,8ch-mixing,16ch-sd,16ch-seq,16ch-720p)");
	//	check_failed |= _vega_bqb_check_res(param);
	return check_failed;
}

int vega_bqb_avc_dec_check_params(const vega_bqb_avc_dec_param *param)
{
#define CHECK(expr, msg) check_failed |= _confirm(param, expr, msg)
	int check_failed = 0; /* abort if there is a fatal configuration problem */

	if (check_failed == 1)
		return check_failed;

	/* These checks might be temporary */
	CHECK(param->logLevel < -1 || param->logLevel > VEGA_BQB_LOG_FULL,
		  "Valid Logging level -1:none 0:error 1:warning 2:info 3:debug 4:full");
	CHECK(param->device < 0 || param->device > 7,
		  "Valid device number 0:device1 1:device2 2:device3 3:device4 4:device5 5:device6 6:device7 7:device8");
	CHECK(param->channel < 0 || param->channel > 15,
		  "Valid channel number 0:channel1 1:channel2 2:channel3 ...15:channel16");
	CHECK(param->channel != 0 && ((param->sourceWidth == 4096 && param->sourceHeight == 2160) ||
								  (param->sourceWidth == 3840 && param->sourceHeight == 2160)),
		  "Single channel encoding (VEGA_BQB_AVC_CHN_1) for 4K");
	CHECK(param->inputMode < 0 || param->inputMode > 1,
		  "Invalid input mode. value must be 0=file in, 1=stream interface");
	CHECK(param->outputPath < 0 || param->outputPath > 1,
		  "Invalid output path. value must be 0=file out, 1=video interface");
	CHECK(param->dbgLevel < 0 || param->dbgLevel > 3, "Invalid debug level for VEGA API. value must be 0, 1, 2, 3");
	return check_failed;
}

static void appendtool(const vega_bqb_avc_param *param, char *buf, size_t size, const char *toolstr)
{
	static const int overhead = (int)strlen("vega_bqb_avc [info]: tools: ");

	if (strlen(buf) + strlen(toolstr) + overhead >= size)
	{
		vegaff_log(param, VEGA_BQB_LOG_INFO, "tools:%s\n", buf);
		sprintf(buf, " %s", toolstr);
	}
	else
	{
		strcat(buf, " ");
		strcat(buf, toolstr);
	}
}

void vega_bqb_avc_print_params(const vega_bqb_avc_param *param)
{
	vegaff_log(param, VEGA_BQB_LOG_INFO, "Device / Channel / Input mode       : %d / %d / %d\n", param->device, param->channel, param->inputMode);
	vegaff_log(param, VEGA_BQB_LOG_INFO, "Internal bit depth                  : %d\n", param->internalBitDepth);
	vegaff_log(param, VEGA_BQB_LOG_INFO, "Dimension (%d,%d) (%d,%d)\n", param->sourceWidth, param->sourceHeight, param->inputWidth, param->inputHeight);
	if (param->interlaceMode)
		vegaff_log(param, VEGA_BQB_LOG_INFO, "Interlaced field inputs             : %s\n", vega_bqb_avc_interlace_names[param->interlaceMode]);
	if (param->keyframeMax != INT_MAX)
		vegaff_log(param, VEGA_BQB_LOG_INFO, "Keyframe max                        : %d\n", param->keyframeMax);
	vegaff_log(param, VEGA_BQB_LOG_INFO, "scenecut                            : %d\n", param->scenecutThreshold);
	vegaff_log(param, VEGA_BQB_LOG_INFO, "open GOP                            : %d\n", param->bOpenGOP);
	vegaff_log(param, VEGA_BQB_LOG_INFO, "bframes                             : %d\n", param->bframes);
	vegaff_log(param, VEGA_BQB_LOG_INFO, "weightp / weightb                   : %d / %d\n", param->bEnableWeightedPred, param->bEnableWeightedBiPred);
	vegaff_log(param, VEGA_BQB_LOG_INFO, "cpb delay                           : %.1f s\n", param->cpbDelay);

	switch (param->rc.rateControlMode)
	{
	case VEGA_BQB_RC_CBR:
		vegaff_log(param, VEGA_BQB_LOG_INFO, "Rate Control / bitrate              : CBR / %d kbps\n", param->rc.bitrate);
		break;

	case VEGA_BQB_RC_CAPPED_VBR:
		vegaff_log(param, VEGA_BQB_LOG_INFO,
				   "Rate Control / max / ave / min      : VBR / %d kbps / %d kbps / %d kbps\n", param->rc.vbrMaxBitrate, param->rc.vbrAveBitrate, param->rc.vbrMinBitrate);
		break;
	}

	char buf[80] = {0};
#define TOOLOPT(FLAG, STR)                                                                                             \
	if (FLAG)                                                                                                          \
		appendtool(param, buf, sizeof(buf), STR);
	if (param->bEnableLoopFilter)
	{
		TOOLOPT(param->bEnableLoopFilter, "deblock");
	}

	vegaff_log(param, VEGA_BQB_LOG_INFO, "tools:%s\n", buf);
}

void vega_bqb_avc_dec_print_params(const vega_bqb_avc_dec_param *param)
{
	vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Device / Channel                    : %d / %d\n", param->device,
			   param->channel);
	vegaff_log(param, VEGA_BQB_LOG_VERBOSE, "Input mode / Output path            : %s / %s\n",
			   param->inputMode ? "stream interface" : "file in", param->outputPath ? "video interface" : "file out");
}
