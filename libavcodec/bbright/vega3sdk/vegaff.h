﻿/*
 *
 * Copyright (C) 2015 Advantech Co., Ltd. - http://www.advantech.com.tw/
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Advantech Co., Ltd. nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef VEGA_BQB_H
#define VEGA_BQB_H

#include <stdint.h>
#include <stddef.h>
#ifndef __cplusplus
#include <stdbool.h>
#endif

#ifdef __cplusplus
extern "C" {
#endif

#define VEGA_BQB_API

/* VEGA encoder */
#define NUM_CROPPING_EDGE 4
#define NUM_CODING_PARAM  2

typedef enum
{
	NAL_UNIT_CODED_SLICE_TRAIL_N = 0,
	NAL_UNIT_CODED_SLICE_TRAIL_R,

	NAL_UNIT_CODED_SLICE_TSA_N,
	NAL_UNIT_CODED_SLICE_TSA_R,

	NAL_UNIT_CODED_SLICE_STSA_N,
	NAL_UNIT_CODED_SLICE_STSA_R,

	NAL_UNIT_CODED_SLICE_RADL_N,
	NAL_UNIT_CODED_SLICE_RADL_R,

	NAL_UNIT_CODED_SLICE_RASL_N,
	NAL_UNIT_CODED_SLICE_RASL_R,

	NAL_UNIT_RESERVED_VCL_N10,
	NAL_UNIT_RESERVED_VCL_R11,
	NAL_UNIT_RESERVED_VCL_N12,
	NAL_UNIT_RESERVED_VCL_R13,
	NAL_UNIT_RESERVED_VCL_N14,
	NAL_UNIT_RESERVED_VCL_R15,

	NAL_UNIT_CODED_SLICE_BLA_W_LP = 16,
	NAL_UNIT_CODED_SLICE_BLA_W_RADL,
	NAL_UNIT_CODED_SLICE_BLA_N_LP,
	NAL_UNIT_CODED_SLICE_IDR_W_RADL,
	NAL_UNIT_CODED_SLICE_IDR_N_LP,
	NAL_UNIT_CODED_SLICE_CRA,
	NAL_UNIT_RESERVED_IRAP_VCL22,
	NAL_UNIT_RESERVED_IRAP_VCL23,

	NAL_UNIT_RESERVED_VCL24,
	NAL_UNIT_RESERVED_VCL25,
	NAL_UNIT_RESERVED_VCL26,
	NAL_UNIT_RESERVED_VCL27,
	NAL_UNIT_RESERVED_VCL28,
	NAL_UNIT_RESERVED_VCL29,
	NAL_UNIT_RESERVED_VCL30,
	NAL_UNIT_RESERVED_VCL31,

	NAL_UNIT_VPS = 32,
	NAL_UNIT_SPS,
	NAL_UNIT_PPS,
	NAL_UNIT_ACCESS_UNIT_DELIMITER,
	NAL_UNIT_EOS,
	NAL_UNIT_EOB,
	NAL_UNIT_FILLER_DATA,
	NAL_UNIT_PREFIX_SEI,
	NAL_UNIT_SUFFIX_SEI,

	NAL_UNIT_RESERVED_NVCL41,
	NAL_UNIT_RESERVED_NVCL42,
	NAL_UNIT_RESERVED_NVCL43,
	NAL_UNIT_RESERVED_NVCL44,
	NAL_UNIT_RESERVED_NVCL45,
	NAL_UNIT_RESERVED_NVCL46,
	NAL_UNIT_RESERVED_NVCL47,
	NAL_UNIT_UNSPECIFIED_48,
	NAL_UNIT_UNSPECIFIED_49,
	NAL_UNIT_UNSPECIFIED_50,
	NAL_UNIT_UNSPECIFIED_51,
	NAL_UNIT_UNSPECIFIED_52,
	NAL_UNIT_UNSPECIFIED_53,
	NAL_UNIT_UNSPECIFIED_54,
	NAL_UNIT_UNSPECIFIED_55,
	NAL_UNIT_UNSPECIFIED_56,
	NAL_UNIT_UNSPECIFIED_57,
	NAL_UNIT_UNSPECIFIED_58,
	NAL_UNIT_UNSPECIFIED_59,
	NAL_UNIT_UNSPECIFIED_60,
	NAL_UNIT_UNSPECIFIED_61,
	NAL_UNIT_UNSPECIFIED_62,
	NAL_UNIT_UNSPECIFIED_63,
	NAL_UNIT_INVALID = 64,
} HEVCNalUnitType;

typedef enum
{
	NAL_SLICE_ = 1,
	NAL_SLICE_DPA_ = 2,
	NAL_SLICE_DPB_ = 3,
	NAL_SLICE_DPC_ = 4,
	NAL_SLICE_IDR_ = 5, /* ref_idc != 0 */
	NAL_SEI_ = 6,       /* ref_idc == 0 */
	NAL_SPS_ = 7,
	NAL_PPS_ = 8,
	NAL_AUD_ = 9,
	NAL_END_SEQUENCE_ = 10,
	NAL_END_STREAM_ = 11,
	NAL_FILLER_DATA_ = 12,
	NAL_SPS_EXT_ = 13,
	NAL_AUXILIARY_SLICE_ = 19,
	NAL_FF_IGNORE_ = 0xff0f001,
} AVCNalUnitType;

typedef enum
{
	VEGA_BQB_PIX_FMT_NV12 = 1,
	VEGA_BQB_PIX_FMT_NV16,
	VEGA_BQB_PIX_FMT_YUV420P,
	VEGA_BQB_PIX_FMT_YUV422P,
	VEGA_BQB_PIX_FMT_YUV420P10LE,
	VEGA_BQB_PIX_FMT_YUV422P10LE,
	VEGA_BQB_PIX_FMT_V210,
} vega_bqb_pix_fmt_t;

/* The data within the pu8Addr is already NAL-encapsulated; the type is merely
 * in the struct for easy access by the calling application.  All data returned
 * in an vegaff_nal_t, including the data in pu8Addr, is no longer valid after the
 * next call to vega_bqb_hevc_encoder_encode.  Thus it must be used or copied before
 * calling vega_bqb_hevc_encoder_encode again. */
typedef struct vegaff_nal_t
{
	uint32_t type;      /* HEVCNalUnitType */
	uint32_t u32Length; /* size in bytes */
	uint8_t *pu8Addr;
	int64_t  pts;
	int64_t  dts;
	int      sliceType;
	int      is_last_ES; // Ref in MPEG2 only
} vegaff_nal_t;

/* Used to pass pictures into the encoder, and to get picture data back out of
 * the encoder.  The input and output semantics are different */
typedef struct vegaff_picture_t
{
	/* presentation time stamp: user-specified, returned on output */
	int64_t pts;

	/* display time stamp: returned on output */
	int64_t dts;

	/* Must be specified on input pictures, the number of planes is determined
	 * by the colorSpace value */
	uint8_t *planes[3];

	/* Stride is the number of bytes between row starts */
	int stride[3];

	/* Must be specified on input pictures. vega_bqb_hevc_picture_init() will set it to
	 * the encoder's internal bit depth, but this field must describe the depth
	 * of the input pictures. Must be between 8 and 10. */
	int bitDepth;

	/* slice type */
	int sliceType;

	/* Must be specified on input pictures. It must match the vega_bqb_hevc-supported iamge
	 * format. vega_bqb_hevc_picture_init() will initialize this value. */
	int imgFormat;

	/* width */
	int width;
	/* height */
	int height;

	void * sei_data;
	size_t sei_size;

	/* only from mpeg encoder */
	uint8_t *pu8Addr;
	uint32_t u32Length;

	int is_last_pic; // indicate this is the last slice received from codec.

} vegaff_picture_t;

typedef struct vegaff_packet_t
{
	int64_t  pts;
	int64_t  dts;
	uint8_t *data;
	int      size;
	int      stream_index;
} vegaff_packet_t;

#define VEGA_BQB_TYPE_I       0x0000
#define VEGA_BQB_TYPE_P       0x0001
#define VEGA_BQB_TYPE_B       0x0002
#define IS_VEGA_BQB_TYPE_I(x) ((x) == VEGA_BQB_TYPE_I)
#define IS_VEGA_BQB_TYPE_B(x) ((x) == VEGA_BQB_TYPE_B)

/* NOTE! For this release only VEGA_BQB_HEVC_CSP_I420 and VEGA_BQB_HEVC_CSP_I422 are supported */

/* Supported internal color space types (according to semantics of chroma_format_idc) */
#define VEGA_BQB_CSP_I420  0 /* yuv 4:2:0 planar */
#define VEGA_BQB_CSP_I422  1 /* yuv 4:2:2 planar */
#define VEGA_BQB_CSP_COUNT 2 /* Number of supported internal color spaces */

/* These color spaces are supported as input pictures. The pictures will
 * be converted to the appropriate planar color spaces at ingest */
#define VEGA_BQB_CSP_NV12 2 /* yuv 4:2:0 12bpp, with one y plane and one packed u+v */
#define VEGA_BQB_CSP_NV16 3 /* yuv 4:2:2 16bpp, with one y plane and one packed u+v */
#define VEGA_BQB_CSP_MAX  4 /* end of list */

#define VEGA_BQB_EXTENDED_SAR 255 /* aspect ratio explicitly specified as width:height */

/* Analyse flags */
#define VEGA_BQB_KEYINT_MAX_INFINITE (1 << 30)

/* rate control method */
typedef enum
{
	VEGA_BQB_RC_CBR,
	VEGA_BQB_RC_CAPPED_VBR,
} VEGA_BQB_RC_METHODS;

#define vegaff_codec_param_fields                                                                                      \
	size_t objsize;                                                                                                    \
	/* Log level for vega_bqb encoder api */                                                                           \
	int logLevel;                                                                                                      \
	/* Specify the bit depth of the compress video. 8=8bits, 9=9bits, 10=10bits. Default 8 */                          \
	int internalBitDepth;                                                                                              \
	/* Specify the chroma format. 0=I420, 1=I422, 2=NV12, 3=NV16 Default 2*/                                           \
	int internalCsp;                                                                                                   \
	/* Frame rate: calculate by Num/Denom */                                                                           \
	uint32_t fpsNum;                                                                                                   \
	uint32_t fpsDenom;                                                                                                 \
	/* Horizontal size of the video to be encoded. Default 3840 */                                                     \
	int sourceWidth;                                                                                                   \
	/* Vertical size of the video to be encoded. Default 2160                                                          \
	 * If the codec mode is interlace (INTERLACE = 1) then this parameter works                                        \
	 * for frame picture.(1920x1088 -> VSIZE = 1088) */                                                                \
	int sourceHeight;                                                                                                  \
	/* Frame format of EH# 0. 0=progressive, 1=interlace Default 0 */                                                  \
	int interlaceMode;                                                                                                 \
	/* Horizontal size of the input video. Default 3840 */                                                             \
	int inputWidth;                                                                                                    \
	/* Vertical size of the input video. Default 2160                                                                  \
	 * If the codec mode is interlace (INTERLACE = 1) then this parameter works                                        \
	 * for frame picture.(1920x1088 -> VSIZE = 1088) */                                                                \
	int inputHeight;                                                                                                   \
	/* Codec HW device: 0=device1, 1=device2, 2=device3, 3=device4, 4=device5, 5=device6,                              \
	 * 6=device7, 7=device8. Default is 0 */                                                                           \
	int device;                                                                                                        \
	/* Codec HW channel: 0=channel1, 1=channel2, 2=channel3, 3=channel4. Default is 0 */                               \
	int channel;                                                                                                       \
	/* Log level for vega_bqb_xxx api */                                                                               \
	int dbgLevel;                                                                                                      \
	/* encoders: 0=file, 1=SDI/HDMI/DisplayPort input. Default is 0 */                                                 \
	/* decoders: 0=file in, 1=stream interface*/                                                                       \
	int inputMode;                                                                                                     \
	/** GOP Setting.                                                                                                   \
	 * [HEVC]                                                                                                          \
	 *	0=all intra frames                                                                                             \
	 *	1=no B frame, inter P only                                                                                     \
	 *	2=inter P and B frames                                                                                         \
	 *	3=no P frame, inter B only                                                                                     \
	 * [AVC]                                                                                                           \
	 *	0=all intra frames                                                                                             \
	 *	1=no B frame, inter P only                                                                                     \
	 *	2=inter P and B frames                                                                                         \
	 *	3=no P frame, inter B only                                                                                     \
	 * [MPEG2]                                                                                                         \
	 *	0=I only                                                                                                       \
	 *	1=IPPP                                                                                                         \
	 *  2=IBP                                                                                                          \
	 *	3=IBBP                                                                                                         \
	 **/                                                                                                               \
	int gopType;                                                                                                       \
	struct                                                                                                             \
	{                                                                                                                  \
		/* Specify the control method of the video rate control.                                                       \
		 * 0=CBR,  1=Capped VBR. Default is 0 */                                                                       \
		int rateControlMode;                                                                                           \
		/* Actual/target percentage of bitrate,to prevent bitrate underflow */                                         \
		int fillerrate;                                                                                                \
		/* Specify the video bit rate on the video ES conformance of output video stream.                              \
		 * (Unit: kbps, 1 kbps = 1000 bps) */                                                                          \
		int bitrate;                                                                                                   \
		/* Specify the maximum video bit rate for VBR.                                                                 \
		 * (Unit: kbps, 1 kbps = 1000 bps)                                                                             \
		 * (Limitations: vbrAveBitrate <= vbrMaxBitrate, vbrMaxBitrate <= bitrate) */                                  \
		int vbrMaxBitrate;                                                                                             \
		/* Specify the average video bit rate for VBR.                                                                 \
		 * (Unit: kbps, 1 kbps = 1000 bps)                                                                             \
		 * (Limiations: vbrAveBitrate <= vbrMaxBitrate, bitrate <= 2 * vbrAveBitrate)*/                                \
		int vbrAveBitrate;                                                                                             \
		/* Specify the minimum video bit rate for VBR.                                                                 \
		 * (Unit: kbps, 1 kbps = 1000 bps) */                                                                          \
		int vbrMinBitrate;                                                                                             \
		/* Specify the bit rate of FILLER data for VBR.                                                                \
		 *    (Unit: kbps, 1 kbps = 1000 bps) */                                                                       \
		int vbrFillerBitrate;                                                                                          \
		/* Enable stricter conditions to control bitrate deviance from the target bitrate                              \
		 * in CBRmode. Bit rate adherence is prioritised over quality. Default disabled. */                            \
		int bStrictCbr;                                                                                                \
	} rc;                                                                                                              \
	/* Specify the cpb delay time. (Unit: second) */                                                                   \
	double cpbDelay;                                                                                                   \
	/* Specify the length of each GOP.                                                                                 \
	 * [HEVC]  1=All IntraPicturemode. Default is 64.                                                                  \
	 * 192 is the maximum number of P or B frames.                                                                     \
	 * (Note: keyframeMax must be divisible by reference frame number, otherwise causes                                \
	 * video image broken near GOP boundary. e.g., gopType = 3 and bframes = 4,                                        \
	 * then keyframeMax must be a multiple of 4)                                                                       \
	 * [AVC] 1=All IntraPicturemode. Default is 64.                                                                    \
	 * 192 is the maximum number of P or B frames.                                                                     \
	 * (Note: keyframeMax must be divisible by reference frame number, otherwise causes                                \
	 * video image broken near GOP boundary. e.g., gopType = 3 and bframes = 4,                                        \
	 * then keyframeMax must be a multiple of 4)                                                                       \
	 * [MPEG2] 1=All IntraPicturemode. Default is 64.                                                                  \
	 * 192 is the maximum number of P or B frames.                                                                     \
	 * (Note: keyframeMax must be divisible by reference frame number, otherwise causes                                \
	 * video image broken near GOP boundary. e.g., gopType = 3 and bframes = 4,                                        \
	 * then keyframeMax must be a multiple of 4) */                                                                    \
	int keyframeMax;                                                                                                   \
	/* [HEVC/AVC] Default is 0, maximum is VEGA_BQB_HEVC_B_FRAME_MAX / VEGA_BQB_AVC_B_FRAME_MAX */                     \
	int bframes;                                                                                                       \
	/* Robustness method for SDI input port                                                                            \
	 *	0: stop encode                                                                                                  \
	 *	1: blue screen                                                                                                  \
	 */                                                                                                                \
	int robustMode;                                                                                                    \
	/* vega_bqb decoder: 0=file out, 1=video interface*/                                                               \
	int outputPath;                                                                                                    \
	/* vega_bqb decoder: 0=y+uv, 1=yuv422p10 , 2=SNI_V210 , 3=NV16*/                                                   \
	int                outputFormat;                                                                                   \
	vega_bqb_pix_fmt_t pix_fmt

typedef struct _vegaff_codec_param
{
	vegaff_codec_param_fields;
} vegaff_codec_param;

/* vega_bqb_hevc input parameters
 *
 * vega_bqb_hevc parameters structure designed referred to x265 parameters
 * For version safety you may use vega_bqb_hevc_param_alloc/free() to manage the
 * allocation of vega_bqb_hevc_param instances, and vega_bqb_hevc_param_parse() to assign values
 * by name.  By never dereferencing param fields in your own code you can treat
 * vega_bqb_hevc_param as an opaque data structure */
typedef struct vega_bqb_hevc_param
{
	vegaff_codec_param_fields;

	int chip_type; /* 0: HEVC defined for M30; 1: defined for M31(HEVC only chip) */
	/* x265: vega_bqb_hevc_param_default() will auto-detect this cpu capability bitmap. it is
	 * recommended to not change this value unless you know the cpu detection is
	 * somehow flawed on your target hardware. The asm function tables are
	 * process global, the first encoder configures them for all encoders */

	/* vega_bqb_hevc: not support */
	// int cpuid;

	/*== Parallelism Features ==*/

	/* x265: Number of concurrently encoded frames between 1 and VEGA_BQB_HEVC_MAX_FRAME_THREADS
	 * or 0 for auto-detection. By default vega_bqb_hevc will use a number of frame
	 * threads empirically determined to be optimal for your CPU core count,
	 * between 2 and 6.  Using more than one frame thread causes motion search
	 * in the down direction to be clamped but otherwise encode behavior is
	 * unaffected. With CQP rate control the output bitstream is deterministic
	 * for all values of frameNumThreads greater than 1. All other forms of
	 * rate-control can be negatively impacted by increases to the number of
	 * frame threads because the extra concurrency adds uncertainty to the
	 * bitrate estimations. Frame parallelism is generally limited by the the
	 * is generally limited by the the number of CU rows
	 *
	 * When thread pools are used, each frame thread is assigned to a single
	 * pool and the frame thread itself is given the node affinity of its pool.
	 * But when no thread pools are used no node affinity is assigned. */

	/* vega_bqb_hevc: not support */
	int frameNumThreads;

	/* x265: Comma seperated list of threads per NUMA node. If "none", then no worker
	 * pools are created and only frame parallelism is possible. If NULL or ""
	 * (default) vega_bqb_hevc will use all available threads on each NUMA node.
	 *
	 * '+'  is a special value indicating all cores detected on the node
	 * '*'  is a special value indicating all cores detected on the node and all
	 *      remaining nodes.
	 * '-'  is a special value indicating no cores on the node, same as '0'
	 *
	 * example strings for a 4-node system:
	 *   ""        - default, unspecified, all numa nodes are used for thread pools
	 *   "*"       - same as default
	 *   "none"    - no thread pools are created, only frame parallelism possible
	 *   "-"       - same as "none"
	 *   "10"      - allocate one pool, using up to 10 cores on node 0
	 *   "-,+"     - allocate one pool, using all cores on node 1
	 *   "+,-,+"   - allocate two pools, using all cores on nodes 0 and 2
	 *   "+,-,+,-" - allocate two pools, using all cores on nodes 0 and 2
	 *   "-,*"     - allocate three pools, using all cores on nodes 1, 2 and 3
	 *   "8,8,8,8" - allocate four pools with up to 8 threads in each pool
	 *
	 * The total number of threads will be determined by the number of threads
	 * assigned to all nodes. The worker threads will each be given affinity for
	 * their node, they will not be allowed to migrate between nodes, but they
	 * will be allowed to move between CPU cores within their node.
	 *
	 * If the three pool features: bEnableWavefront, bDistributeModeAnalysis and
	 * bDistributeMotionEstimation are all disabled, then numaPools is ignored
	 * and no thread pools are created.
	 *
	 * If "none" is specified, then all three of the thread pool features are
	 * implicitly disabled.
	 *
	 * Multiple thread pools will be allocated for any NUMA node with more than
	 * 64 logical CPU cores. But any given thread pool will always use at most
	 * one NUMA node.
	 *
	 * Frame encoders are distributed between the available thread pools, and
	 * the encoder will never generate more thread pools than frameNumThreads */

	/* vega_bqb_hevc: not support */
	// const char *numaPools;

	/* x265: Enable wavefront parallel processing, greatly increases parallelism for
	 * less than 1% compression efficiency loss. Requires a thread pool, enabled
	 * by default */

	/* vega_bqb_hevc: Specify the WPP mode. 0=WPP off, 1=WPP on. Default is disabled */
	int bEnableWavefront;

	/* x265: Use multiple threads to measure CU mode costs. Recommended for many core
	 * CPUs. On RD levels less than 5, it may not offload enough work to warrant
	 * the overhead. It is useful with the slow preset since it has the
	 * rectangular predictions enabled. At RD level 5 and 6 (preset slower and
	 * below), this feature should be an unambiguous win if you have CPU
	 * cores available for work. Default disabled */

	/* vega_bqb_hevc: not support */
	int bDistributeModeAnalysis;

	/* x265: Use multiple threads to perform motion estimation to (ME to one reference
	 * per thread). Recommended for many core CPUs. The more references the more
	 * motion searches there will be to distribute. This option is often not a
	 * win, particularly in video sequences with low motion. Default disabled */

	/* vega_bqb_hevc: not support */
	int bDistributeMotionEstimation;

	/*== Logging Features ==*/

	/* x265: Enable analysis and logging distribution of CUs encoded across various
	 * modes during mode decision. Default disabled */

	/* vega_bqb_hevc: not support */
	int bLogCuStats;

	/* x265: Enable the measurement and reporting of PSNR. Default is enabled */

	/* vega_bqb_hevc: not support */
	int bEnablePsnr;

	/* x265: Enable the measurement and reporting of SSIM. Default is disabled */

	/* vega_bqb_hevc: not support */
	int bEnableSsim;

	/* x265: The level of logging detail emitted by the encoder. VEGA_BQB_HEVC_LOG_NONE to
	 * VEGA_BQB_HEVC_LOG_FULL, default is VEGA_BQB_HEVC_LOG_INFO */

	/* x265: filename of CSV log. If logLevel greater than or equal to VEGA_BQB_HEVC_LOG_FRAME,
	 * the encoder will emit per-slice statistics to this log file in encode
	 * order. Otherwise the encoder will emit per-stream statistics into the log
	 * file when vega_bqb_hevc_encoder_log is called (presumably at the end of the
	 * encode) */

	/* vega_bqb_hevc: not support */
	// const char *csvfn;

	/*== Internal Picture Specification ==*/

	/* x265: Internal encoder bit depth. If vega_bqb_hevc was compiled to use 8bit pixels
	 * (HIGH_BIT_DEPTH=0), this field must be 8, else this field must be 10.
	 * Future builds may support 12bit pixels. */

	/* x265: Total Number of frames to be encoded, calculated from the user input
	 * (--frames) and (--seek). In case, the input is read from a pipe, this can
	 * remain as 0. It is later used in 2 pass RateControl, hence storing the
	 * value in param */

	/* vega_bqb_hevc: not support */
	// int totalFrames;

	/*== Profile / Tier / Level ==*/

	/* x265: Note: the profile is specified by vega_bqb_hevc_param_apply_profile() */

	/* x265: Minimum decoder requirement level. Defaults to 0, which implies auto-
	 * detection by the encoder. If specified, the encoder will attempt to bring
	 * the encode specifications within that specified level. If the encoder is
	 * unable to reach the level it issues a warning and emits the actual
	 * decoder requirement. If the requested requirement level is higher than
	 * the actual level, the actual requirement level is signaled. The value is
	 * an specified as an integer with the level times 10, for example level
	 * "5.1" is specified as 51, and level "5.0" is specified as 50. */

	/* vega_bqb_hevc: Specify hevc general_level_idc. Please set a value of 10 times.
	 * e.g. 51= Level 5.1 Default 51*/
	int levelIdc;

	/* x265: if levelIdc is specified (non-zero) this flag will differentiate between
	 * Main (0) and High (1) tier. Default is Main tier (0) */

	/* vega_bqb_hevc: Specify hevc general_tier_flag. 0=Main Tier, 1=High Tier Default 0 */
	int bHighTier;

	/* x265: The maximum number of L0 references a P or B slice may use. This
	 * influences the size of the decoded picture buffer. The higher this
	 * number, the more reference frames there will be available for motion
	 * search, improving compression efficiency of most video at a cost of
	 * performance. Value must be between 1 and 16, default is 3 */

	/* vega_bqb_hevc: not support */
	int maxNumReferences;

	/* x265: Allow libvega_bqb_hevc to emit HEVC bitstreams which do not meet strict level
	 * requirements. Defaults to false */

	/* vega_bqb_hevc: not support */
	int bAllowNonConformance;

	/*== Bitstream Options ==*/

	/* x265: Flag indicating whether VPS, SPS and PPS headers should be output with
	 * each keyframe. Default false */

	/* vega_bqb_hevc: not support */
	int bRepeatHeaders;

	/* x265: Flag indicating whether the encoder should generate start codes (Annex B
	 * format) or length (file format) before NAL units. Default true, Annex B.
	 * Muxers should set this to the correct value */

	/* vega_bqb_hevc: not support */
	int bAnnexB;

	/* x265: Flag indicating whether the encoder should emit an Access Unit Delimiter
	 * NAL at the start of every access unit. Default false */

	/* vega_bqb_hevc: not support */
	int bEnableAccessUnitDelimiters;

	/* x265: Enables the buffering period SEI and picture timing SEI to signal the HRD
	 * parameters. Default is disabled */

	/* vega_bqb_hevc: Specify whether the following parameters are inserted.
	 * hrd_parameters() of VUI
	 * buffering_period() SEI
	 * removal_delay of pic_timing() SEI
	 * please set PIC_TIMING=1.
	 * Deafult is disabled */
	int bEmitHRDSEI;

	/* x265: Enables the emission of a user data SEI with the stream headers which
	 * describes the encoder version, build info, and parameters. This is
	 * very helpful for debugging, but may interfere with regression tests.
	 * Default enabled */

	/* vega_bqb_hevc: Default disabled */
	int bEmitInfoSEI;

	/* x265: Enable the generation of SEI messages for each encoded frame containing
	 * the hashes of the three reconstructed picture planes. Most decoders will
	 * validate those hashes against the reconstructed images it generates and
	 * report any mismatches. This is essentially a debugging feature.  Hash
	 * types are MD5(1), CRC(2), Checksum(3).  Default is 0, none */

	/* vega_bqb_hevc: Decoded picture hash SEI enabled flag. 0=disabled. 1=enabled
	 * ( +0(MD5), +1(CRC), +2(Checksum) ) Default is 0 */
	int decodedPictureHashSEI;

	/* x265: Enable Temporal Sub Layers while encoding, signals NAL units of coded
	 * slices with their temporalId. Output bitstreams can be extracted either
	 * at the base temporal layer (layer 0) with roughly half the frame rate or
	 * at a higher temporal layer (layer 1) that decodes all the frames in the
	 * sequence. */

	/* vega_bqb_hevc: not support */
	int bEnableTemporalSubLayers;

	/*== GOP structure and slice type decisions (lookahead) ==*/

	/* x265: Enable open GOP - meaning I slices are not necessarily IDR and thus frames
	 * encoded after an I slice may reference frames encoded prior to the I
	 * frame which have remained in the decoded picture buffer.  Open GOP
	 * generally has better compression efficiency and negligible encoder
	 * performance impact, but the use case may preclude it.  Default true */

	/* vega_bqb_hevc: Specify open GOP or closed GOP. 0=open GOP (IDR + CRA) 1=closed GOP (IDR).
	 * Default open GOP */
	int bOpenGOP;

	/* x265: Scene cuts closer together than this are coded as I, not IDR. */

	/* vega_bqb_hevc: not support */
	int keyframeMin;

	/* x265: Maximum keyframe distance or intra period in number of frames. If 0 or 1,
	 * all frames are I frames. A negative value is casted to MAX_INT internally
	 * which effectively makes frame 0 the only I frame. Default is 250 */

	/* x265: Maximum consecutive B frames that can be emitted by the lookahead. When
	 * b-adapt is 0 and keyframMax is greater than bframes, the lookahead emits
	 * a fixed pattern of `bframes` B frames between each P.  With b-adapt 1 the
	 * lookahead ignores the value of bframes for the most part.  With b-adapt 2
	 * the value of bframes determines the search (POC) distance performed in
	 * both directions, quadratically increasing the compute load of the
	 * lookahead.  The higher the value, the more B frames the lookahead may
	 * possibly use consecutively, usually improving compression. Default is 3,
	 * maximum is 16 */

	/* x265: Sets the operating mode of the lookahead.  With b-adapt 0, the GOP
	 * structure is fixed based on the values of keyframeMax and bframes.
	 * With b-adapt 1 a light lookahead is used to chose B frame placement.
	 * With b-adapt 2 (trellis) a viterbi B path selection is performed */

	/* vega_bqb_hevc: not support */
	int bFrameAdaptive;

	/* x265: When enabled, the encoder will use the B frame in the middle of each
	 * mini-GOP larger than 2 B frames as a motion reference for the surrounding
	 * B frames.  This improves compression efficiency for a small performance
	 * penalty.  Referenced B frames are treated somewhere between a B and a P
	 * frame by rate control.  Default is enabled. */

	/* vega_bqb_hevc: not support */
	int bBPyramid;

	/* x265: A value which is added to the cost estimate of B frames in the lookahead.
	 * It may be a positive value (making B frames appear more expensive, which
	 * causes the lookahead to chose more P frames) or negative, which makes the
	 * lookahead chose more B frames. Default is 0, there are no limits */

	/* vega_bqb_hevc: not support */
	int bFrameBias;

	/* x265: The number of frames that must be queued in the lookahead before it may
	 * make slice decisions. Increasing this value directly increases the encode
	 * latency. The longer the queue the more optimally the lookahead may make
	 * slice decisions, particularly with b-adapt 2. When cu-tree is enabled,
	 * the length of the queue linearly increases the effectiveness of the
	 * cu-tree analysis. Default is 40 frames, maximum is 250 */

	/* vega_bqb_hevc: not support */
	int lookaheadDepth;

	/* x265: Use multiple worker threads to measure the estimated cost of each frame
	 * within the lookahead. When bFrameAdaptive is 2, most frame cost estimates
	 * will be performed in batch mode, many cost estimates at the same time,
	 * and lookaheadSlices is ignored for batched estimates. The effect on
	 * performance can be quite small.  The higher this parameter, the less
	 * accurate the frame costs will be (since context is lost across slice
	 * boundaries) which will result in less accurate B-frame and scene-cut
	 * decisions. Default is 0 - disabled. 1 is the same as 0. Max 16 */

	/* vega_bqb_hevc: not support */
	int lookaheadSlices;

	/* x265: An arbitrary threshold which determines how aggressively the lookahead
	 * should detect scene cuts. The default (40) is recommended. */

	/* vega_bqb_hevc: Specifies whether to insert the I-picture with the scene change.
	 * 0=Not insert.
	 * 1=After the scene change, Set the TID=0 picture to I or IDR picture.
	 * The I and IDR interval counter, which are specified by IP_PERIOD and IDR_INTERVAL,
	 * is reset by the NEW I or IDR picture.*/
	int scenecutThreshold;

	/*== Coding Unit (CU) definitions ==*/

	/* x265: Maximum CU width and height in pixels.  The size must be 64, 32, or 16.
	 * The higher the size, the more efficiently vega_bqb_hevc can encode areas of low
	 * complexity, greatly improving compression efficiency at large
	 * resolutions.  The smaller the size, the more effective wavefront and
	 * frame parallelism will become because of the increase in rows. default 64
	 * All encoders within the same process must use the same maxCUSize, until
	 * all encoders are closed and vega_bqb_hevc_cleanup() is called to reset the value. */

	/* vega_bqb_hevc: CTU Size. 16=16x16, 32=32x32, 64=64x64(default) */
	uint32_t maxCUSize;

	/* x265: Minimum CU width and height in pixels.  The size must be 64, 32, 16, or
	 * 8. Default 8. All encoders within the same process must use the same
	 * minCUSize. */

	/* vega_bqb_hevc: Minimum CU_SIZE. 8=8x8(default), 16=16x16, 32=32x32, 64=64x64 */
	uint32_t minCUSize;

	/* x265: Enable rectangular motion prediction partitions (vertical and
	 * horizontal), available at all CU depths from 64x64 to 8x8. Default is
	 * disabled */

	/* vega_bqb_hevc: not support */
	int bEnableRectInter;

	/* x265: Enable asymmetrical motion predictions.  At CU depths 64, 32, and 16, it
	 * is possible to use 25%/75% split partitions in the up, down, right, left
	 * directions. For some material this can improve compression efficiency at
	 * the cost of extra analysis. bEnableRectInter must be enabled for this
	 * feature to be used. Default disabled */

	/* vega_bqb_hevc: amp_enabled_flag, 0=disabled, 1=enabled. Default 0 */
	int bEnableAMP;

	/*== Residual Quadtree Transform Unit (TU) definitions ==*/

	/* x265: Maximum TU width and height in pixels.  The size must be 32, 16, 8 or 4.
	 * The larger the size the more efficiently the residual can be compressed
	 * by the DCT transforms, at the expense of more computation */

	/* vega_bqb_hevc: Maximum TU Size.(MAX_TU_SIZE>MIN_TU_SIZE) 0=32x32(default), 4=4x4, 8=8x8,
	 * 16=16x16, 32=32x32 */
	uint32_t maxTUSize;

	/* x265: The additional depth the residual quad-tree is allowed to recurse beyond
	 * the coding quad-tree, for inter coded blocks. This must be between 1 and
	 * 4. The higher the value the more efficiently the residual can be
	 * compressed by the DCT transforms, at the expense of much more compute */

	/* vega_bqb_hevc: Maximum depth of InterTU. 1..4 (SPS:max_transform_hierarchy_depth_inter)
	 * Recommendation for the VQ Log2CTUSize - Log2MinTUSize = 4 */
	uint32_t tuQTMaxInterDepth;

	/* x265: The additional depth the residual quad-tree is allowed to recurse beyond
	 * the coding quad-tree, for intra coded blocks. This must be between 1 and
	 * 4. The higher the value the more efficiently the residual can be
	 * compressed by the DCT transforms, at the expense of much more compute */

	/* vega_bqb_hevc: Maximum depth of IntraTU. 1..4. (SPS:max_transform_hierarchy_depth_intra)
	 * Recommendation for the VQ Log2CTUSize - Log2MinTUSize = 4 */
	uint32_t tuQTMaxIntraDepth;

	/* x265: Set the amount of rate-distortion analysis to use within quant. 0 implies
	 * no rate-distortion optimization. At level 1 rate-distortion cost is used to
	 * find optimal rounding values for each level (and allows psy-rdoq to be
	 * enabled). At level 2 rate-distortion cost is used to make decimate decisions
	 * on each 4x4 coding group (including the cost of signaling the group within
	 * the group bitmap).  Psy-rdoq is less effective at preserving energy when
	 * RDOQ is at level 2 */

	/* vega_bqb_hevc: RDOQ enabled flag. 0=RDOQ disabled, 1=RDOQ enabled. Default 0 */
	int rdoqLevel;

	/* x265: Enable the implicit signaling of the sign bit of the last coefficient of
	 * each transform unit. This saves one bit per TU at the expense of figuring
	 * out which coefficient can be toggled with the least distortion.
	 * Default is enabled */

	/* vega_bqb_hevc: not support */
	int bEnableSignHiding;

	/* x265: Allow intra coded blocks to be encoded directly as residual without the
	 * DCT transform, when this improves efficiency. Checking whether the block
	 * will benefit from this option incurs a performance penalty. Default is
	 * disabled */

	/* vega_bqb_hevc: transformskip for Intra picture 0=disabled, 1=enabled. Default 0 */
	int bEnableTransformSkip;

	/* x265: An integer value in range of 0 to 2000, which denotes strength of noise
	 * reduction in intra CUs. 0 means disabled */

	/* vega_bqb_hevc: not support */
	int noiseReductionIntra;

	/* x265: An integer value in range of 0 to 2000, which denotes strength of noise
	 * reduction in inter CUs. 0 means disabled */

	/* vega_bqb_hevc: not support */
	int noiseReductionInter;

	/* x265: Quantization scaling lists. HEVC supports 6 quantization scaling lists to
	 * be defined; one each for Y, Cb, Cr for intra prediction and one each for
	 * inter prediction.
	 *
	 * - NULL and "off" will disable quant scaling (default)
	 * - "default" will enable the HEVC default scaling lists, which
	 *   do not need to be signaled since they are specified
	 * - all other strings indicate a filename containing custom scaling lists
	 *   in the HM format. The encode will fail if the file is not parsed
	 *   correctly. Custom lists must be signaled in the SPS. */

	/* vega_bqb_hevc: not support */
	const char *scalingLists;

	/*== Intra Coding Tools ==*/

	/* x265: Enable constrained intra prediction. This causes intra prediction to
	 * input samples that were inter predicted. For some use cases this is
	 * believed to me more robust to stream errors, but it has a compression
	 * penalty on P and (particularly) B slices. Defaults to disabled */

	/* vega_bqb_hevc: not support */
	int bEnableConstrainedIntra;

	/* x265: Enable strong intra smoothing for 32x32 blocks where the reference
	 * samples are flat. It may or may not improve compression efficiency,
	 * depending on your source material. Defaults to disabled */

	/* vega_bqb_hevc: strong_intra_smoothing_enabled_flag. 0=disabled, 1=enabled.
	 * Default 0 */
	int bEnableStrongIntraSmoothing;

	/*== Inter Coding Tools ==*/

	/* x265: The maximum number of merge candidates that are considered during inter
	 * analysis.  This number (between 1 and 5) is signaled in the stream
	 * headers and determines the number of bits required to signal a merge so
	 * it can have significant trade-offs. The smaller this number the higher
	 * the performance but the less compression efficiency. Default is 3 */

	/* vega_bqb_hevc: max_num_merge_cand, 0=max_num_merge_cand=5 1~5=merge cand,
	 * Default 0 */
	// uint32_t  maxNumMergeCand;
	int maxNumMergeCand;

	/* x265: Limit the motion references used for each search based on the results of
	 * previous motion searches already performed for the same CU: If 0 all
	 * references are always searched. If VEGA_BQB_HEVC_REF_LIMIT_CU all motion searches
	 * will restrict themselves to the references selected by the 2Nx2N search
	 * at the same depth. If VEGA_BQB_HEVC_REF_LIMIT_DEPTH the 2Nx2N motion search will
	 * only use references that were selected by the best motion searches of the
	 * 4 split CUs at the next lower CU depth.  The two flags may be combined */

	/* vega_bqb_hevc: not support */
	uint32_t limitReferences;

	/* x265: ME search method (DIA, HEX, UMH, STAR, FULL). The search patterns
	 * (methods) are sorted in increasing complexity, with diamond being the
	 * simplest and fastest and full being the slowest.  DIA, HEX, and UMH were
	 * adapted from x264 directly. STAR is an adaption of the HEVC reference
	 * encoder's three step search, while full is a naive exhaustive search. The
	 * default is the star search, it has a good balance of performance and
	 * compression efficiency */

	/* vega_bqb_hevc: not support */
	int searchMethod;

	/* x265: A value between 0 and VEGA_BQB_HEVC_MAX_SUBPEL_LEVEL which adjusts the amount of
	 * effort performed during sub-pel refine. Default is 5 */

	/* vega_bqb_hevc: not support */
	int subpelRefine;

	/* x265: The maximum distance from the motion prediction that the full pel motion
	 * search is allowed to progress before terminating. This value can have an
	 * effect on frame parallelism, as referenced frames must be at least this
	 * many rows of reconstructed pixels ahead of the referencee at all times.
	 * (When considering reference lag, the motion prediction must be ignored
	 * because it cannot be known ahead of time).  Default is 60, which is the
	 * default max CU size (64) minus the luma HPEL half-filter length (4). If a
	 * smaller CU size is used, the search range should be similarly reduced */

	/* vega_bqb_hevc: not support */
	int searchRange;

	/* x265: Enable availability of temporal motion vector for AMVP, default is enabled */

	/* vega_bqb_hevc: sps_temporal_mvp_enabled_flag 0=disabled, 1=enabled. Default disabled */
	int bEnableTemporalMvp;

	/* x265: Enable weighted prediction in P slices.  This enables weighting analysis
	 * in the lookahead, which influences slice decisions, and enables weighting
	 * analysis in the main encoder which allows P reference samples to have a
	 * weight function applied to them prior to using them for motion
	 * compensation.  In video which has lighting changes, it can give a large
	 * improvement in compression efficiency. Default is enabled */

	/* vega_bqb_hevc: Weighted prediction
	 * bit[0]=PPS: weighted_pred_flag
	 * bit[1]=PPS: weighted_bi_pred_flag
	 * Default is 0 */
	int bEnableWeightedPred;

	/* x265: Enable weighted prediction in B slices. Default is disabled */

	/* vega_bqb_hevc: Weighted prediction
	 * bit[0]=PPS: weighted_pred_flag
	 * bit[1]=PPS: weighted_bi_pred_flag
	 * Default is 0 */
	int bEnableWeightedBiPred;

	/*== Loop Filters ==*/

	/* x265: Enable the deblocking loop filter, which improves visual quality by
	 * reducing blocking effects at block edges, particularly at lower bitrates
	 * or higher QP. When enabled it adds another CU row of reference lag,
	 * reducing frame parallelism effectiveness. Default is enabled */

	/* vega_bqb_hevc: Specify the opposite value of pcm_loop_filter_disabled_flag SPS.
	 * 0:pcm_loop_filter_disabled_flag=1, 1:pcm_loop_filter_disabled_flag=0
	 * Default is 0 */
	int bEnableLoopFilter;

	/* x265: deblocking filter tC offset [-6, 6] -6 light filter, 6 strong.
	 * This is the coded div2 value, actual offset is doubled at use */

	/* vega_bqb_hevc: not support */
	int deblockingFilterTCOffset;

	/* x265: deblocking filter Beta offset [-6, 6] -6 light filter, 6 strong
	 * This is the coded div2 value, actual offset is doubled at use */

	/* vega_bqb_hevc: not support */
	int deblockingFilterBetaOffset;

	//#if (API_CHIP_TYPE == API_CHIP_M30)
	/* x265: Enable the Sample Adaptive Offset loop filter, which reduces distortion
	 * effects by adjusting reconstructed sample values based on histogram
	 * analysis to better approximate the original samples. When enabled it adds
	 * a CU row of reference lag, reducing frame parallelism effectiveness.
	 * Default is enabled */

	/* vega_bqb_hevc: SAO enabled flag.
	 * bit[0]= SPS: sample_adaptive_offest_enabled_flag
	 * bit[1]= Slice: slice_sao_luma_flag
	 * bit[2]= Slice: slice_sao_chroma_flag
	 * Default is 0 */
	int bEnableSAO;
	//#endif

	/* Note: when deblocking and SAO are both enabled, the loop filter CU lag is
	 * only one row, as they operate in series on the same row. */

	/* x265: Select the method in which SAO deals with deblocking boundary pixels.  If
	 * disabled the right and bottom boundary areas are skipped. If enabled,
	 * non-deblocked pixels are used entirely. Default is disabled */

	/* vega_bqb_hevc: not supported */
	int bSaoNonDeblocked;

	/*== Analysis tools ==*/

	/* x265: A value between VEGA_BQB_HEVC_NO_RDO_NO_RDOQ and VEGA_BQB_HEVC_RDO_LEVEL which determines
	 * the level of rate distortion optimizations to perform during mode
	 * decisions and quantization. The more RDO the better the compression
	 * efficiency at a major cost of performance. Default is no RDO (0) */

	/* vega_bqb_hevc: not supported */
	int rdLevel;

	/* x265: Enable early skip decisions to avoid intra and inter analysis in likely
	 * skip blocks. Default is disabled */

	/* vega_bqb_hevc: not supported */
	int bEnableEarlySkip;

	/* x265: Use a faster search method to find the best intra mode. Default is 0 */

	/* vega_bqb_hevc: not supported */
	int bEnableFastIntra;

	/* x265: Enable a faster determination of whether skipping the DCT transform will
	 * be beneficial. Slight performance gain for some compression loss. Default
	 * is enabled */

	/* vega_bqb_hevc: not supported */
	int bEnableTSkipFast;

	/* x265: The CU Lossless flag, when enabled, compares the rate-distortion costs
	 * for normal and lossless encoding, and chooses the best mode for each CU.
	 * If lossless mode is chosen, the cu-transquant-bypass flag is set for that
	 * CU */

	/* vega_bqb_hevc: not supported */
	int bCULossless;

	/* x265: Specify whether to attempt to encode intra modes in B frames. By default
	 * enabled, but only applicable for the presets which use rdLevel 5 or 6
	 * (veryslow and placebo). All other presets will not try intra in B frames
	 * regardless of this setting */

	/* vega_bqb_hevc: not supported */
	int bIntraInBFrames;

	/* x265: Apply an optional penalty to the estimated cost of 32x32 intra blocks in
	 * non-intra slices. 0 is disabled, 1 enables a small penalty, and 2 enables
	 * a full penalty. This favors inter-coding and its low bitrate over
	 * potential increases in distortion, but usually improves performance.
	 * Default is 0 */

	/* vega_bqb_hevc: not supported */
	int rdPenalty;

	/* x265: Psycho-visual rate-distortion strength. Only has an effect in presets
	 * which use RDO. It makes mode decision favor options which preserve the
	 * energy of the source, at the cost of lost compression. The value must
	 * be between 0 and 2.0, 1.0 is typical. Default 0.3 */

	/* vega_bqb_hevc: not supported */
	double psyRd;

	/* x265: Strength of psycho-visual optimizations in quantization. Only has an
	 * effect in presets which use RDOQ (rd-levels 4 and 5).  The value must be
	 * between 0 and 50, 1.0 is typical. Default 1.0 */

	/* vega_bqb_hevc: not supported */
	double psyRdoq;

	/* x265: If VEGA_BQB_HEVC_ANALYSIS_SAVE, write per-frame analysis information into analysis
	 * buffers.  if VEGA_BQB_HEVC_ANALYSIS_LOAD, read analysis information into analysis
	 * buffer and use this analysis information to reduce the amount of work
	 * the encoder must perform. Default VEGA_BQB_HEVC_ANALYSIS_OFF */

	/* vega_bqb_hevc: not supported */
	int analysisMode;

	/* x265: Filename for analysisMode save/load. Default name is "vega_bqb_hevc_analysis.dat" */

	/* vega_bqb_hevc: not supported */
	const char *analysisFileName;

	/*== Rate Control ==*/

	/* x265: The lossless flag enables true lossless coding, bypassing scaling,
	 * transform, quantization and in-loop filter processes. This is used for
	 * ultra-high bitrates with zero loss of quality. It implies no rate control */

	/* vega_bqb_hevc: not supported */
	int bLossless;

	/* x265: Generally a small signed integer which offsets the QP used to quantize
	 * the Cb chroma residual (delta from luma QP specified by rate-control).
	 * Default is 0, which is recommended */

	/* vega_bqb_hevc: not supported */
	int cbQpOffset;

	/* x265: Generally a small signed integer which offsets the QP used to quantize
	 * the Cr chroma residual (delta from luma QP specified by rate-control).
	 * Default is 0, which is recommended */

	/* vega_bqb_hevc: not supported */
	int crQpOffset;

	/*== Video Usability Information ==*/
	struct
	{
		/* x265: Aspect ratio idc to be added to the VUI.  The default is 0 indicating
		 * the apsect ratio is unspecified. If set to VEGA_BQB_HEVC_EXTENDED_SAR then
		 * sarWidth and sarHeight must also be set */

		/* vega_bqb_hevc: Specify the ASPECT_RATIO_IDC in the VUI parameters. Default is 1 */
		int aspectRatioIdc;

		/* x265: Sample Aspect Ratio width in arbitrary units to be added to the VUI
		 * only if aspectRatioIdc is set to VEGA_BQB_HEVC_EXTENDED_SAR.  This is the width
		 * of an individual pixel. If this is set then sarHeight must also be set */

		/* vega_bqb_hevc: Specify Width of the sample aspect ratio when the ASPECT_RATIO_IDC
		   is equal 255. Default is 0 */
		int sarWidth;

		/* x265: Sample Aspect Ratio height in arbitrary units to be added to the VUI.
		 * only if aspectRatioIdc is set to VEGA_BQB_HEVC_EXTENDED_SAR.  This is the width
		 * of an individual pixel. If this is set then sarWidth must also be set */

		/* vega_bqb_hevc: Specify Hegiht of the sample aspect ratio when the ASPECT_RATIO_IDC
		   is equal 255. Default is 0 */
		int sarHeight;

		/* x265: Enable overscan info present flag in the VUI.  If this is set then
		 * bEnabledOverscanAppropriateFlag will be added to the VUI. The default
		 * is false */

		/* vega_bqb_hevc: Specify whether the overscan_info of vui_parameters()
		 * 0: overscan_info_present_flag=0
		 * 1: overscan_info_present_flag=1, overscan_appropriate_flag=0
		 * 2: overscan_info_present_flag=1, overscan_appropriate_flag=1
		 * Default is 0 */
		int bEnableOverscanInfoPresentFlag;

		/* x265: Enable overscan appropriate flag.  The status of this flag is added
		 * to the VUI only if bEnableOverscanInfoPresentFlag is set. If this
		 * flag is set then cropped decoded pictures may be output for display.
		 * The default is false */

		/* vega_bqb_hevc: Specify whether the overscan_info of vui_parameters()
		 * 0: overscan_info_present_flag=0
		 * 1: overscan_info_present_flag=1, overscan_appropriate_flag=0
		 * 2: overscan_info_present_flag=1, overscan_appropriate_flag=1
		 * Default is 0 */
		int bEnableOverscanAppropriateFlag;

		/* x265: Video signal type present flag of the VUI.  If this is set then
		 * videoFormat, bEnableVideoFullRangeFlag and
		 * bEnableColorDescriptionPresentFlag will be added to the VUI. The
		 * default is false */

		/* vega_bqb_hevc: Specify the video_signal_type_present_flag value of vui_parameters()
		 * Default is 0 */
		int bEnableVideoSignalTypePresentFlag;

		/* x265: Video format of the source video.  0 = component, 1 = PAL, 2 = NTSC,
		 * 3 = SECAM, 4 = MAC, 5 = unspecified video format is the default */

		/* vega_bqb_hevc: video_format. Default is 0 */
		int videoFormat;

		/* x265: Video full range flag indicates the black level and range of the luma
		 * and chroma signals as derived from E′Y, E′PB, and E′PR or E′R, E′G,
		 * and E′B real-valued component signals. The default is false */

		/* vega_bqb_hevc: video_full_range_flag. Default is 0 */
		int bEnableVideoFullRangeFlag;

		/* x265: Color description present flag in the VUI. If this is set then
		 * color_primaries, transfer_characteristics and matrix_coeffs are to be
		 * added to the VUI. The default is false */

		/* vega_bqb_hevc: colour_description_present_flag. Default is 0 */
		int bEnableColorDescriptionPresentFlag;

		/* x265: Color primaries holds the chromacity coordinates of the source
		 * primaries. The default is 2 */

		/* vega_bqb_hevc: colour_primaries. Default is 0 */
		int colorPrimaries;

		/* x265: Transfer characteristics indicates the opto-electronic transfer
		 * characteristic of the source picture. The default is 2 */

		/* vega_bqb_hevc: transfer_characteristics. Default is 0 */
		int transferCharacteristics;

		/* x265: Matrix coefficients used to derive the luma and chroma signals from
		 * the red, blue and green primaries. The default is 2 */

		/* vega_bqb_hevc: matrix_coeffs. Default is 0 */
		int matrixCoeffs;

		/* x265: Chroma location info present flag adds chroma_sample_loc_type_top_field and
		 * chroma_sample_loc_type_bottom_field to the VUI. The default is false */

		/* vega_bqb_hevc: chroma_loc_info_present_flag. Default is 0 */
		int bEnableChromaLocInfoPresentFlag;

		/* x265: Chroma sample location type top field holds the chroma location in
		 * the top field. The default is 0 */

		/* vega_bqb_hevc: chroma_sample_loc_type_top_field. Default is 0 */
		int chromaSampleLocTypeTopField;

		/* x265: Chroma sample location type bottom field holds the chroma location in
		 * the bottom field. The default is 0 */

		/* vega_bqb_hevc: chroma_sample_loc_type_bottom_field. Default is 0 */
		int chromaSampleLocTypeBottomField;

		/* x265: Default display window flag adds def_disp_win_left_offset,
		 * def_disp_win_right_offset, def_disp_win_top_offset and
		 * def_disp_win_bottom_offset to the VUI. The default is false */

		/* vega_bqb_hevc: Default is 0 */
		int bEnableDefaultDisplayWindowFlag;

		/* x265: Default display window left offset holds the left offset with the
		 * conformance cropping window to further crop the displayed window */

		/* vega_bqb_hevc: Specify the Cropping information for left side of a picture.
		 * This parameter specify the pel count of the cropping pictures. */
		int defDispWinLeftOffset;

		/* x265: Default display window right offset holds the right offset with the
		 * conformance cropping window to further crop the displayed window */

		/* vega_bqb_hevc: Specify the Cropping information for right side of a picture.
		 * This parameter specify the pel count of the cropping pictures. */
		int defDispWinRightOffset;

		/* x265: Default display window top offset holds the top offset with the
		 * conformance cropping window to further crop the displayed window */

		/* vega_bqb_hevc: Specify the Cropping information for top side of a picture.
		 * This parameter specify the line count of the cropping pictures.
		 * If the codec mode is interlace (INTERLACE = 1) then this parameter
		 * works for frame picture.(1920x1080i -> CROP_TOP=0, CROP_BOTTOM=8) */
		int defDispWinTopOffset;

		/* x265: Default display window bottom offset holds the bottom offset with the
		 * conformance cropping window to further crop the displayed window */

		/* vega_bqb_hevc: Specify the Cropping information for bottom side of a picture.
		 * This parameter specify the line count of the cropping pictures.
		 * If the codec mode is interlace (INTERLACE = 1) then this parameter
		 * works for frame picture.(1920x1080i -> CROP_TOP=0, CROP_BOTTOM=8) */
		int defDispWinBottomOffset;
	} vui;

	/* x265: SMPTE ST 2086 mastering display color volume SEI info, specified as a
	 * string which is parsed when the stream header SEI are emitted. The string
	 * format is "G(%hu,%hu)B(%hu,%hu)R(%hu,%hu)WP(%hu,%hu)L(%u,%u)" where %hu
	 * are unsigned 16bit integers and %u are unsigned 32bit integers. The SEI
	 * includes X,Y display primaries for RGB channels, white point X,Y and
	 * max,min luminance values. */

	/* vega_bqb_hevc: SMPTE ST 2086 mastering display color volume SEI info, specified as a
	 * string which is parsed when the stream header SEI are emitted. The string
	 * format is "G[%hu,%hu]B[%hu,%hu]R[%hu,%hu]WP[%hu,%hu]L[%u,%u]" where %hu
	 * are unsigned 16bit integers and %u are unsigned 32bit integers. The SEI
	 * includes X,Y display primaries for RGB channels, white point X,Y and
	 * max,min luminance values. */
	const char *masteringDisplayColorVolume;

	/* x265: Maximum Content light level(MaxCLL), specified as integer that indicates the
	 * maximum pixel intensity level in units of 1 candela per square metre of the
	 * bitstream. x265 will also calculate MaxCLL programmatically from the input
	 * pixel values and set in the Content light level info SEI */

	/* vega_bqb_hevc: supported */
	uint16_t maxCLL;

	/* x265: Maximum Frame Average Light Level(MaxFALL), specified as integer that indicates
	 * the maximum frame average intensity level in units of 1 candela per square
	 * metre of the bitstream. x265 will also calculate MaxFALL programmatically
	 * from the input pixel values and set in the Content light level info SEI */

	/* vega_bqb_hevc: supported */
	uint16_t maxFALL;

	/* x265: Minimum luma level of input source picture, specified as a integer which
	 * would automatically increase any luma values below the specified --min-luma
	 * value to that value. */

	/* vega_bqb_hevc: not supported */
	uint16_t minLuma;

	/* x265: Maximum luma level of input source picture, specified as a integer which
	 * would automatically decrease any luma values above the specified --max-luma
	 * value to that value. */

	/* vega_bqb_hevc: not supported */
	uint16_t maxLuma;

	/* vega_bqb_hevc: Specify hevc general_profile_idc.
	 * 1=Main, 2=Main10, 3=Main Still Picture, 4=Main 4:2:2 10. Default is 1 */
	int profile;

	/* vega_bqb_hevc: Set min q value, max q value, max CTU size */
	int coding[NUM_CODING_PARAM];

	/* vega_bqb_hevc: Minimum TU size. (MAX_TU_SIZE>MIN_TU_SIZE)
	 * 4=4x4(default), 8=8x8, 16=16x16, 32=32x32
	 * Recommendation for the VQ: Log2maxCUSize - Log2minTUSize = 4 */
	uint32_t minTUSize;

	/*vega_bqb_hevc: Specfiy the ultra low latency mode*/
	int ultraLowLatency;

	/*
		vega_bqb_hevc: scaling list mode,0:default(SNI), 1: standard
	*/
	int scalingListMode;

	/** Set max size for any frames. Unit: bit */
	uint32_t maxFrameSize;

	/*
	 * vega_bqb_hevc: Encode mode for M30 device
	 */
	int encodeMode;
} vega_bqb_hevc_param;

typedef struct vega_bqb_avc_param
{
	vegaff_codec_param_fields;
	/* vega_bqb_avc: Specify avc general_level_idc. Please set a value of 10 times.
	 * e.g. 51= Level 5.1 Default 51*/
	int levelIdc;

	/*== GOP structure and slice type decisions (lookahead) ==*/

	/* vega_bqb_avc: Specify open GOP or closed GOP. 0=open GOP (IDR + CRA) 1=closed GOP (IDR).
	 * Default open GOP */
	int bOpenGOP;

	/* vega_bqb_avc: Specifies whether to insert the I-picture with the scene change.
	 * 0=Not insert.
	 * 1=After the scene change, Set the TID=0 picture to I or IDR picture.
	 * The I and IDR interval counter, which are specified by IP_PERIOD and IDR_INTERVAL,
	 * is reset by the NEW I or IDR picture.*/
	int scenecutThreshold;

	/*== Inter Coding Tools ==*/

	/* vega_bqb_avc: Weighted prediction
	 * bit[0]=PPS: weighted_pred_flag
	 * bit[1]=PPS: weighted_bi_pred_flag
	 * Default is 0 */
	int bEnableWeightedPred;

	/* vega_bqb_avc: Weighted prediction
	 * bit[0]=PPS: weighted_pred_flag
	 * bit[1]=PPS: weighted_bi_pred_flag
	 * Default is 0 */
	int bEnableWeightedBiPred;

	/*== Loop Filters ==*/

	/* vega_bqb_avc: Specify the opposite value of pcm_loop_filter_disabled_flag SPS.
	 * 0:pcm_loop_filter_disabled_flag=1, 1:pcm_loop_filter_disabled_flag=0
	 * Default is 0 */
	int bEnableLoopFilter;

	/*== Entropy Coding ==*/

	/* vega_bqb_avc: entropy coding mode */
	int bCabac;

	/*== Video Usability Information ==*/
	struct
	{
		/* vega_bqb_avc: Specify the ASPECT_RATIO_IDC in the VUI parameters. Default is 1 */
		int aspectRatioIdc;

		/* vega_bqb_avc: Specify Width of the sample aspect ratio when the ASPECT_RATIO_IDC
		   is equal 255. Default is 0 */
		int sarWidth;

		/* vega_bqb_avc: Specify Hegiht of the sample aspect ratio when the ASPECT_RATIO_IDC
		   is equal 255. Default is 0 */
		int sarHeight;

		/* vega_bqb_avc: Specify whether the overscan_info of vui_parameters()
		 * 0: overscan_info_present_flag=0
		 * 1: overscan_info_present_flag=1, overscan_appropriate_flag=0
		 * 2: overscan_info_present_flag=1, overscan_appropriate_flag=1
		 * Default is 0 */
		int bEnableOverscanInfoPresentFlag;

		/* vega_bqb_avc: Specify whether the overscan_info of vui_parameters()
		 * 0: overscan_info_present_flag=0
		 * 1: overscan_info_present_flag=1, overscan_appropriate_flag=0
		 * 2: overscan_info_present_flag=1, overscan_appropriate_flag=1
		 * Default is 0 */
		int bEnableOverscanAppropriateFlag;

		/* vega_bqb_avc: Specify the video_signal_type_present_flag value of vui_parameters()
		 * Default is 0 */
		int bEnableVideoSignalTypePresentFlag;

		/* vega_bqb_avc: video_format. Default is 0 */
		int videoFormat;

		/* vega_bqb_avc: video_full_range_flag. Default is 0 */
		int bEnableVideoFullRangeFlag;

		/* vega_bqb_avc: colour_description_present_flag. Default is 0 */
		int bEnableColorDescriptionPresentFlag;

		/* vega_bqb_avc: colour_primaries. Default is 0 */
		int colorPrimaries;

		/* vega_bqb_avc: transfer_characteristics. Default is 0 */
		int transferCharacteristics;

		/* vega_bqb_avc: matrix_coeffs. Default is 0 */
		int matrixCoeffs;

		/* vega_bqb_avc: chroma_loc_info_present_flag. Default is 0 */
		int bEnableChromaLocInfoPresentFlag;

		/* vega_bqb_avc: chroma_sample_loc_type_top_field. Default is 0 */
		int chromaSampleLocTypeTopField;

		/* vega_bqb_avc: chroma_sample_loc_type_bottom_field. Default is 0 */
		int chromaSampleLocTypeBottomField;

		/* vega_bqb_avc: Default is 0 */
		int bEnableDefaultDisplayWindowFlag;

		/* vega_bqb_avc: Specify the Cropping information for left side of a picture.
		 * This parameter specify the pel count of the cropping pictures. */
		int defDispWinLeftOffset;

		/* vega_bqb_avc: Specify the Cropping information for right side of a picture.
		 * This parameter specify the pel count of the cropping pictures. */
		int defDispWinRightOffset;

		/* vega_bqb_avc: Specify the Cropping information for top side of a picture.
		 * This parameter specify the line count of the cropping pictures.
		 * If the codec mode is interlace (INTERLACE = 1) then this parameter
		 * works for frame picture.(1920x1080i -> CROP_TOP=0, CROP_BOTTOM=8) */
		int defDispWinTopOffset;

		/* vega_bqb_avc: Specify the Cropping information for bottom side of a picture.
		 * This parameter specify the line count of the cropping pictures.
		 * If the codec mode is interlace (INTERLACE = 1) then this parameter
		 * works for frame picture.(1920x1080i -> CROP_TOP=0, CROP_BOTTOM=8) */
		int defDispWinBottomOffset;
	} vui;

	/* vega_bqb_avc: Specify avc general_profile_idc.
	 * 0=Baseline, 1=Main, 2=High, 3=High10, 4=High 4:2:2 10. Default is 1 */
	int profile;

	/*
	 *	vega_bqb_avc: Encode first picture in display order as IDR.
	 */
	int iDRDisplayOrderFirst;
	/*
	 * vega_bqb_avc: Encode mode for M30 device
	 */
	int encodeMode;
} vega_bqb_avc_param;

typedef struct vega_bqb_mpeg_param
{
	vegaff_codec_param_fields;
	/* vega_bqb_mpeg: Specify mpeg general_level_idc.
	 *
	 auto:  API_VEGA_BQB_MPEG_LEVEL_AUTO	 = 0,
	 high: API_VEGA_BQB_MPEG_LEVEL_HIGH	 = 4,
	 high1440: API_VEGA_BQB_MPEG_LEVEL_HIGH14  = 6,
	 main: API_VEGA_BQB_MPEG_LEVEL_MAIN	 = 8,
	 low: API_VEGA_BQB_MPEG_LEVEL_LOW	 = 10,
	 */
	int levelIdc;

	/*== GOP structure and slice type decisions (lookahead) ==*/

	/* vega_bqb_mpeg: Specify open GOP or closed GOP. 0=open GOP (IDR + CRA) 1=closed GOP (IDR).
	 * Default open GOP */
	int bOpenGOP;

	/* vega_bqb_mpeg: Specifies whether to insert the I-picture with the scene change.
	 * 0=Not insert.
	 * 1=After the scene change, Set the TID=0 picture to I or IDR picture.
	 * The I and IDR interval counter, which are specified by IP_PERIOD and IDR_INTERVAL,
	 * is reset by the NEW I or IDR picture.*/
	int scenecutThreshold;

	/*== Video Usability Information ==*/
	struct
	{

		/* vega_bqb_mpeg: Specify the video_signal_type_present_flag value of vui_parameters()
		 * Default is 0 */
		int bEnableVideoSignalTypePresentFlag;

		/* vega_bqb_mpeg: video_format. Default is 0 */
		int videoFormat;

		/* vega_bqb_mpeg: colour_description_present_flag. Default is 0 */
		int bEnableColorDescriptionPresentFlag;

		/* vega_bqb_mpeg: colour_primaries. Default is 0 */
		int colorPrimaries;

		/* vega_bqb_mpeg: transfer_characteristics. Default is 0 */
		int transferCharacteristics;

		/* vega_bqb_mpeg: matrix_coeffs. Default is 0 */
		int matrixCoeffs;

		/* vega_bqb_mpeg: Default is 0 */
		int bEnableDefaultDisplayWindowFlag;

	} vui;

	/* vega_bqb_mpeg: Specify mpeg general_profile_idc.
	 * 0=Baseline, 1=Main, 2=High, 3=High10, 4=High 4:2:2 10. Default is 1 */
	int profile;

} vega_bqb_mpeg_param;

typedef struct _vegaff_codec_param vega_bqb_hevc_dec_param;
typedef struct _vegaff_codec_param vega_bqb_avc_dec_param;
typedef struct _vegaff_codec_param vega_bqb_mpeg_dec_param;

typedef struct vega_bqb_dmx_param
{
	vegaff_codec_param_fields;

	int pmt_id;
	int pcr_id;
	int vid;
} vega_bqb_dmx_param;

typedef struct vega_bqb_video_info
{
	uint32_t Y_raw_data_size;
	uint32_t Y_invalid_data_size;
	uint32_t C_raw_data_size;
	uint32_t C_invalid_data_size;
	uint32_t data1[33];
	uint32_t video_skip_av_sync;
	uint32_t video_repeat_av_sync;
	uint32_t video_repeat_no_valid_video;
	uint32_t data2[24];
} vega_bqb_video_info;

typedef void *vegaffhandle_t;

/* vega_bqb_hevc_param_alloc:
 *  Allocates an vega_bqb_hevc_param instance. The returned param structure is not
 *  special in any way, but using this method together with vega_bqb_hevc_param_free()
 *  and vega_bqb_hevc_param_parse() to set values by name allows the application to treat
 *  vega_bqb_hevc_param as an opaque data struct for version safety */
VEGA_BQB_API vega_bqb_hevc_param *vega_bqb_hevc_param_alloc(void);
VEGA_BQB_API vega_bqb_avc_param *vega_bqb_avc_param_alloc(void);
VEGA_BQB_API vega_bqb_mpeg_param *vega_bqb_mpeg_param_alloc(void);
VEGA_BQB_API vega_bqb_hevc_dec_param *vega_bqb_hevc_dec_param_alloc(void);
VEGA_BQB_API vega_bqb_avc_dec_param *vega_bqb_avc_dec_param_alloc(void);
VEGA_BQB_API vega_bqb_mpeg_dec_param *vega_bqb_mpeg_dec_param_alloc(void);
VEGA_BQB_API vega_bqb_dmx_param *vega_bqb_dmx_param_alloc(void);

/* vega_bqb_hevc_param_free:
 *  Use vega_bqb_hevc_param_free() to release storage for an vega_bqb_hevc_param instance
 *  allocated by vega_bqb_hevc_param_alloc() */
VEGA_BQB_API void vega_bqb_hevc_param_free(void *param);
VEGA_BQB_API void vega_bqb_avc_param_free(void *param);
VEGA_BQB_API void vega_bqb_mpeg_param_free(void *param);
VEGA_BQB_API void vega_bqb_hevc_dec_param_free(vega_bqb_hevc_dec_param *param);
VEGA_BQB_API void vega_bqb_avc_dec_param_free(vega_bqb_avc_dec_param *param);
VEGA_BQB_API void vega_bqb_mpeg_dec_param_free(vega_bqb_mpeg_dec_param *param);
VEGA_BQB_API void vega_bqb_dmx_param_free(vega_bqb_dmx_param *param);

/* vega_bqb_hevc_param_default:
 *  Initialize a vega_bqb_hevc_param structure to default values */
VEGA_BQB_API void vega_bqb_hevc_param_default(vega_bqb_hevc_param *param);
VEGA_BQB_API void vega_bqb_avc_param_default(vega_bqb_avc_param *param);
VEGA_BQB_API void vega_bqb_hevc_dec_param_default(vega_bqb_hevc_dec_param *param);
VEGA_BQB_API void vega_bqb_avc_dec_param_default(vega_bqb_avc_dec_param *param);

/* vega_bqb_hevc_param_parse:
 *  set one parameter by name.
 *  returns 0 on success, or returns one of the following errors.
 *  note: BAD_VALUE occurs only if it can't even parse the value,
 *  numerical range is not checked until vega_bqb_hevc_encoder_open().
 *  value=NULL means "true" for boolean options, but is a BAD_VALUE for non-booleans. */
#define VEGA_BQB_PARAM_BAD_NAME  (-1)
#define VEGA_BQB_PARAM_BAD_VALUE (-2)
VEGA_BQB_API int vega_bqb_hevc_param_parse(vega_bqb_hevc_param *param, const char *name, const char *value);
VEGA_BQB_API int vega_bqb_avc_param_parse(vega_bqb_avc_param *param, const char *name, const char *value);
VEGA_BQB_API int vega_bqb_hevc_dec_param_parse(vega_bqb_hevc_dec_param *param, const char *name, const char *value);
VEGA_BQB_API int vega_bqb_avc_dec_param_parse(vega_bqb_avc_dec_param *param, const char *name, const char *value);
// VEGA_BQB_API int vega_bqb_vif_param_parse(vega_bqb_vif_param *param, const char *name, const char *value);
VEGA_BQB_API int vega_bqb_mpeg_param_parse(vega_bqb_mpeg_param *param, const char *name, const char *value);

/* vega_bqb_hevc_param_default_bitrate:
 *  Set bitrate parameters to default values */
VEGA_BQB_API void vega_bqb_hevc_param_default_bitrate(vega_bqb_hevc_param *p);
VEGA_BQB_API void vega_bqb_avc_param_default_bitrate(vega_bqb_avc_param *p);
VEGA_BQB_API void vega_bqb_mpeg_param_default_bitrate(vega_bqb_mpeg_param *p);

/* vega_bqb_hevc_param_apply_profile:
 *      Applies the restrictions of the given profile. (one of below) */
static const char *const vega_bqb_hevc_profile_names[] = {"main", "main10", "mainstillpicture", "main422-10", 0};
static const char *const vega_bqb_avc_profile_names[] = {"main", "high", "high10", "high422", "baseline", 0};

/*      (can be NULL, in which case the function will do nothing)
 *      returns 0 on success, negative on failure (e.g. invalid profile name). */

/* vega_bqb_hevc_param_default_preset:
 *      The same as vega_bqb_hevc_param_default, but also use the passed preset and tune
 *      to modify the default settings.
 *      (either can be NULL, which implies no preset or no tune, respectively)
 *
 *      Currently available presets are, ordered from fastest to slowest: */
static const char *const vega_bqb_hevc_preset_names[] = {"fast", "medium", "slow", 0};
static const char *const vega_bqb_avc_preset_names[] = {"fast", "medium", "slow", 0};
static const char *const vega_bqb_mpeg_preset_names[] = {"fast", "medium", "slow", 0};

/*      The presets can also be indexed numerically, as in:
 *      vega_bqb_hevc_param_default_preset( &param, "3", ... )
 *      with fast mapping to "0" and slow mapping to "2".  This mapping may
 *      of course change if new presets are added in between, but will always be
 *      ordered from fastest to slowest.
 *
 *      returns 0 on success, negative on failure (e.g. invalid preset name). */
VEGA_BQB_API int vega_bqb_hevc_param_default_preset(vega_bqb_hevc_param *param, const char *preset);
VEGA_BQB_API int vega_bqb_avc_param_default_preset(vega_bqb_avc_param *param, const char *preset);
// VEGA_BQB_API int vega_bqb_mpeg_param_default_preset(vega_bqb_mpeg_param* param, const char* preset);
VEGA_BQB_API void vega_bqb_mpeg_param_default(vega_bqb_mpeg_param *param);

/* vega_bqb_hevc_picture_alloc:
 *  Allocates an vegaff_picture_t instance. The returned picture structure is not
 *  special in any way, but using this method together with vega_bqb_hevc_picture_free()
 *  and vega_bqb_hevc_picture_init() allows some version safety. */
VEGA_BQB_API vegaff_picture_t *vega_bqb_picture_alloc(void);

/* vega_bqb_hevc_picture_free:
 *  Use vega_bqb_hevc_picture_free() to release storage for an vegaff_picture_t instance
 *  allocated by vega_bqb_hevc_picture_alloc() */
// VEGA_BQB_API void vega_bqb_hevc_picture_free(vegaff_picture_t *pic);
// VEGA_BQB_API void vega_bqb_avc_picture_free(vegaff_picture_t *pic);

/* vega_bqb_hevc_picture_init:
 *       Initialize an vegaff_picture_t structure to default values. It sets the pixel
 *       depth and image format to the encoder's internal values. */
VEGA_BQB_API void vega_bqb_picture_init(vegaff_codec_param *param, vegaff_picture_t *pic);

/* vega_bqb_hevc_encoder_open:
 *      create a new encoder handler, all parameters from vega_bqb_hevc_param are copied */
VEGA_BQB_API vegaffhandle_t vega_bqb_hevc_encoder_open(const vega_bqb_hevc_param *param);
VEGA_BQB_API vegaffhandle_t vega_bqb_hevc_decoder_open(const vega_bqb_hevc_dec_param *param);
VEGA_BQB_API vegaffhandle_t vega_bqb_avc_encoder_open(const vega_bqb_avc_param *param);
VEGA_BQB_API vegaffhandle_t vega_bqb_avc_decoder_open(const vega_bqb_avc_dec_param *param);
VEGA_BQB_API vegaffhandle_t vega_bqb_mpeg_encoder_open(const vega_bqb_mpeg_param *p);

/* vega_bqb_hevc_encoder_headers:
 *      return the SPS and PPS that will be used for the whole stream.
 *      *pi_nal is the number of NAL units outputted in pp_nal.
 *      returns negative on error, total byte size of payload data on success
 *      the payloads of all output NALs are guaranteed to be sequential in memory. */
VEGA_BQB_API int vega_bqb_hevc_encoder_headers(vegaffhandle_t enc, vegaff_nal_t **pp_nal, uint32_t *pi_nal);
VEGA_BQB_API int vega_bqb_avc_encoder_headers(vegaffhandle_t enc, vegaff_nal_t **pp_nal, uint32_t *pi_nal);

/* vega_bqb_hevc_encoder_encode:
 *      encode one picture.
 *      *pi_nal is the number of NAL units outputted in pp_nal.
 *      returns negative on error, 1 if a picture and access unit were output,
 *      or zero if the encoder pipeline is still filling or is empty after flushing.
 *      the payloads of all output NALs are guaranteed to be sequential in memory.
 *      To flush the encoder and retrieve delayed output pictures, pass pic_in as NULL.
 *      Once flushing has begun, all subsequent calls must pass pic_in as NULL. */
VEGA_BQB_API int vega_bqb_hevc_encoder_encode(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal,
											  vegaff_picture_t *pic_in, vegaff_picture_t *pic_out);
VEGA_BQB_API int vega_bqb_hevc_decoder_decode(vegaffhandle_t dec, int *got_frame, vegaff_packet_t *pkt_in,
											  vegaff_picture_t *pic_out);
VEGA_BQB_API int vega_bqb_avc_encoder_encode(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal,
											 vegaff_picture_t *pic_in, vegaff_picture_t *pic_out);
VEGA_BQB_API int vega_bqb_avc_decoder_decode(vegaffhandle_t dec, int *got_frame, vegaff_packet_t *pkt_in,
											 vegaff_picture_t *pic_out);
VEGA_BQB_API int vega_bqb_mpeg_encoder_encode(vegaffhandle_t enc, vegaff_picture_t *pic_in, vegaff_picture_t *pic_out);

/* vega_bqb_hevc_encoder_pushimage:
 *      push one picture into vega_bqb_hevc encoder */
VEGA_BQB_API int vega_bqb_hevc_encoder_pushimage(vegaffhandle_t enc, vegaff_picture_t *pic_in);
VEGA_BQB_API int vega_bqb_avc_encoder_pushimage(vegaffhandle_t enc, vegaff_picture_t *pic_in);
VEGA_BQB_API int vega_bqb_mpeg_encoder_pushimage(vegaffhandle_t enc, vegaff_picture_t *pic_in);

/* vega_bqb_hevc_encoder_getes:
 *      get encoded stream from es queue */
VEGA_BQB_API int vega_bqb_hevc_encoder_getes(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal,
											 vegaff_picture_t *pic_in, vegaff_picture_t *pic_out);
VEGA_BQB_API int vega_bqb_avc_encoder_getes(vegaffhandle_t enc, vegaff_nal_t **pp_nal, int *pi_nal,
											vegaff_picture_t *pic_in, vegaff_picture_t *pic_out);
VEGA_BQB_API int vega_bqb_mpeg_encoder_getes(vegaffhandle_t enc, vegaff_picture_t *pic_in, vegaff_picture_t *pic_out);

/* vega_bqb_hevc_encoder_close:
 *      close an encoder handler */
VEGA_BQB_API void vega_bqb_hevc_encoder_close(vegaffhandle_t enc);
VEGA_BQB_API void vega_bqb_hevc_decoder_close(vegaffhandle_t dec);
VEGA_BQB_API void vega_bqb_avc_encoder_close(vegaffhandle_t enc);
VEGA_BQB_API void vega_bqb_avc_decoder_close(vegaffhandle_t dec);
VEGA_BQB_API void vega_bqb_mpeg_encoder_close(vegaffhandle_t enc);

/* vega_bqb_hevc_encoder_start:
 *      start an encoder handler */
VEGA_BQB_API void vega_bqb_hevc_encoder_start(vegaffhandle_t enc);
VEGA_BQB_API void vega_bqb_avc_encoder_start(vegaffhandle_t enc);

/* vega_bqb_hevc_encoder_stop:
 *      stop an encoder handler */
VEGA_BQB_API void vega_bqb_hevc_encoder_stop(vegaffhandle_t enc);
VEGA_BQB_API void vega_bqb_avc_encoder_stop(vegaffhandle_t enc);

/* vega_bqb_hevc_encoder_reset:
 *      reset an encoder handler */
VEGA_BQB_API void vega_bqb_hevc_encoder_reset(vegaffhandle_t enc);
VEGA_BQB_API void vega_bqb_avc_encoder_reset(vegaffhandle_t enc);

/* vega_bqb_hevc_encoder_setbitrate
 *      change CBR bitrate on the fly */
VEGA_BQB_API int vega_bqb_hevc_encoder_setbitrate(vegaffhandle_t enc, uint32_t bitrate);
VEGA_BQB_API int vega_bqb_avc_encoder_setbitrate(vegaffhandle_t enc, uint32_t bitrate);

/* vega_bqb_hevc_encoder_setvbr
 *      change VBR bitrates on the fly */
VEGA_BQB_API int vega_bqb_hevc_encoder_setvbr(vegaffhandle_t enc, uint32_t max, uint32_t ave, uint32_t min);
VEGA_BQB_API int vega_bqb_avc_encoder_setvbr(vegaffhandle_t enc, uint32_t max, uint32_t ave, uint32_t min);

/* vega_bqb_hevc_encoder_forceidr
 *      force insert IDR randomly at an unspecified picture number */
VEGA_BQB_API int vega_bqb_hevc_encoder_forceidr(vegaffhandle_t enc);
VEGA_BQB_API int vega_bqb_avc_encoder_forceidr(vegaffhandle_t enc);

/* vega_bqb_hevc_encoder_forceidrat
 *      force insert IDR at an unspecified picture number */
VEGA_BQB_API int vega_bqb_hevc_encoder_forceidrat(vegaffhandle_t enc, uint32_t pic_num);
VEGA_BQB_API int vega_bqb_avc_encoder_forceidrat(vegaffhandle_t enc, uint32_t pic_num);

/* vega_bqb_hevc_encoder_setframerate
 *      change frame rate
 *      before calling this function, use vega_bqb_hevc_encoder_stop to flush all queued ES */
VEGA_BQB_API int vega_bqb_hevc_encoder_setframerate(vegaffhandle_t enc, uint32_t fps);
VEGA_BQB_API int vega_bqb_avc_encoder_setframerate(vegaffhandle_t enc, uint32_t fps);

/* vega_bqb_hevc_encoder_setframerateat
 *      change frame rate on the fly */
VEGA_BQB_API int vega_bqb_hevc_encoder_setframerateat(vegaffhandle_t enc, uint32_t fps, uint32_t pic_num);

/* vega_bqb_demuxer_open:
 *      create a new demuxer handler, all parameters from vega_bqb_dmx_param are copied */
VEGA_BQB_API vegaffhandle_t vega_bqb_demuxer_open(vega_bqb_dmx_param *dmx_param);

/* vega_bqb_demuxer_close:
 *      close a demuxer handler */
VEGA_BQB_API void vega_bqb_demuxer_close(vegaffhandle_t dmx);

/* vega_bqb_demuxer_read_packet:
 *      get packet from demuxer */
VEGA_BQB_API int vega_bqb_demuxer_read_packet(vegaffhandle_t dmx, void *buffer, int size);

//#define VEGA_BQB_MAJOR_VERSION 1

#ifdef __cplusplus
}
#endif

#endif // VEGA_BQB_H
