clean::
	$(RM) $(CLEANSUFFIXES:%=libavcodec/bsf/%)

OBJS-$(CONFIG_AAC_ADTSTOASC_BSF)          += bsf/aac_adtstoasc.o
OBJS-$(CONFIG_AV1_FRAME_MERGE_BSF)        += bsf/av1_frame_merge.o
OBJS-$(CONFIG_AV1_FRAME_SPLIT_BSF)        += bsf/av1_frame_split.o
OBJS-$(CONFIG_AV1_METADATA_BSF)           += bsf/av1_metadata.o
OBJS-$(CONFIG_CHOMP_BSF)                  += bsf/chomp.o
OBJS-$(CONFIG_DCA_CORE_BSF)               += bsf/dca_core.o
OBJS-$(CONFIG_DTS2PTS_BSF)                += bsf/dts2pts.o
OBJS-$(CONFIG_DUMP_EXTRADATA_BSF)         += bsf/dump_extradata.o
OBJS-$(CONFIG_DV_ERROR_MARKER_BSF)        += bsf/dv_error_marker.o
OBJS-$(CONFIG_EAC3_CORE_BSF)              += bsf/eac3_core.o
OBJS-$(CONFIG_EVC_FRAME_MERGE_BSF)        += bsf/evc_frame_merge.o
OBJS-$(CONFIG_EXTRACT_EXTRADATA_BSF)      += bsf/extract_extradata.o
OBJS-$(CONFIG_FILTER_UNITS_BSF)           += bsf/filter_units.o
OBJS-$(CONFIG_H264_METADATA_BSF)          += bsf/h264_metadata.o
OBJS-$(CONFIG_H264_MP4TOANNEXB_BSF)       += bsf/h264_mp4toannexb.o
OBJS-$(CONFIG_H264_REDUNDANT_PPS_BSF)     += bsf/h264_redundant_pps.o
OBJS-$(CONFIG_HAPQA_EXTRACT_BSF)          += bsf/hapqa_extract.o
OBJS-$(CONFIG_HEVC_METADATA_BSF)          += bsf/h265_metadata.o
OBJS-$(CONFIG_DOVI_RPU_BSF)               += bsf/dovi_rpu.o
OBJS-$(CONFIG_HEVC_MP4TOANNEXB_BSF)       += bsf/hevc_mp4toannexb.o
OBJS-$(CONFIG_IMX_DUMP_HEADER_BSF)        += bsf/imx_dump_header.o
OBJS-$(CONFIG_MEDIA100_TO_MJPEGB_BSF)     += bsf/media100_to_mjpegb.o
OBJS-$(CONFIG_MJPEG2JPEG_BSF)             += bsf/mjpeg2jpeg.o
OBJS-$(CONFIG_MJPEGA_DUMP_HEADER_BSF)     += bsf/mjpega_dump_header.o
OBJS-$(CONFIG_MOV2TEXTSUB_BSF)            += bsf/movsub.o
OBJS-$(CONFIG_MPEG2_METADATA_BSF)         += bsf/mpeg2_metadata.o
OBJS-$(CONFIG_MPEG4_UNPACK_BFRAMES_BSF)   += bsf/mpeg4_unpack_bframes.o
OBJS-$(CONFIG_NOISE_BSF)                  += bsf/noise.o
OBJS-$(CONFIG_NULL_BSF)                   += bsf/null.o
OBJS-$(CONFIG_OPUS_METADATA_BSF)          += bsf/opus_metadata.o
OBJS-$(CONFIG_PCM_RECHUNK_BSF)            += bsf/pcm_rechunk.o
OBJS-$(CONFIG_PGS_FRAME_MERGE_BSF)        += bsf/pgs_frame_merge.o
OBJS-$(CONFIG_PRORES_METADATA_BSF)        += bsf/prores_metadata.o
OBJS-$(CONFIG_REMOVE_EXTRADATA_BSF)       += bsf/remove_extradata.o
OBJS-$(CONFIG_SETTS_BSF)                  += bsf/setts.o
OBJS-$(CONFIG_SHOWINFO_BSF)               += bsf/showinfo.o
OBJS-$(CONFIG_TEXT2MOVSUB_BSF)            += bsf/movsub.o
OBJS-$(CONFIG_TRACE_HEADERS_BSF)          += bsf/trace_headers.o
OBJS-$(CONFIG_TRUEHD_CORE_BSF)            += bsf/truehd_core.o
OBJS-$(CONFIG_VP9_METADATA_BSF)           += bsf/vp9_metadata.o
OBJS-$(CONFIG_VP9_RAW_REORDER_BSF)        += bsf/vp9_raw_reorder.o
OBJS-$(CONFIG_VP9_SUPERFRAME_BSF)         += bsf/vp9_superframe.o
OBJS-$(CONFIG_VP9_SUPERFRAME_SPLIT_BSF)   += bsf/vp9_superframe_split.o
OBJS-$(CONFIG_VVC_METADATA_BSF)           += bsf/h266_metadata.o
OBJS-$(CONFIG_VVC_MP4TOANNEXB_BSF)        += bsf/vvc_mp4toannexb.o

libavcodec/bsf/%.o: CPPFLAGS += -I$(SRC_PATH)/libavcodec/
