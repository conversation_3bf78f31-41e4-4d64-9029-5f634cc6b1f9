OBJS-$(CONFIG_H264CHROMA)             += loongarch/h264chroma_init_loongarch.o
OBJS-$(CONFIG_H264QPEL)               += loongarch/h264qpel_init_loongarch.o
OBJS-$(CONFIG_H264DSP)                += loongarch/h264dsp_init_loongarch.o
OBJS-$(CONFIG_H264PRED)               += loongarch/h264_intrapred_init_loongarch.o
OBJS-$(CONFIG_VP8_DECODER)            += loongarch/vp8dsp_init_loongarch.o
OBJS-$(CONFIG_VP9_DECODER)            += loongarch/vp9dsp_init_loongarch.o
OBJS-$(CONFIG_VC1DSP)                 += loongarch/vc1dsp_init_loongarch.o
OBJS-$(CONFIG_HPELDSP)                += loongarch/hpeldsp_init_loongarch.o
OBJS-$(CONFIG_IDCTDSP)                += loongarch/idctdsp_init_loongarch.o
OBJS-$(CONFIG_VIDEODSP)               += loongarch/videodsp_init.o
OBJS-$(CONFIG_HEVC_DECODER)           += loongarch/hevcdsp_init_loongarch.o
LASX-OBJS-$(CONFIG_H264QPEL)          += loongarch/h264qpel_lasx.o
LASX-OBJS-$(CONFIG_H264DSP)           += loongarch/h264dsp_lasx.o \
                                         loongarch/h264_deblock_lasx.o
LASX-OBJS-$(CONFIG_VC1DSP)            += loongarch/vc1dsp_lasx.o
LASX-OBJS-$(CONFIG_HPELDSP)           += loongarch/hpeldsp_lasx.o
LASX-OBJS-$(CONFIG_IDCTDSP)           += loongarch/simple_idct_lasx.o  \
                                         loongarch/idctdsp_lasx.o
LSX-OBJS-$(CONFIG_VP8_DECODER)        += loongarch/vp8_mc_lsx.o \
                                         loongarch/vp8_lpf_lsx.o
LSX-OBJS-$(CONFIG_VP9_DECODER)        += loongarch/vp9_mc_lsx.o \
                                         loongarch/vp9_intra_lsx.o \
                                         loongarch/vp9_lpf_lsx.o \
                                         loongarch/vp9_idct_lsx.o
LSX-OBJS-$(CONFIG_HEVC_DECODER)       += loongarch/hevcdsp_lsx.o \
                                         loongarch/hevc_idct_lsx.o \
                                         loongarch/hevc_lpf_sao_lsx.o \
                                         loongarch/hevc_mc_bi_lsx.o \
                                         loongarch/hevc_mc_uni_lsx.o \
                                         loongarch/hevc_mc_uniw_lsx.o \
                                         loongarch/hevc_add_res.o \
                                         loongarch/hevc_mc.o \
                                         loongarch/hevc_idct.o
LSX-OBJS-$(CONFIG_H264DSP)            += loongarch/h264idct.o \
                                         loongarch/h264idct_loongarch.o \
                                         loongarch/h264dsp.o
LSX-OBJS-$(CONFIG_H264QPEL)           += loongarch/h264qpel.o \
                                         loongarch/h264qpel_lsx.o
LSX-OBJS-$(CONFIG_H264CHROMA)         += loongarch/h264chroma.o
LSX-OBJS-$(CONFIG_H264PRED)           += loongarch/h264intrapred.o
